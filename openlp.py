import json
import os
import random
import string

import xm<PERSON><PERSON><PERSON><PERSON>
from lxml import etree
from xml_to_dict import XMLtoDict

def test():
    from lxml import etree

    # Sample XML content with embedded HTML
    xml_content = """
    <root>
        <child name="child1">This is a <b>bold</b> statement.</child>
        <child name="child2">This is a <a href="https://example.com">link</a> in a sentence.</child>
    </root>
    """



def run():
    import xml.etree.ElementTree as ET

    songs = []

    dir = "/Users/<USER>/Downloads/songs2/"
    directory = os.fsencode(dir)

    for file in os.listdir(directory):
        filename = os.fsdecode(file)
        if ".DS" in filename:
            continue
        print(f"Process {filename}")
        # Laad het XML-bestand
        tree = ET.parse(f"{dir}/{filename}")
        root = tree.getroot()

        # XML namespace
        namespace = {'ol': 'http://openlyrics.info/namespace/2009/song'}

        # Titel van het nummer ophalen
        title = root.find('ol:properties/ol:titles/ol:title', namespace).text
        number = root.find('ol:properties/ol:titles/ol:title', namespace).text.split(' ')[0].replace("O-", '')
        id = ''.join(random.choice(string.ascii_uppercase + string.ascii_lowercase + string.digits) for _ in range(21))

        # Doorloop elk vers en druk de songtekst in de gewenste structuur af
        lyrics = """"""
        for verse in root.findall('ol:lyrics/ol:verse', namespace):
            verse_name = verse.get('name')

            # Check of het een vers of refrein is en formatteer dienovereenkomstig
            if verse_name.startswith("v"):
                lyrics += f"""Vers {verse_name[1:]}\n"""
            elif verse_name.startswith("c"):
                lyrics += f"""Refrein\n"""

            # Print elke regel in het verse-element met een <br/> tag tussen de regels
            lines = verse.find('ol:lines', namespace)
            for i, line in enumerate(lines.itertext()):
                if i > 0:
                    lyrics += """\n"""
                lyrics += f"""{line}"""
            lyrics += """\n\n"""  # Voeg een lege regel toe na elk vers of refrein

        song = {
            "objectID": id,
            "title": title,
            "collection": "Opwekking",
            "abbreviation": "Opw",
            "liturgyVerses": "",
            "fontSize": "4.0",
            "number": str(number),
            "lyrics": lyrics.lstrip("\n"),
            "lyricstranslate": "",
            "creator": "joost",
            "created_at": "2024-10-30 00:33:09",
            "updated_at": "2024-10-30 00:33:09"
        }
        songs.append(song)

    encoder = json.JSONEncoder()
    json_output = encoder.encode(songs)
    print(json_output)

if __name__ == '__main__':
    run()
