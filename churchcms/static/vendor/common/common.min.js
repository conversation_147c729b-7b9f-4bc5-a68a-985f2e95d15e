function scrollAndFocus(t,e,o,i,s){var n;(n=jQuery)("body").addClass("scrolling"),n(t).closest("#mainNav").get(0)&&n(t).parents(".collapse.show").collapse("hide"),n("html, body").animate({scrollTop:n(e).offset().top-(i||0)},300,function(){n("body").removeClass("scrolling"),setTimeout(function(){n(o).focus()},500),s&&n("html, body").animate({scrollTop:n(e).offset().top-(i||0)})})}if(!function(p){p.extend({browserSelector:function(){var t;t=navigator.userAgent||navigator.vendor||window.opera,(jQuery.browser=jQuery.browser||{}).mobile=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(t)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(t.substr(0,4));function e(t){return-1<i.indexOf(t)}var o="ontouchstart"in window||navigator.msMaxTouchPoints,i=navigator.userAgent.toLowerCase(),s="gecko",n="webkit",a="safari",r="opera",l=document.documentElement,d=[!/opera|webtv/i.test(i)&&/msie\s(\d)/.test(i)?"ie ie"+parseFloat(navigator.appVersion.split("MSIE")[1]):e("firefox/2")?s+" ff2":e("firefox/3.5")?s+" ff3 ff3_5":e("firefox/3")?s+" ff3":e("gecko/")?s:e("opera")?r+(/version\/(\d+)/.test(i)?" "+r+RegExp.jQuery1:/opera(\s|\/)(\d+)/.test(i)?" "+r+RegExp.jQuery2:""):e("konqueror")?"konqueror":e("chrome")?n+" chrome":e("iron")?n+" iron":e("applewebkit/")?n+" "+a+(/version\/(\d+)/.test(i)?" "+a+RegExp.jQuery1:""):e("mozilla/")?s:"",e("j2me")?"mobile":e("iphone")?"iphone":e("ipod")?"ipod":e("mac")||e("darwin")?"mac":e("webtv")?"webtv":e("win")?"win":e("freebsd")?"freebsd":e("x11")||e("linux")?"linux":"","js"];c=d.join(" "),p.browser.mobile&&(c+=" mobile"),o&&(c+=" touch"),l.className+=" "+c,/Edge/.test(navigator.userAgent)&&p("html").removeClass("chrome").addClass("edge"),!window.ActiveXObject&&"ActiveXObject"in window?p("html").removeClass("gecko").addClass("ie ie11"):(p("body").hasClass("dark")&&p("html").addClass("dark"),p("body").hasClass("boxed")&&p("html").addClass("boxed"))}}),p.browserSelector()}(jQuery),/iPad|iPhone|iPod/.test(navigator.platform)&&$(document).ready(function(t){t(".thumb-info").attr("onclick","return true")}),function(t){t('[data-toggle="tooltip"]').tooltip(),t('[data-toggle="popover"]').popover()}(jQuery),jQuery('a[data-toggle="tab"]').on("shown.bs.tab",function(t){$(this).parents(".nav-tabs").find(".active").removeClass("active"),$(this).addClass("active").parent().addClass("active")}),!jQuery("html").hasClass("disable-onload-scroll")&&window.location.hash&&(window.scrollTo(0,0),$(window).on("load",function(){setTimeout(function(){var t=window.location.hash,e=$(window).width()<768?180:90;$(t).get(0)&&($("a[href$='"+window.location.hash+"']").is("[data-hash-offset]")?e=parseInt($("a[href$='"+window.location.hash+"']").first().attr("data-hash-offset")):$("html").is("[data-hash-offset]")&&(e=parseInt($("html").attr("data-hash-offset"))),$("body").addClass("scrolling"),$("html, body").animate({scrollTop:$(t).offset().top-e},600,"easeOutQuad",function(){$("body").removeClass("scrolling")}))},1)})),function(s){s.fn.extend({textRotator:function(i){i=s.extend({fadeSpeed:500,pauseSpeed:100,child:null},i);return this.each(function(){var e=i,o=s(this);if(s(o.children(),o).each(function(){s(this).hide()}),e.child)t=e.child;else var t=s(o).children(":first");s(t).fadeIn(e.fadeSpeed,function(){s(t).delay(e.pauseSpeed).fadeOut(e.fadeSpeed,function(){var t=s(this).next();0==t.length&&(t=s(o).children(":first")),s(o).textRotator({child:t,fadeSpeed:e.fadeSpeed,pauseSpeed:e.pauseSpeed})})})})}})}(jQuery),function(e){var t={$wrapper:e(".notice-top-bar"),$body:e(".body"),init:function(){this.build()},build:function(){var t=this;e(window).on("load",function(){setTimeout(function(){t.$body.css({"margin-top":t.$wrapper.outerHeight(),transition:"ease margin 300ms"}),e("#noticeTopBarContent").textRotator({fadeSpeed:500,pauseSpeed:5e3})},1e3)})}};e(".notice-top-bar").get(0)&&t.init()}(jQuery),function(t){t(".close-theme-switcher-bar").on("click",function(){t(this).closest(".header-top").css({height:0,"min-height":0,overflow:"hidden"})})}(jQuery),function(e){e(".recommend-themes").get(0)&&e("#demos").get(0)&&e(window).on("scroll",function(t){e(window).scrollTop()+1>=e("#demos").offset().top?e(".recommend-themes").addClass("active"):e(".recommend-themes").removeClass("active")})}(jQuery),function(o){o('a[data-toggle="tab"]').on("shown.bs.tab",function(t){var e=o(o(t.target).attr("href"));e.get(0)&&e.find(".owl-carousel").trigger("refresh.owl.carousel")})}(jQuery),jQuery(".image-hotspot").append('<span class="ring"></span>').append('<span class="circle"></span>'),function(e){e("body[data-plugin-page-transition]").get(0)&&(e(window).on("beforeunload",function(){e("body").addClass("page-transition-active")}),window.onpageshow=function(t){t.persisted&&e("html").hasClass("safari")&&window.location.reload()})}(jQuery),function(e){e(".thumb-info-floating-caption").get(0)&&(e(".thumb-info-floating-caption").on("mouseenter",function(){e(".thumb-info-floating-caption-title").get(0)||(e(".body").append('<div class="thumb-info-floating-caption-title">'+e(this).data("title")+"</div>"),e(this).data("type")&&e(".thumb-info-floating-caption-title").append('<div class="thumb-info-floating-caption-type">'+e(this).data("type")+"</div>").css({"padding-bottom":22}),e(this).hasClass("thumb-info-floating-caption-clean")&&e(".thumb-info-floating-caption-title").addClass("bg-transparent"))}).on("mouseout",function(){e(".thumb-info-floating-caption-title").remove()}),e(document).on("mousemove",function(t){e(".thumb-info-floating-caption-title").css({position:"fixed",left:t.clientX-20,top:t.clientY+20})}))}(jQuery),function(o){o("[data-toggle-text-click]").on("click",function(){o(this).text(function(t,e){return e===o(this).attr("data-toggle-text-click")?o(this).attr("data-toggle-text-click-alt"):o(this).attr("data-toggle-text-click")})})}(jQuery),function(t){"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports?t(require("jquery")):t(jQuery)}(function(n){var a=function(t,e){this.$element=n(t),this.options=n.extend({},a.DEFAULTS,this.dataOptions(),e),this.init()};a.DEFAULTS={from:0,to:0,speed:1e3,refreshInterval:100,decimals:0,formatter:function(t,e){return t.toFixed(e.decimals)},onUpdate:null,onComplete:null},a.prototype.init=function(){this.value=this.options.from,this.loops=Math.ceil(this.options.speed/this.options.refreshInterval),this.loopCount=0,this.increment=(this.options.to-this.options.from)/this.loops},a.prototype.dataOptions=function(){var t={from:this.$element.data("from"),to:this.$element.data("to"),speed:this.$element.data("speed"),refreshInterval:this.$element.data("refresh-interval"),decimals:this.$element.data("decimals")},e=Object.keys(t);for(var o in e){var i=e[o];void 0===t[i]&&delete t[i]}return t},a.prototype.update=function(){this.value+=this.increment,this.loopCount++,this.render(),"function"==typeof this.options.onUpdate&&this.options.onUpdate.call(this.$element,this.value),this.loopCount>=this.loops&&(clearInterval(this.interval),this.value=this.options.to,"function"==typeof this.options.onComplete&&this.options.onComplete.call(this.$element,this.value))},a.prototype.render=function(){var t=this.options.formatter.call(this.$element,this.value,this.options);this.$element.text(t)},a.prototype.restart=function(){this.stop(),this.init(),this.start()},a.prototype.start=function(){this.stop(),this.render(),this.interval=setInterval(this.update.bind(this),this.options.refreshInterval)},a.prototype.stop=function(){this.interval&&clearInterval(this.interval)},a.prototype.toggle=function(){this.interval?this.stop():this.start()},n.fn.countTo=function(s){return this.each(function(){var t=n(this),e=t.data("countTo"),o="object"==typeof s?s:{},i="string"==typeof s?s:"start";e&&"object"!=typeof s||(e&&e.stop(),t.data("countTo",e=new a(this,o))),e[i].call(e)})}}),function(H){H.fn.visible=function(t,e,o,i){if(!(this.length<1)){var s=1<this.length?this.eq(0):this,n=null!=i,a=H(n?i:window),r=n?a.position():0,l=s.get(0),c=a.outerWidth(),d=a.outerHeight(),p=(o=o||"both",!0!==e||l.offsetWidth*l.offsetHeight);if("function"==typeof l.getBoundingClientRect){var u=l.getBoundingClientRect(),h=n?0<=u.top-r.top&&u.top<d+r.top:0<=u.top&&u.top<d,f=n?0<u.bottom-r.top&&u.bottom<=d+r.top:0<u.bottom&&u.bottom<=d,m=n?0<=u.left-r.left&&u.left<c+r.left:0<=u.left&&u.left<c,v=n?0<u.right-r.left&&u.right<c+r.left:0<u.right&&u.right<=c,g=t?h||f:h&&f,w=t?m||v:m&&v;if("both"===o)return p&&g&&w;if("vertical"===o)return p&&g;if("horizontal"===o)return p&&w}else{var b=n?0:r,y=b+d,C=a.scrollLeft(),x=C+c,k=s.position(),j=k.top,T=j+s.height(),$=k.left,S=$+s.width(),I=!0===t?T:j,Q=!0===t?j:T,_=!0===t?S:$,z=!0===t?$:S;if("both"===o)return!!p&&Q<=y&&b<=I&&z<=x&&C<=_;if("vertical"===o)return!!p&&Q<=y&&b<=I;if("horizontal"===o)return!!p&&z<=x&&C<=_}}}}(jQuery),function(t){"use strict";"function"==typeof define&&define.amd?define(["jquery"],t):"undefined"!=typeof module&&module.exports?module.exports=t(require("jquery")):t(jQuery)}(function(l){function c(t){return parseFloat(t)||0}function d(t){var e=l(t),i=null,s=[];return e.each(function(){var t=l(this),e=t.offset().top-c(t.css("margin-top")),o=0<s.length?s[s.length-1]:null;null!==o&&Math.floor(Math.abs(i-e))<=1?s[s.length-1]=o.add(t):s.push(t),i=e}),s}function p(t){var e={byRow:!0,property:"height",target:null,remove:!1};return"object"==typeof t?l.extend(e,t):("boolean"==typeof t?e.byRow=t:"remove"===t&&(e.remove=!0),e)}var i=-1,s=-1,u=l.fn.matchHeight=function(t){var e=p(t);if(e.remove){var o=this;return this.css(e.property,""),l.each(u._groups,function(t,e){e.elements=e.elements.not(o)}),this}return this.length<=1&&!e.target||(u._groups.push({elements:this,options:e}),u._apply(this,e)),this};u.version="0.7.2",u._groups=[],u._throttle=80,u._maintainScroll=!1,u._beforeUpdate=null,u._afterUpdate=null,u._rows=d,u._parse=c,u._parseOptions=p,u._apply=function(t,e){var n=p(e),o=l(t),i=[o],s=l(window).scrollTop(),a=l("html").outerHeight(!0),r=o.parents().filter(":hidden");return r.each(function(){var t=l(this);t.data("style-cache",t.attr("style"))}),r.css("display","block"),n.byRow&&!n.target&&(o.each(function(){var t=l(this),e=t.css("display");"inline-block"!==e&&"flex"!==e&&"inline-flex"!==e&&(e="block"),t.data("style-cache",t.attr("style")),t.css({display:e,"padding-top":"0","padding-bottom":"0","margin-top":"0","margin-bottom":"0","border-top-width":"0","border-bottom-width":"0",height:"100px",overflow:"hidden"})}),i=d(o),o.each(function(){var t=l(this);t.attr("style",t.data("style-cache")||"")})),l.each(i,function(t,e){var o=l(e),s=0;if(n.target)s=n.target.outerHeight(!1);else{if(n.byRow&&o.length<=1)return void o.css(n.property,"");o.each(function(){var t=l(this),e=t.attr("style"),o=t.css("display");"inline-block"!==o&&"flex"!==o&&"inline-flex"!==o&&(o="block");var i={display:o};i[n.property]="",t.css(i),t.outerHeight(!1)>s&&(s=t.outerHeight(!1)),e?t.attr("style",e):t.css("display","")})}o.each(function(){var t=l(this),e=0;n.target&&t.is(n.target)||("border-box"!==t.css("box-sizing")&&(e+=c(t.css("border-top-width"))+c(t.css("border-bottom-width")),e+=c(t.css("padding-top"))+c(t.css("padding-bottom"))),t.css(n.property,s-e+"px"))})}),r.each(function(){var t=l(this);t.attr("style",t.data("style-cache")||null)}),u._maintainScroll&&l(window).scrollTop(s/a*l("html").outerHeight(!0)),this},u._applyDataApi=function(){var o={};l("[data-match-height], [data-mh]").each(function(){var t=l(this),e=t.attr("data-mh")||t.attr("data-match-height");o[e]=e in o?o[e].add(t):t}),l.each(o,function(){this.matchHeight(!0)})};function n(t){u._beforeUpdate&&u._beforeUpdate(t,u._groups),l.each(u._groups,function(){u._apply(this.elements,this.options)}),u._afterUpdate&&u._afterUpdate(t,u._groups)}u._update=function(t,e){if(e&&"resize"===e.type){var o=l(window).width();if(o===i)return;i=o}t?-1===s&&(s=setTimeout(function(){n(e),s=-1},u._throttle)):n(e)},l(u._applyDataApi);var t=l.fn.on?"on":"bind";l(window)[t]("load",function(t){u._update(!1,t)}),l(window)[t]("resize orientationchange",function(t){u._update(!0,t)})}),function(t){"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports?module.exports=t(require("jquery")):t(jQuery)}(function(h){var t,f="waitForImages",m=(t=new Image).srcset&&t.sizes;h.waitForImages={hasImageProperties:["backgroundImage","listStyleImage","borderImage","borderCornerImage","cursor"],hasImageAttributes:["srcset"]},h.expr.pseudos["has-src"]=function(t){return h(t).is('img[src][src!=""]')},h.expr.pseudos.uncached=function(t){return!!h(t).is(":has-src")&&!t.complete},h.fn.waitForImages=function(){var n,a,r,e,l=0,c=h.Deferred(),d=this,p=[],o=h.waitForImages.hasImageProperties||[],i=h.waitForImages.hasImageAttributes||[],u=/url\(\s*(['"]?)(.*?)\1\s*\)/g;if(h.isPlainObject(arguments[0])?(e=arguments[0].waitForAll,r=arguments[0].each,a=arguments[0].finished):e=1===arguments.length&&"boolean"===h.type(arguments[0])?arguments[0]:(a=arguments[0],r=arguments[1],arguments[2]),a=a||h.noop,r=r||h.noop,e=!!e,!h.isFunction(a)||!h.isFunction(r))throw new TypeError("An invalid callback was supplied.");return this.each(function(){var t=h(this);e?t.find("*").addBack().each(function(){var s=h(this);s.is("img:has-src")&&!s.is("[srcset]")&&p.push({src:s.attr("src"),element:s[0]}),h.each(o,function(t,e){var o,i=s.css(e);if(!i)return!0;for(;o=u.exec(i);)p.push({src:o[2],element:s[0]})}),h.each(i,function(t,e){if(!s.attr(e))return!0;p.push({src:s.attr("src"),srcset:s.attr("srcset"),element:s[0]})})}):t.find("img:has-src").each(function(){p.push({src:this.src,element:this})})}),n=p.length,(l=0)===n&&(a.call(d),c.resolveWith(d)),h.each(p,function(t,i){var e=new Image,s="load."+f+" error."+f;h(e).one(s,function t(e){var o=[l,n,"load"==e.type];if(l++,r.apply(i.element,o),c.notifyWith(i.element,o),h(this).off(s,t),l==n)return a.call(d[0]),c.resolveWith(d[0]),!1}),m&&i.srcset&&(e.srcset=i.srcset,e.sizes=i.sizes),e.src=i.src}),c.promise()}}),function(c){fontSpy=function(t,e){var o=c("html"),i=c("body");if("string"!=typeof t||""===t)throw"A valid fontName is required. fontName must be a string and must not be an empty string.";var s={font:t,fontClass:t.toLowerCase().replace(/\s/g,""),success:function(){},failure:function(){},testFont:"Courier New",testString:"QW@HhsXJ",glyphs:"",delay:50,timeOut:1e3,callback:c.noop},n=c.extend(s,e),a=c("<span>"+n.testString+n.glyphs+"</span>").css("position","absolute").css("top","-9999px").css("left","-9999px").css("visibility","hidden").css("fontFamily",n.testFont).css("fontSize","250px");i.append(a);var r=a.outerWidth();a.css("fontFamily",n.font+","+n.testFont);var l=function(){var t=a.outerWidth();r!==t?(n.callback(),o.addClass(n.fontClass),n&&n.success&&n.success(),a.remove()):n.timeOut<0?(o.addClass("no-"+n.fontClass),n&&n.failure&&n.failure(),n.callback(new Error("FontSpy timeout")),a.remove()):(setTimeout(l,n.delay),n.timeOut=n.timeOut-n.delay)};l()}}(jQuery),function(u){"use strict";u.fn.pin=function(l){var r=0,c=[],d=!1,p=u(window);l=l||{};function o(){for(var t=0,e=c.length;t<e;t++){var o=c[t];if(l.minWidth&&p.width()<=l.minWidth)o.parent().is(".pin-wrapper")&&o.unwrap(),o.css({width:"",left:"",top:"",position:""}),l.activeClass&&o.removeClass(l.activeClass),d=!0;else{d=!1;var i=l.containerSelector?o.closest(l.containerSelector):u(document.body),s=o.offset(),n=i.offset(),a=o.parent().offset();o.parent().is(".pin-wrapper")||o.wrap("<div class='pin-wrapper'>");var r=u.extend({top:0,bottom:0},l.padding||{});o.data("pin",{pad:r,from:(l.containerSelector?n.top:s.top)-r.top,to:n.top+i.height()-o.outerHeight()-r.bottom,end:n.top+i.height(),parentTop:a.top}),o.css({width:o.outerWidth()}),o.parent().css("height",o.outerHeight())}}}function t(){if(!d){r=p.scrollTop();for(var t=[],e=0,o=c.length;e<o;e++){var i=u(c[e]),s=i.data("pin");if(s){t.push(i);var n=s.from-s.pad.bottom,a=s.to-s.pad.top;n+i.outerHeight()>s.end?i.css("position",""):n<r&&r<a?("fixed"!=i.css("position")&&i.css({left:i.offset().left,top:s.pad.top}).css("position","fixed"),l.activeClass&&i.addClass(l.activeClass)):a<=r?(i.css({left:"",top:a-s.parentTop+s.pad.top}).css("position","absolute"),l.activeClass&&i.addClass(l.activeClass)):(i.css({position:"",top:"",left:""}),l.activeClass&&i.removeClass(l.activeClass))}}c=t}}function i(){o(),t()}return this.each(function(){var t=u(this),e=u(this).data("pin")||{};e&&e.update||(c.push(t),u("img",this).one("load",o),e.update=i,u(this).data("pin",e))}),p.scroll(t),p.resize(function(){o()}),o(),p.on("load",i),this}}(jQuery),function(o){"use strict";var s,n={action:function(){},runOnLoad:!1,duration:500},a=!1,i={init:function(){for(var t=0;t<=arguments.length;t++){var e=arguments[t];switch(typeof e){case"function":n.action=e;break;case"boolean":n.runOnLoad=e;break;case"number":n.duration=e}}return this.each(function(){n.runOnLoad&&n.action(),o(this).resize(function(){i.timedAction.call(this)})})}};i.timedAction=function(t,e){function o(){var t=n.duration;if(a){var e=new Date-s;if((t=n.duration-e)<=0)return clearTimeout(a),a=!1,void n.action()}i(t)}var i=function(t){a=setTimeout(o,t)};s=new Date,"number"==typeof e&&(n.duration=e),"function"==typeof t&&(n.action=t),a||o()},o.fn.afterResize=function(t){return i[t]?i[t].apply(this,Array.prototype.slice.call(arguments,1)):i.init.apply(this,arguments)}}(jQuery),jQuery(document).ready(function(a){var r=2500,l=3800,c=l-3e3,s=50,n=150,d=500,p=d+800,u=600,o=1500;function h(t){var e=v(t);if(t.parents(".word-rotator").hasClass("type")){var o=t.parent(".word-rotator-words");o.addClass("selected").removeClass("waiting"),setTimeout(function(){o.removeClass("selected"),t.removeClass("is-visible").addClass("is-hidden").children("i").removeClass("in").addClass("out")},d),setTimeout(function(){f(e,n)},p)}else if(t.parents(".word-rotator").hasClass("letters")){var i=t.children("i").length>=e.children("i").length;!function t(e,o,i,s){e.removeClass("in").addClass("out");e.is(":last-child")?i&&setTimeout(function(){h(v(o))},r):setTimeout(function(){t(e.next(),o,i,s)},s);if(e.is(":last-child")&&a("html").hasClass("no-csstransitions")){var n=v(o);g(o,n)}}(t.find("i").eq(0),t,i,s),m(e.find("i").eq(0),e,i,s)}else t.parents(".word-rotator").hasClass("clip")?t.parents(".word-rotator-words").animate({width:"2px"},u,function(){g(t,e),f(e)}):t.parents(".word-rotator").hasClass("loading-bar")?(t.parents(".word-rotator-words").removeClass("is-loading"),g(t,e),setTimeout(function(){h(e)},l),setTimeout(function(){t.parents(".word-rotator-words").addClass("is-loading")},c)):(g(t,e),setTimeout(function(){h(e)},r))}function f(t,e){t.parents(".word-rotator").hasClass("type")?(m(t.find("i").eq(0),t,!1,e),t.addClass("is-visible").removeClass("is-hidden")):t.parents(".word-rotator").hasClass("clip")&&t.parents(".word-rotator-words").animate({width:t.outerWidth()+10},u,function(){setTimeout(function(){h(t)},o)})}function m(t,e,o,i){t.addClass("in").removeClass("out"),t.is(":last-child")?(e.parents(".word-rotator").hasClass("type")&&setTimeout(function(){e.parents(".word-rotator-words").addClass("waiting")},200),o||setTimeout(function(){h(e)},r),e.closest(".word-rotator").hasClass("type")||e.closest(".word-rotator-words").animate({width:e.outerWidth()})):setTimeout(function(){m(t.next(),e,o,i)},i)}function v(t){return t.is(":last-child")?t.parent().children().eq(0):t.next()}function g(t,e){if(t.removeClass("is-visible").addClass("is-hidden"),e.removeClass("is-hidden").addClass("is-visible"),!e.closest(".word-rotator").hasClass("clip")){var o=0,i=e.outerWidth()>t.outerWidth()?0:600;(e.closest(".word-rotator").hasClass("loading-bar")||e.closest(".word-rotator").hasClass("slide"))&&(o=3,i=0),setTimeout(function(){e.closest(".word-rotator-words").animate({width:e.outerWidth()+o})},i)}}(function(t){t.each(function(){var t=a(this),e=t.text().split(""),o=t.hasClass("is-visible");for(i in e)0<t.parents(".rotate-2").length&&(e[i]="<em>"+e[i]+"</em>"),e[i]=o?'<i class="in">'+e[i]+"</i>":"<i>"+e[i]+"</i>";var s=e.join("");t.html(s).css("opacity",1)})})(a(".word-rotator.letters").find("b")),function(t){var n=r;t.each(function(){var t=a(this);if(t.hasClass("loading-bar"))n=l,setTimeout(function(){t.find(".word-rotator-words").addClass("is-loading")},c);else if(t.hasClass("clip")){var e=t.find(".word-rotator-words"),o=e.outerWidth()+10;e.css("width",o)}else if(!t.hasClass("type")){var i=t.find(".word-rotator-words b"),s=0;i.each(function(){var t=a(this).outerWidth();s<t&&(s=t)}),t.find(".word-rotator-words").css("width",s)}setTimeout(function(){h(t.find(".is-visible").eq(0))},n)})}(a(".word-rotator"))}),function(e){e.fn.hover3d=function(t){var h=e.extend({selector:null,perspective:1e3,sensitivity:20,invert:!1,shine:!1,hoverInClass:"hover-in",hoverOutClass:"hover-out",hoverClass:"hover-3d"},t);return this.each(function(){var t=e(this),p=t.find(h.selector);currentX=0,currentY=0,h.shine&&p.append('<div class="shine"></div>');var u=e(this).find(".shine");t.css({perspective:h.perspective+"px",transformStyle:"preserve-3d"}),p.css({perspective:h.perspective+"px",transformStyle:"preserve-3d"}),u.css({position:"absolute",top:0,left:0,bottom:0,right:0,transform:"translateZ(1px)","z-index":9}),t.on("mouseenter",function(){return p.addClass(h.hoverInClass+" "+h.hoverClass),currentX=currentY=0,void setTimeout(function(){p.removeClass(h.hoverInClass)},1e3)}),t.on("mousemove",function(t){return e=t,o=p.innerWidth(),i=p.innerHeight(),s=Math.round(e.pageX-p.offset().left),n=Math.round(e.pageY-p.offset().top),a=h.invert?(o/2-s)/h.sensitivity:-(o/2-s)/h.sensitivity,r=h.invert?-(i/2-n)/h.sensitivity:(i/2-n)/h.sensitivity,l=s-o/2,c=n-i/2,(d=180*Math.atan2(c,l)/Math.PI-90)<0&&(d+=360),p.css({perspective:h.perspective+"px",transformStyle:"preserve-3d",transform:"rotateY("+a+"deg) rotateX("+r+"deg)"}),void u.css("background","linear-gradient("+d+"deg, rgba(255,255,255,"+e.offsetY/i*.5+") 0%,rgba(255,255,255,0) 80%)");var e,o,i,s,n,a,r,l,c,d}),t.on("mouseleave",function(){return p.addClass(h.hoverOutClass+" "+h.hoverClass),p.css({perspective:h.perspective+"px",transformStyle:"preserve-3d",transform:"rotateX(0) rotateY(0)"}),void setTimeout(function(){p.removeClass(h.hoverOutClass+" "+h.hoverClass),currentX=currentY=0},1e3)})})}}(jQuery),function(t){t.isFunction(t.fn.hover3d)&&t(function(){t(".hover-effect-3d").each(function(){t(this).hover3d({selector:".thumb-info"})})})}.apply(this,[jQuery]),jQuery("[data-title-border]").get(0)){var $pageHeaderTitleBorder=$('<span class="page-header-title-border"></span>'),$pageHeaderTitle=$("[data-title-border]"),$window=$(window);$pageHeaderTitle.before($pageHeaderTitleBorder);var setPageHeaderTitleBorderWidth=function(){$pageHeaderTitleBorder.width($pageHeaderTitle.width())};$window.afterResize(function(){setPageHeaderTitleBorderWidth()}),setPageHeaderTitleBorderWidth(),$pageHeaderTitleBorder.addClass("visible")}!function(o){var t={$wrapper:o(".footer-reveal"),init:function(){this.build(),this.events()},build:function(){var t=this.$wrapper.outerHeight(!0);o(window).height()-o(".header-body").height()<t?(o("#footer").removeClass("footer-reveal"),o("body").css("padding-bottom",0)):(o("#footer").addClass("footer-reveal"),o("body").css("padding-bottom",t))},events:function(){var t=this,e=o(window);e.on("load",function(){e.afterResize(function(){t.build()})})}};o(".footer-reveal").get(0)&&t.init()}(jQuery);