(()=>{"use strict";var q={};async function k(){let e=null;const t=document.getElementById("otp_webauthn_config");if(t)return e=JSON.parse(t.innerText),Object.freeze(e);throw new Error("otp_webauthn_config element not found")}function E(e){const t=new Uint8Array(e);let n="";for(const s of t)n+=String.fromCharCode(s);return btoa(n).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}function T(e){const t=e.replace(/-/g,"+").replace(/_/g,"/"),n=(4-t.length%4)%4,r=t.padEnd(t.length+n,"="),s=atob(r),p=new ArrayBuffer(s.length),u=new Uint8Array(p);for(let m=0;m<s.length;m++)u[m]=s.charCodeAt(m);return p}function C(){return window?.PublicKeyCredential!==void 0&&typeof window.PublicKeyCredential=="function"}function P(e){const{id:t}=e;return{...e,id:T(t),transports:e.transports}}function D(e){return e==="localhost"||/^([a-z0-9]+(-[a-z0-9]+)*\.)+[a-z]{2,}$/i.test(e)}class c extends Error{constructor({message:t,code:n,cause:r,name:s}){super(t,{cause:r}),this.name=s??r.name,this.code=n}}function x({error:e,options:t}){const{publicKey:n}=t;if(!n)throw Error("options was missing required publicKey property");if(e.name==="AbortError"){if(t.signal instanceof AbortSignal)return new c({message:"Registration ceremony was sent an abort signal",code:"ERROR_CEREMONY_ABORTED",cause:e})}else if(e.name==="ConstraintError"){if(n.authenticatorSelection?.requireResidentKey===!0)return new c({message:"Discoverable credentials were required but no available authenticator supported it",code:"ERROR_AUTHENTICATOR_MISSING_DISCOVERABLE_CREDENTIAL_SUPPORT",cause:e});if(n.authenticatorSelection?.userVerification==="required")return new c({message:"User verification was required but no available authenticator supported it",code:"ERROR_AUTHENTICATOR_MISSING_USER_VERIFICATION_SUPPORT",cause:e})}else{if(e.name==="InvalidStateError")return new c({message:"The authenticator was previously registered",code:"ERROR_AUTHENTICATOR_PREVIOUSLY_REGISTERED",cause:e});if(e.name==="NotAllowedError")return new c({message:e.message,code:"ERROR_PASSTHROUGH_SEE_CAUSE_PROPERTY",cause:e});if(e.name==="NotSupportedError")return n.pubKeyCredParams.filter(s=>s.type==="public-key").length===0?new c({message:'No entry in pubKeyCredParams was of type "public-key"',code:"ERROR_MALFORMED_PUBKEYCREDPARAMS",cause:e}):new c({message:"No available authenticator supported any of the specified pubKeyCredParams algorithms",code:"ERROR_AUTHENTICATOR_NO_SUPPORTED_PUBKEYCREDPARAMS_ALG",cause:e});if(e.name==="SecurityError"){const r=window.location.hostname;if(D(r)){if(n.rp.id!==r)return new c({message:`The RP ID "${n.rp.id}" is invalid for this domain`,code:"ERROR_INVALID_RP_ID",cause:e})}else return new c({message:`${window.location.hostname} is an invalid domain`,code:"ERROR_INVALID_DOMAIN",cause:e})}else if(e.name==="TypeError"){if(n.user.id.byteLength<1||n.user.id.byteLength>64)return new c({message:"User ID was not between 1 and 64 characters",code:"ERROR_INVALID_USER_ID_LENGTH",cause:e})}else if(e.name==="UnknownError")return new c({message:"The authenticator was unable to process the specified options, or could not create a new credential",code:"ERROR_AUTHENTICATOR_GENERAL_ERROR",cause:e})}return e}class K{createNewAbortSignal(){if(this.controller){const n=new Error("Cancelling existing WebAuthn API call for new one");n.name="AbortError",this.controller.abort(n)}const t=new AbortController;return this.controller=t,t.signal}cancelCeremony(){if(this.controller){const t=new Error("Manually cancelling existing WebAuthn API call");t.name="AbortError",this.controller.abort(t),this.controller=void 0}}}const N=new K,F=["cross-platform","platform"];function L(e){if(e&&!(F.indexOf(e)<0))return e}async function M(e){if(!C())throw new Error("WebAuthn is not supported in this browser");const n={publicKey:{...e,challenge:T(e.challenge),user:{...e.user,id:T(e.user.id)},excludeCredentials:e.excludeCredentials?.map(P)}};n.signal=N.createNewAbortSignal();let r;try{r=await navigator.credentials.create(n)}catch(b){throw x({error:b,options:n})}if(!r)throw new Error("Registration was not completed");const{id:s,rawId:p,response:u,type:m}=r;let l;typeof u.getTransports=="function"&&(l=u.getTransports());let _;if(typeof u.getPublicKeyAlgorithm=="function")try{_=u.getPublicKeyAlgorithm()}catch(b){S("getPublicKeyAlgorithm()",b)}let R;if(typeof u.getPublicKey=="function")try{const b=u.getPublicKey();b!==null&&(R=E(b))}catch(b){S("getPublicKey()",b)}let d;if(typeof u.getAuthenticatorData=="function")try{d=E(u.getAuthenticatorData())}catch(b){S("getAuthenticatorData()",b)}return{id:s,rawId:E(p),response:{attestationObject:E(u.attestationObject),clientDataJSON:E(u.clientDataJSON),transports:l,publicKeyAlgorithm:_,publicKey:R,authenticatorData:d},type:m,clientExtensionResults:r.getClientExtensionResults(),authenticatorAttachment:L(r.authenticatorAttachment)}}function S(e,t){console.warn(`The browser extension that intercepted this WebAuthn API call incorrectly implemented ${e}. You should report this error to them.
`,t)}function V(){if(!C())return new Promise(t=>t(!1));const e=window.PublicKeyCredential;return e.isConditionalMediationAvailable===void 0?new Promise(t=>t(!1)):e.isConditionalMediationAvailable()}function B({error:e,options:t}){const{publicKey:n}=t;if(!n)throw Error("options was missing required publicKey property");if(e.name==="AbortError"){if(t.signal instanceof AbortSignal)return new c({message:"Authentication ceremony was sent an abort signal",code:"ERROR_CEREMONY_ABORTED",cause:e})}else{if(e.name==="NotAllowedError")return new c({message:e.message,code:"ERROR_PASSTHROUGH_SEE_CAUSE_PROPERTY",cause:e});if(e.name==="SecurityError"){const r=window.location.hostname;if(D(r)){if(n.rpId!==r)return new c({message:`The RP ID "${n.rpId}" is invalid for this domain`,code:"ERROR_INVALID_RP_ID",cause:e})}else return new c({message:`${window.location.hostname} is an invalid domain`,code:"ERROR_INVALID_DOMAIN",cause:e})}else if(e.name==="UnknownError")return new c({message:"The authenticator was unable to process the specified options, or could not create a new assertion signature",code:"ERROR_AUTHENTICATOR_GENERAL_ERROR",cause:e})}return e}async function U(e,t=!1){if(!C())throw new Error("WebAuthn is not supported in this browser");let n;e.allowCredentials?.length!==0&&(n=e.allowCredentials?.map(P));const r={...e,challenge:T(e.challenge),allowCredentials:n},s={};if(t){if(!await V())throw Error("Browser does not support WebAuthn autofill");if(document.querySelectorAll("input[autocomplete$='webauthn']").length<1)throw Error('No <input> with "webauthn" as the only or last value in its `autocomplete` attribute was detected');s.mediation="conditional",r.allowCredentials=[]}s.publicKey=r,s.signal=N.createNewAbortSignal();let p;try{p=await navigator.credentials.get(s)}catch(d){throw B({error:d,options:s})}if(!p)throw new Error("Authentication was not completed");const{id:u,rawId:m,response:l,type:_}=p;let R;return l.userHandle&&(R=E(l.userHandle)),{id:u,rawId:E(m),response:{authenticatorData:E(l.authenticatorData),clientDataJSON:E(l.clientDataJSON),signature:E(l.signature),userHandle:R},type:_,clientExtensionResults:p.getClientExtensionResults(),authenticatorAttachment:L(p.authenticatorAttachment)}}function H(){return C()?PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable():new Promise(e=>e(!1))}(async function(){const e="passkey-verification-button",t="passkey-verification-status-message",n="visible",r="passkey-verification-placeholder",s="passkey-verification-unavailable-template",p="passkey-verification-available-template",u="otp_webauthn.verification_start",m="otp_webauthn.verification_complete",l="otp_webauthn.verification_failed";async function _(o){if(!o.autocompleteLoginFieldSelector)return;const i=document.querySelector(o.autocompleteLoginFieldSelector);if(!i){console.error(`Could not find login field with selector ${o.autocompleteLoginFieldSelector}. WebAuthn autofill cannot continue.`);return}const a=i.getAttribute("autocomplete")||"";i.setAttribute("autocomplete",a+" webauthn"),i.dispatchEvent(new CustomEvent(u,{detail:{fromAutofill:!0},bubbles:!0}));const y=await fetch(o.beginAuthenticationUrl,{method:"POST",credentials:"same-origin",headers:{"X-CSRFToken":o.csrfToken,Accept:"application/json"}});if(!y.ok){console.error("Unable to fetch options from server. Will not attempt autofill."),i.dispatchEvent(new CustomEvent(l,{detail:{fromAutofill:!0,response:y},bubbles:!0}));return}let w;try{w=await U(await y.json(),!0)}catch(h){console.error("Got error during the webauthn credential autofill call",h),i.dispatchEvent(new CustomEvent(l,{detail:{fromAutofill:!0,error:h},bubbles:!0}));return}const I=document.querySelector("input[name='next']");let v=o.completeAuthenticationUrl;I&&I.value&&(v+=`?next=${encodeURIComponent(I.value)}`);const A=await fetch(v,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":o.csrfToken},credentials:"same-origin",body:JSON.stringify(w)});if(!A.headers.get("content-type")?.includes("application/json")){i.dispatchEvent(new CustomEvent(l,{detail:{fromAutofill:!0,response:A},bubbles:!0})),alert(gettext("Verification failed. A server error occurred."));return}const f=await A.json();if(!A.ok&&"detail"in f){i.dispatchEvent(new CustomEvent(l,{detail:{fromAutofill:!0,response:A},bubbles:!0})),alert(f.detail);return}if(f&&f.id)i.dispatchEvent(new CustomEvent(m,{detail:{fromAutofill:!0,response:A},bubbles:!0})),f.redirect_url&&(window.location.href=f.redirect_url);else{i.dispatchEvent(new CustomEvent(l,{detail:{fromAutofill:!0,response:A},bubbles:!0}));const h=f.error||gettext("An error occurred during verification.");alert(h)}}async function R(o){const i=document.getElementById(e);if(!i)return;const a=i.textContent||gettext("Verify with Passkey");i.addEventListener("click",async y=>{i.dispatchEvent(new CustomEvent(u,{detail:{fromAutofill:!1},bubbles:!0})),await d({buttonDisabled:!0,buttonLabel:gettext("Verifying...")});const w=await fetch(o.beginAuthenticationUrl,{method:"POST",credentials:"same-origin",headers:{"X-CSRFToken":o.csrfToken,Accept:"application/json"}});if(!w.ok){await d({buttonDisabled:!1,buttonLabel:a,requestFocus:!0,status:gettext("Verification failed. Could not retrieve parameters from the server.")}),i.dispatchEvent(new CustomEvent(l,{detail:{fromAutofill:!1,response:w},bubbles:!0}));return}let I;try{I=await U(await w.json())}catch(g){if(g instanceof Error||g instanceof c){switch(console.error(g),g.name){case"AbortError":await d({buttonDisabled:!1,buttonLabel:a,requestFocus:!0,status:gettext("Verification aborted.")});break;case"NotAllowedError":await d({buttonDisabled:!1,buttonLabel:a,requestFocus:!0,status:gettext("Verification canceled or not allowed.")});break;default:throw await d({buttonDisabled:!1,buttonLabel:a,requestFocus:!0,status:gettext("Verification failed. An unknown error occurred.")}),g}i.dispatchEvent(new CustomEvent(l,{detail:{fromAutofill:!1,error:g},bubbles:!0}));return}}await d({buttonDisabled:!0,buttonLabel:gettext("Finishing verification...")});const v=document.querySelector("input[name='next']");let A=o.completeAuthenticationUrl;v&&v.value&&(A+=`?next=${encodeURIComponent(v.value)}`);const f=await fetch(A,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":o.csrfToken},credentials:"same-origin",body:JSON.stringify(I)});if(!f.headers.get("content-type")?.includes("application/json")){await d({buttonDisabled:!1,buttonLabel:a,requestFocus:!0,status:gettext("Verification failed. An unknown server error occurred.")}),i.dispatchEvent(new CustomEvent(l,{detail:{fromAutofill:!1,response:f},bubbles:!0}));return}const h=await f.json();if(!f.ok){const g=h.detail||gettext("Verification failed. An unknown error occurred.");await d({buttonDisabled:!1,buttonLabel:a,requestFocus:!0,status:g}),i.dispatchEvent(new CustomEvent(l,{detail:{fromAutofill:!1,response:f},bubbles:!0}));return}if(h&&h.id)await d({buttonDisabled:!1,buttonLabel:a,status:gettext("Verification successful!")}),i.dispatchEvent(new CustomEvent(m,{detail:{fromAutofill:!1,response:f,id:h.id},bubbles:!0})),h.redirect_url&&(window.location.href=h.redirect_url);else{const g=h.error||gettext("An error occurred during verification.");await d({buttonDisabled:!1,buttonLabel:a,requestFocus:!0,status:g})}})}async function d(o){const i=document.getElementById(e);if(!i)return;const a=document.getElementById(t);i.disabled=o.buttonDisabled,i.textContent=o.buttonLabel,a&&(o.status?(i.setAttribute("aria-describedby",t),a.classList.add(n),a.textContent=o.status,a.setAttribute("aria-live","assertive"),o.requestFocus&&i.focus()):(i.removeAttribute("aria-describedby"),a.removeAttribute("aria-live"),a.classList.remove(n)))}async function b(o){const i=document.getElementById(r),a=document.getElementById(p),y=document.getElementById(s);if(!i)throw new Error("Placeholder element not found");if(!a)throw new Error("Available template not found");if(o){const w=a.content.cloneNode(!0);i.replaceWith(w)}else if(y){const w=y.content.cloneNode(!0);i.replaceWith(w)}else i.remove()}const O=await k();O.autocompleteLoginFieldSelector&&await V()&&_(O),C()?(await b(!0),R(O)):await b(!1)})()})();

//# sourceMappingURL=otp_webauthn_auth.js.map