(()=>{"use strict";var B={};async function O(){let e=null;const t=document.getElementById("otp_webauthn_config");if(t)return e=JSON.parse(t.innerText),Object.freeze(e);throw new Error("otp_webauthn_config element not found")}function f(e){const t=new Uint8Array(e);let n="";for(const a of t)n+=String.fromCharCode(a);return btoa(n).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}function I(e){const t=e.replace(/-/g,"+").replace(/_/g,"/"),n=(4-t.length%4)%4,r=t.padEnd(t.length+n,"="),a=atob(r),b=new ArrayBuffer(a.length),o=new Uint8Array(b);for(let E=0;E<a.length;E++)o[E]=a.charCodeAt(E);return b}function _(){return window?.PublicKeyCredential!==void 0&&typeof window.PublicKeyCredential=="function"}function C(e){const{id:t}=e;return{...e,id:I(t),transports:e.transports}}function P(e){return e==="localhost"||/^([a-z0-9]+(-[a-z0-9]+)*\.)+[a-z]{2,}$/i.test(e)}class s extends Error{constructor({message:t,code:n,cause:r,name:a}){super(t,{cause:r}),this.name=a??r.name,this.code=n}}function N({error:e,options:t}){const{publicKey:n}=t;if(!n)throw Error("options was missing required publicKey property");if(e.name==="AbortError"){if(t.signal instanceof AbortSignal)return new s({message:"Registration ceremony was sent an abort signal",code:"ERROR_CEREMONY_ABORTED",cause:e})}else if(e.name==="ConstraintError"){if(n.authenticatorSelection?.requireResidentKey===!0)return new s({message:"Discoverable credentials were required but no available authenticator supported it",code:"ERROR_AUTHENTICATOR_MISSING_DISCOVERABLE_CREDENTIAL_SUPPORT",cause:e});if(n.authenticatorSelection?.userVerification==="required")return new s({message:"User verification was required but no available authenticator supported it",code:"ERROR_AUTHENTICATOR_MISSING_USER_VERIFICATION_SUPPORT",cause:e})}else{if(e.name==="InvalidStateError")return new s({message:"The authenticator was previously registered",code:"ERROR_AUTHENTICATOR_PREVIOUSLY_REGISTERED",cause:e});if(e.name==="NotAllowedError")return new s({message:e.message,code:"ERROR_PASSTHROUGH_SEE_CAUSE_PROPERTY",cause:e});if(e.name==="NotSupportedError")return n.pubKeyCredParams.filter(a=>a.type==="public-key").length===0?new s({message:'No entry in pubKeyCredParams was of type "public-key"',code:"ERROR_MALFORMED_PUBKEYCREDPARAMS",cause:e}):new s({message:"No available authenticator supported any of the specified pubKeyCredParams algorithms",code:"ERROR_AUTHENTICATOR_NO_SUPPORTED_PUBKEYCREDPARAMS_ALG",cause:e});if(e.name==="SecurityError"){const r=window.location.hostname;if(P(r)){if(n.rp.id!==r)return new s({message:`The RP ID "${n.rp.id}" is invalid for this domain`,code:"ERROR_INVALID_RP_ID",cause:e})}else return new s({message:`${window.location.hostname} is an invalid domain`,code:"ERROR_INVALID_DOMAIN",cause:e})}else if(e.name==="TypeError"){if(n.user.id.byteLength<1||n.user.id.byteLength>64)return new s({message:"User ID was not between 1 and 64 characters",code:"ERROR_INVALID_USER_ID_LENGTH",cause:e})}else if(e.name==="UnknownError")return new s({message:"The authenticator was unable to process the specified options, or could not create a new credential",code:"ERROR_AUTHENTICATOR_GENERAL_ERROR",cause:e})}return e}class L{createNewAbortSignal(){if(this.controller){const n=new Error("Cancelling existing WebAuthn API call for new one");n.name="AbortError",this.controller.abort(n)}const t=new AbortController;return this.controller=t,t.signal}cancelCeremony(){if(this.controller){const t=new Error("Manually cancelling existing WebAuthn API call");t.name="AbortError",this.controller.abort(t),this.controller=void 0}}}const v=new L,k=["cross-platform","platform"];function D(e){if(e&&!(k.indexOf(e)<0))return e}async function K(e){if(!_())throw new Error("WebAuthn is not supported in this browser");const n={publicKey:{...e,challenge:I(e.challenge),user:{...e.user,id:I(e.user.id)},excludeCredentials:e.excludeCredentials?.map(C)}};n.signal=v.createNewAbortSignal();let r;try{r=await navigator.credentials.create(n)}catch(i){throw N({error:i,options:n})}if(!r)throw new Error("Registration was not completed");const{id:a,rawId:b,response:o,type:E}=r;let u;typeof o.getTransports=="function"&&(u=o.getTransports());let A;if(typeof o.getPublicKeyAlgorithm=="function")try{A=o.getPublicKeyAlgorithm()}catch(i){S("getPublicKeyAlgorithm()",i)}let R;if(typeof o.getPublicKey=="function")try{const i=o.getPublicKey();i!==null&&(R=f(i))}catch(i){S("getPublicKey()",i)}let m;if(typeof o.getAuthenticatorData=="function")try{m=f(o.getAuthenticatorData())}catch(i){S("getAuthenticatorData()",i)}return{id:a,rawId:f(b),response:{attestationObject:f(o.attestationObject),clientDataJSON:f(o.clientDataJSON),transports:u,publicKeyAlgorithm:A,publicKey:R,authenticatorData:m},type:E,clientExtensionResults:r.getClientExtensionResults(),authenticatorAttachment:D(r.authenticatorAttachment)}}function S(e,t){console.warn(`The browser extension that intercepted this WebAuthn API call incorrectly implemented ${e}. You should report this error to them.
`,t)}function U(){if(!_())return new Promise(t=>t(!1));const e=window.PublicKeyCredential;return e.isConditionalMediationAvailable===void 0?new Promise(t=>t(!1)):e.isConditionalMediationAvailable()}function x({error:e,options:t}){const{publicKey:n}=t;if(!n)throw Error("options was missing required publicKey property");if(e.name==="AbortError"){if(t.signal instanceof AbortSignal)return new s({message:"Authentication ceremony was sent an abort signal",code:"ERROR_CEREMONY_ABORTED",cause:e})}else{if(e.name==="NotAllowedError")return new s({message:e.message,code:"ERROR_PASSTHROUGH_SEE_CAUSE_PROPERTY",cause:e});if(e.name==="SecurityError"){const r=window.location.hostname;if(P(r)){if(n.rpId!==r)return new s({message:`The RP ID "${n.rpId}" is invalid for this domain`,code:"ERROR_INVALID_RP_ID",cause:e})}else return new s({message:`${window.location.hostname} is an invalid domain`,code:"ERROR_INVALID_DOMAIN",cause:e})}else if(e.name==="UnknownError")return new s({message:"The authenticator was unable to process the specified options, or could not create a new assertion signature",code:"ERROR_AUTHENTICATOR_GENERAL_ERROR",cause:e})}return e}async function G(e,t=!1){if(!_())throw new Error("WebAuthn is not supported in this browser");let n;e.allowCredentials?.length!==0&&(n=e.allowCredentials?.map(C));const r={...e,challenge:I(e.challenge),allowCredentials:n},a={};if(t){if(!await U())throw Error("Browser does not support WebAuthn autofill");if(document.querySelectorAll("input[autocomplete$='webauthn']").length<1)throw Error('No <input> with "webauthn" as the only or last value in its `autocomplete` attribute was detected');a.mediation="conditional",r.allowCredentials=[]}a.publicKey=r,a.signal=v.createNewAbortSignal();let b;try{b=await navigator.credentials.get(a)}catch(m){throw x({error:m,options:a})}if(!b)throw new Error("Authentication was not completed");const{id:o,rawId:E,response:u,type:A}=b;let R;return u.userHandle&&(R=f(u.userHandle)),{id:o,rawId:f(E),response:{authenticatorData:f(u.authenticatorData),clientDataJSON:f(u.clientDataJSON),signature:f(u.signature),userHandle:R},type:A,clientExtensionResults:b.getClientExtensionResults(),authenticatorAttachment:D(b.authenticatorAttachment)}}function q(){return _()?PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable():new Promise(e=>e(!1))}(async function(){const e="passkey-register-button",t="passkey-register-status-message",n="visible",r="passkey-registration-placeholder",a="passkey-registration-unavailable-template",b="passkey-registration-available-template",o="otp_webauthn.register_start",E="otp_webauthn.register_complete",u="otp_webauthn.register_failed";async function A(i){const l=document.getElementById(e);if(!l)return;const d=l.textContent||gettext("Register a Passkey");l.addEventListener("click",async g=>{l.dispatchEvent(new CustomEvent(o,{bubbles:!0})),await c({buttonDisabled:!0,buttonLabel:gettext("Registering...")});const h=await fetch(i.beginRegistrationUrl,{method:"POST",credentials:"same-origin",headers:{"X-CSRFToken":i.csrfToken,Accept:"application/json"}});if(!h.ok){c({buttonDisabled:!1,buttonLabel:d,requestFocus:!0,status:gettext("Registration failed. Unable to fetch registration options from server.")}),l.dispatchEvent(new CustomEvent(u,{detail:{response:h},bubbles:!0}));return}let p;try{p=await K(await h.json())}catch(w){if(console.error(w),w instanceof Error||w instanceof s){switch(w.name){case"AbortError":c({buttonDisabled:!1,buttonLabel:d,status:gettext("Registration aborted."),requestFocus:!0});break;case"InvalidStateError":c({buttonDisabled:!1,buttonLabel:d,status:gettext("Registration failed. You most likely already have a Passkey registered for this site."),requestFocus:!0});break;case"NotAllowedError":c({buttonDisabled:!1,buttonLabel:d,status:gettext("Registration aborted or not allowed."),requestFocus:!0});break;case"SecurityError":c({buttonDisabled:!1,buttonLabel:d,status:gettext("Registration failed. A technical problem occurred that prevents you from registering a Passkey for this site."),requestFocus:!0});break;default:throw c({buttonDisabled:!1,buttonLabel:d,status:gettext("Registration failed. An unknown error occurred."),requestFocus:!0}),w}l.dispatchEvent(new CustomEvent(u,{detail:{error:w},bubbles:!0}));return}}c({buttonDisabled:!0,buttonLabel:gettext("Finishing registration...")});const y=await fetch(i.completeRegistrationUrl,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":i.csrfToken},credentials:"same-origin",body:JSON.stringify(p)});if(!y.ok){c({buttonDisabled:!1,buttonLabel:d,status:gettext("Registration failed. The server was unable to verify this passkey."),requestFocus:!0}),l.dispatchEvent(new CustomEvent(u,{detail:{response:y},bubbles:!0}));return}if(!y.headers.get("content-type")?.includes("application/json")){c({buttonDisabled:!1,buttonLabel:d,status:gettext("Registration failed. A server error occurred."),requestFocus:!0}),l.dispatchEvent(new CustomEvent(u,{detail:{response:y},bubbles:!0}));return}const T=await y.json();if(T&&T.id)c({buttonDisabled:!1,buttonLabel:d,status:gettext("Registration successful!"),requestFocus:!0}),l.dispatchEvent(new CustomEvent(E,{detail:{response:T,id:T.id},bubbles:!0}));else{const w=T.error||gettext("An error occurred during registration.");c({buttonDisabled:!1,buttonLabel:d,status:w,requestFocus:!0}),l.dispatchEvent(new CustomEvent(u,{detail:{response:y},bubbles:!0}))}});async function c(g){const h=document.getElementById(e);if(!h)return;const p=document.getElementById(t);h.disabled=g.buttonDisabled,h.textContent=g.buttonLabel,p&&(g.status?(h.setAttribute("aria-describedby",t),p.classList.add(n),p.textContent=g.status,p.setAttribute("aria-live","assertive"),g.requestFocus&&h.focus()):(h.removeAttribute("aria-describedby"),p.removeAttribute("aria-live"),p.classList.remove(n)))}}async function R(i){const l=document.getElementById(r),d=document.getElementById(b),c=document.getElementById(a);if(!l)throw new Error("Placeholder element not found");if(!d)throw new Error("Available template not found");if(i){const g=d.content.cloneNode(!0);l.replaceWith(g)}else if(c){const g=c.content.cloneNode(!0);l.replaceWith(g)}else l.remove()}const m=await O();if(_())await R(!0),await A(m);else{await R(!1);return}})()})();

//# sourceMappingURL=otp_webauthn_register.js.map