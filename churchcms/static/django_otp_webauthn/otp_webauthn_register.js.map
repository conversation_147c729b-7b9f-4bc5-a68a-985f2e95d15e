{"version": 3, "file": "otp_webauthn_register.js", "mappings": "4BAMO,eAAeA,GAAS,CAC7B,IAAIC,EAAwB,KAC5B,MAAMC,EAAgB,SAAS,eAC7B,qBAAqB,EAGvB,GAAIA,EACF,OAAAD,EAAS,KAAK,MAAMC,EAAc,SAAS,EACpC,OAAO,OAAOD,CAAM,EAE7B,MAAM,IAAI,MAAM,uCAAuC,CACzD,CChBA,SAASE,EAAwBC,EAAQ,CACrC,MAAMC,EAAQ,IAAI,WAAWD,CAAM,EACnC,IAAIE,EAAM,GACV,UAAWC,KAAYF,EACnBC,GAAO,OAAO,aAAaC,CAAQ,EAGvC,OADqB,KAAKD,CAAG,EACT,QAAQ,MAAO,GAAG,EAAE,QAAQ,MAAO,GAAG,EAAE,QAAQ,KAAM,EAAE,CAChF,CAEA,SAASE,EAAwBC,EAAiB,CAC9C,MAAMC,EAASD,EAAgB,QAAQ,KAAM,GAAG,EAAE,QAAQ,KAAM,GAAG,EAC7DE,GAAa,EAAKD,EAAO,OAAS,GAAM,EACxCE,EAASF,EAAO,OAAOA,EAAO,OAASC,EAAW,GAAG,EACrDE,EAAS,KAAKD,CAAM,EACpBR,EAAS,IAAI,YAAYS,EAAO,MAAM,EACtCR,EAAQ,IAAI,WAAWD,CAAM,EACnC,QAASU,EAAI,EAAGA,EAAID,EAAO,OAAQC,IAC/BT,EAAMS,CAAC,EAAID,EAAO,WAAWC,CAAC,EAElC,OAAOV,CACX,CAEA,SAASW,GAA0B,CAC/B,OAAQ,QAAQ,sBAAwB,QACpC,OAAO,OAAO,qBAAwB,UAC9C,CAEA,SAASC,EAAgCC,EAAY,CACjD,KAAM,CAAE,GAAAC,CAAG,EAAID,EACf,MAAO,CACH,GAAGA,EACH,GAAIT,EAAwBU,CAAE,EAC9B,WAAYD,EAAW,UAC3B,CACJ,CAEA,SAASE,EAAcC,EAAU,CAC7B,OAAQA,IAAa,aACjB,0CAA0C,KAAKA,CAAQ,CAC/D,CAEA,MAAMC,UAAsB,KAAM,CAC9B,YAAY,CAAE,QAAAC,EAAS,KAAAC,EAAM,MAAAC,EAAO,KAAAC,CAAM,EAAG,CACzC,MAAMH,EAAS,CAAE,MAAAE,CAAM,CAAC,EACxB,KAAK,KAAOC,GAAQD,EAAM,KAC1B,KAAK,KAAOD,CAChB,CACJ,CAEA,SAASG,EAA0B,CAAE,MAAAC,EAAO,QAAAC,CAAS,EAAG,CACpD,KAAM,CAAE,UAAAC,CAAU,EAAID,EACtB,GAAI,CAACC,EACD,MAAM,MAAM,iDAAiD,EAEjE,GAAIF,EAAM,OAAS,cACf,GAAIC,EAAQ,kBAAkB,YAC1B,OAAO,IAAIP,EAAc,CACrB,QAAS,iDACT,KAAM,yBACN,MAAOM,CACX,CAAC,UAGAA,EAAM,OAAS,kBAAmB,CACvC,GAAIE,EAAU,wBAAwB,qBAAuB,GACzD,OAAO,IAAIR,EAAc,CACrB,QAAS,qFACT,KAAM,8DACN,MAAOM,CACX,CAAC,EAEA,GAAIE,EAAU,wBAAwB,mBAAqB,WAC5D,OAAO,IAAIR,EAAc,CACrB,QAAS,6EACT,KAAM,wDACN,MAAOM,CACX,CAAC,CAET,KACK,IAAIA,EAAM,OAAS,oBACpB,OAAO,IAAIN,EAAc,CACrB,QAAS,8CACT,KAAM,4CACN,MAAOM,CACX,CAAC,EAEA,GAAIA,EAAM,OAAS,kBACpB,OAAO,IAAIN,EAAc,CACrB,QAASM,EAAM,QACf,KAAM,uCACN,MAAOA,CACX,CAAC,EAEA,GAAIA,EAAM,OAAS,oBAEpB,OAD8BE,EAAU,iBAAiB,OAAQC,GAAUA,EAAM,OAAS,YAAY,EAC5E,SAAW,EAC1B,IAAIT,EAAc,CACrB,QAAS,wDACT,KAAM,mCACN,MAAOM,CACX,CAAC,EAEE,IAAIN,EAAc,CACrB,QAAS,wFACT,KAAM,wDACN,MAAOM,CACX,CAAC,EAEA,GAAIA,EAAM,OAAS,gBAAiB,CACrC,MAAMI,EAAkB,OAAO,SAAS,SACxC,GAAKZ,EAAcY,CAAe,GAO7B,GAAIF,EAAU,GAAG,KAAOE,EACzB,OAAO,IAAIV,EAAc,CACrB,QAAS,cAAcQ,EAAU,GAAG,EAAE,+BACtC,KAAM,sBACN,MAAOF,CACX,CAAC,MAXD,QAAO,IAAIN,EAAc,CACrB,QAAS,GAAG,OAAO,SAAS,QAAQ,wBACpC,KAAM,uBACN,MAAOM,CACX,CAAC,CAST,SACSA,EAAM,OAAS,aACpB,GAAIE,EAAU,KAAK,GAAG,WAAa,GAAKA,EAAU,KAAK,GAAG,WAAa,GACnE,OAAO,IAAIR,EAAc,CACrB,QAAS,8CACT,KAAM,+BACN,MAAOM,CACX,CAAC,UAGAA,EAAM,OAAS,eACpB,OAAO,IAAIN,EAAc,CACrB,QAAS,sGACT,KAAM,oCACN,MAAOM,CACX,CAAC,EAEL,OAAOA,CACX,CAEA,MAAMK,CAAyB,CAC3B,sBAAuB,CACnB,GAAI,KAAK,WAAY,CACjB,MAAMC,EAAa,IAAI,MAAM,mDAAmD,EAChFA,EAAW,KAAO,aAClB,KAAK,WAAW,MAAMA,CAAU,CACpC,CACA,MAAMC,EAAgB,IAAI,gBAC1B,YAAK,WAAaA,EACXA,EAAc,MACzB,CACA,gBAAiB,CACb,GAAI,KAAK,WAAY,CACjB,MAAMD,EAAa,IAAI,MAAM,gDAAgD,EAC7EA,EAAW,KAAO,aAClB,KAAK,WAAW,MAAMA,CAAU,EAChC,KAAK,WAAa,MACtB,CACJ,CACJ,CACA,MAAME,EAAuB,IAAIH,EAE3BI,EAAc,CAAC,iBAAkB,UAAU,EACjD,SAASC,EAA0BC,EAAY,CAC3C,GAAKA,GAGD,EAAAF,EAAY,QAAQE,CAAU,EAAI,GAGtC,OAAOA,CACX,CAEA,eAAeC,EAAkBC,EAAa,CAC1C,GAAI,CAACzB,EAAwB,EACzB,MAAM,IAAI,MAAM,2CAA2C,EAW/D,MAAMa,EAAU,CAAE,UATA,CACd,GAAGY,EACH,UAAWhC,EAAwBgC,EAAY,SAAS,EACxD,KAAM,CACF,GAAGA,EAAY,KACf,GAAIhC,EAAwBgC,EAAY,KAAK,EAAE,CACnD,EACA,mBAAoBA,EAAY,oBAAoB,IAAIxB,CAA+B,CAC3F,CAC4B,EAC5BY,EAAQ,OAASO,EAAqB,qBAAqB,EAC3D,IAAIM,EACJ,GAAI,CACAA,EAAc,MAAM,UAAU,YAAY,OAAOb,CAAO,CAC5D,OACOc,EAAK,CACR,MAAMhB,EAA0B,CAAE,MAAOgB,EAAK,QAAAd,CAAQ,CAAC,CAC3D,CACA,GAAI,CAACa,EACD,MAAM,IAAI,MAAM,gCAAgC,EAEpD,KAAM,CAAE,GAAAvB,EAAI,MAAAyB,EAAO,SAAAC,EAAU,KAAAC,CAAK,EAAIJ,EACtC,IAAIK,EACA,OAAOF,EAAS,eAAkB,aAClCE,EAAaF,EAAS,cAAc,GAExC,IAAIG,EACJ,GAAI,OAAOH,EAAS,uBAA0B,WAC1C,GAAI,CACAG,EAA6BH,EAAS,sBAAsB,CAChE,OACOjB,EAAO,CACVqB,EAA2B,0BAA2BrB,CAAK,CAC/D,CAEJ,IAAIsB,EACJ,GAAI,OAAOL,EAAS,cAAiB,WACjC,GAAI,CACA,MAAMM,EAAaN,EAAS,aAAa,EACrCM,IAAe,OACfD,EAAoB9C,EAAwB+C,CAAU,EAE9D,OACOvB,EAAO,CACVqB,EAA2B,iBAAkBrB,CAAK,CACtD,CAEJ,IAAIwB,EACJ,GAAI,OAAOP,EAAS,sBAAyB,WACzC,GAAI,CACAO,EAA4BhD,EAAwByC,EAAS,qBAAqB,CAAC,CACvF,OACOjB,EAAO,CACVqB,EAA2B,yBAA0BrB,CAAK,CAC9D,CAEJ,MAAO,CACH,GAAAT,EACA,MAAOf,EAAwBwC,CAAK,EACpC,SAAU,CACN,kBAAmBxC,EAAwByC,EAAS,iBAAiB,EACrE,eAAgBzC,EAAwByC,EAAS,cAAc,EAC/D,WAAAE,EACA,mBAAoBC,EACpB,UAAWE,EACX,kBAAmBE,CACvB,EACA,KAAAN,EACA,uBAAwBJ,EAAW,0BAA0B,EAC7D,wBAAyBJ,EAA0BI,EAAW,uBAAuB,CACzF,CACJ,CACA,SAASO,EAA2BI,EAAY5B,EAAO,CACnD,QAAQ,KAAK,yFAAyF4B,CAAU;AAAA,EAA6C5B,CAAK,CACtK,CAEA,SAAS6B,GAAkC,CACvC,GAAI,CAACtC,EAAwB,EACzB,OAAO,IAAI,QAASuC,GAAYA,EAAQ,EAAK,CAAC,EAElD,MAAMC,EAA4B,OAC7B,oBACL,OAAIA,EAA0B,kCAAoC,OACvD,IAAI,QAASD,GAAYA,EAAQ,EAAK,CAAC,EAE3CC,EAA0B,gCAAgC,CACrE,CAEA,SAASC,EAA4B,CAAE,MAAA7B,EAAO,QAAAC,CAAS,EAAG,CACtD,KAAM,CAAE,UAAAC,CAAU,EAAID,EACtB,GAAI,CAACC,EACD,MAAM,MAAM,iDAAiD,EAEjE,GAAIF,EAAM,OAAS,cACf,GAAIC,EAAQ,kBAAkB,YAC1B,OAAO,IAAIP,EAAc,CACrB,QAAS,mDACT,KAAM,yBACN,MAAOM,CACX,CAAC,MAGJ,IAAIA,EAAM,OAAS,kBACpB,OAAO,IAAIN,EAAc,CACrB,QAASM,EAAM,QACf,KAAM,uCACN,MAAOA,CACX,CAAC,EAEA,GAAIA,EAAM,OAAS,gBAAiB,CACrC,MAAMI,EAAkB,OAAO,SAAS,SACxC,GAAKZ,EAAcY,CAAe,GAO7B,GAAIF,EAAU,OAASE,EACxB,OAAO,IAAIV,EAAc,CACrB,QAAS,cAAcQ,EAAU,IAAI,+BACrC,KAAM,sBACN,MAAOF,CACX,CAAC,MAXD,QAAO,IAAIN,EAAc,CACrB,QAAS,GAAG,OAAO,SAAS,QAAQ,wBACpC,KAAM,uBACN,MAAOM,CACX,CAAC,CAST,SACSA,EAAM,OAAS,eACpB,OAAO,IAAIN,EAAc,CACrB,QAAS,+GACT,KAAM,oCACN,MAAOM,CACX,CAAC,EAEL,OAAOA,CACX,CAEA,eAAe8B,EAAoBjB,EAAakB,EAAqB,GAAO,CACxE,GAAI,CAAC3C,EAAwB,EACzB,MAAM,IAAI,MAAM,2CAA2C,EAE/D,IAAI4C,EACAnB,EAAY,kBAAkB,SAAW,IACzCmB,EAAmBnB,EAAY,kBAAkB,IAAIxB,CAA+B,GAExF,MAAMa,EAAY,CACd,GAAGW,EACH,UAAWhC,EAAwBgC,EAAY,SAAS,EACxD,iBAAAmB,CACJ,EACM/B,EAAU,CAAC,EACjB,GAAI8B,EAAoB,CACpB,GAAI,CAAE,MAAML,EAAgC,EACxC,MAAM,MAAM,4CAA4C,EAG5D,GADuB,SAAS,iBAAiB,iCAAiC,EAC/D,OAAS,EACxB,MAAM,MAAM,mGAAmG,EAEnHzB,EAAQ,UAAY,cACpBC,EAAU,iBAAmB,CAAC,CAClC,CACAD,EAAQ,UAAYC,EACpBD,EAAQ,OAASO,EAAqB,qBAAqB,EAC3D,IAAIM,EACJ,GAAI,CACAA,EAAc,MAAM,UAAU,YAAY,IAAIb,CAAO,CACzD,OACOc,EAAK,CACR,MAAMc,EAA4B,CAAE,MAAOd,EAAK,QAAAd,CAAQ,CAAC,CAC7D,CACA,GAAI,CAACa,EACD,MAAM,IAAI,MAAM,kCAAkC,EAEtD,KAAM,CAAE,GAAAvB,EAAI,MAAAyB,EAAO,SAAAC,EAAU,KAAAC,CAAK,EAAIJ,EACtC,IAAImB,EACJ,OAAIhB,EAAS,aACTgB,EAAazD,EAAwByC,EAAS,UAAU,GAErD,CACH,GAAA1B,EACA,MAAOf,EAAwBwC,CAAK,EACpC,SAAU,CACN,kBAAmBxC,EAAwByC,EAAS,iBAAiB,EACrE,eAAgBzC,EAAwByC,EAAS,cAAc,EAC/D,UAAWzC,EAAwByC,EAAS,SAAS,EACrD,WAAAgB,CACJ,EACA,KAAAf,EACA,uBAAwBJ,EAAW,0BAA0B,EAC7D,wBAAyBJ,EAA0BI,EAAW,uBAAuB,CACzF,CACJ,CAEA,SAASoB,GAAmC,CACxC,OAAK9C,EAAwB,EAGtB,oBAAoB,8CAA8C,EAF9D,IAAI,QAASuC,GAAYA,EAAQ,EAAK,CAAC,CAGtD,ECjXG,gBAAkB,CACjB,MAAMQ,EAAqB,0BACrBC,EAA6B,kCAC7BC,EAAwC,UACxCC,EAA0B,mCAC1BC,EACJ,4CACIC,EACJ,0CAEIC,EAAuB,8BACvBC,EAA0B,iCAC1BC,EAAwB,+BAE9B,eAAeC,EACbtE,EAAc,CAEd,MAAMuE,EAAwB,SAAS,eAAeV,CAAkB,EACxE,GAAI,CAACU,EACH,OAGF,MAAMC,EACJD,EAAsB,aAAe,QAAQ,oBAAoB,EAGnEA,EAAsB,iBAAiB,QAAS,MAAOE,GAAM,CAC3DF,EAAsB,cACpB,IAAI,YAAYJ,EAAsB,CAAE,QAAS,EAAK,CAAC,CAAC,EAE1D,MAAMO,EAAwB,CAC5B,eAAgB,GAChB,YAAa,QAAQ,gBAAgB,C,CACtC,EAGD,MAAM/B,EAAW,MAAM,MAAM3C,EAAO,qBAAsB,CACxD,OAAQ,OACR,YAAa,cACb,QAAS,CACP,cAAeA,EAAO,UACtB,OAAQ,kB,EAEX,EAED,GAAI,CAAC2C,EAAS,GAAI,CAChB+B,EAAwB,CACtB,eAAgB,GAChB,YAAAF,EACA,aAAc,GACd,OAAQ,QACN,wEAAwE,C,CAE3E,EACDD,EAAsB,cACpB,IAAI,YAAYF,EAAuB,CACrC,OAAQ,CACN,SAAA1B,C,EAEF,QAAS,E,CACV,CAAC,EAEJ,MACF,CAEA,IAAIgC,EAEJ,GAAI,CACFA,EAAU,MAAMrC,EAAkB,MAAMK,EAAS,KAAK,CAAC,CACzD,OAASjB,EAAgB,CAEvB,GADA,QAAQ,MAAMA,CAAK,EACfA,aAAiB,OAASA,aAAiBN,EAAe,CAC5D,OAAQM,EAAM,KAAM,CAClB,IAAK,aACHgD,EAAwB,CACtB,eAAgB,GAChB,YAAAF,EACA,OAAQ,QAAQ,uBAAuB,EACvC,aAAc,E,CACf,EACD,MACF,IAAK,oBACHE,EAAwB,CACtB,eAAgB,GAChB,YAAAF,EACA,OAAQ,QACN,uFAAuF,EAEzF,aAAc,E,CACf,EACD,MACF,IAAK,kBACHE,EAAwB,CACtB,eAAgB,GAChB,YAAAF,EACA,OAAQ,QAAQ,sCAAsC,EACtD,aAAc,E,CACf,EACD,MACF,IAAK,gBACHE,EAAwB,CACtB,eAAgB,GAChB,YAAAF,EACA,OAAQ,QACN,+GAA+G,EAEjH,aAAc,E,CACf,EACD,MACF,QACE,MAAAE,EAAwB,CACtB,eAAgB,GAChB,YAAAF,EACA,OAAQ,QACN,iDAAiD,EAEnD,aAAc,E,CACf,EACK9C,CACV,CACA6C,EAAsB,cACpB,IAAI,YAAYF,EAAuB,CACrC,OAAQ,CACN,MAAA3C,C,EAEF,QAAS,E,CACV,CAAC,EAEJ,MACF,CACF,CAEAgD,EAAwB,CACtB,eAAgB,GAChB,YAAa,QAAQ,2BAA2B,C,CACjD,EAGD,MAAME,EAAmB,MAAM,MAAM5E,EAAO,wBAAyB,CACnE,OAAQ,OACR,QAAS,CACP,eAAgB,mBAChB,cAAeA,EAAO,S,EAExB,YAAa,cACb,KAAM,KAAK,UAAU2E,CAAO,C,CAC7B,EAED,GAAI,CAACC,EAAiB,GAAI,CACxBF,EAAwB,CACtB,eAAgB,GAChB,YAAAF,EACA,OAAQ,QACN,oEAAoE,EAEtE,aAAc,E,CACf,EACDD,EAAsB,cACpB,IAAI,YAAYF,EAAuB,CACrC,OAAQ,CACN,SAAUO,C,EAEZ,QAAS,E,CACV,CAAC,EAEJ,MACF,CAGA,GACE,CAACA,EAAiB,QACf,IAAI,cAAc,GACjB,SAAS,kBAAkB,EAC/B,CACAF,EAAwB,CACtB,eAAgB,GAChB,YAAAF,EACA,OAAQ,QAAQ,+CAA+C,EAC/D,aAAc,E,CACf,EACDD,EAAsB,cACpB,IAAI,YAAYF,EAAuB,CACrC,OAAQ,CACN,SAAUO,C,EAEZ,QAAS,E,CACV,CAAC,EAEJ,MACF,CAGA,MAAMC,EAAmB,MAAMD,EAAiB,KAAK,EAGrD,GAAIC,GAAoBA,EAAiB,GACvCH,EAAwB,CACtB,eAAgB,GAChB,YAAAF,EACA,OAAQ,QAAQ,0BAA0B,EAC1C,aAAc,E,CACf,EACDD,EAAsB,cACpB,IAAI,YAAYH,EAAyB,CACvC,OAAQ,CACN,SAAUS,EACV,GAAIA,EAAiB,E,EAEvB,QAAS,E,CACV,CAAC,MAEC,CACL,MAAMC,EACJD,EAAiB,OACjB,QAAQ,wCAAwC,EAClDH,EAAwB,CACtB,eAAgB,GAChB,YAAAF,EACA,OAAQM,EACR,aAAc,E,CACf,EACDP,EAAsB,cACpB,IAAI,YAAYF,EAAuB,CACrC,OAAQ,CACN,SAAUO,C,EAEZ,QAAS,E,CACV,CAAC,CAEN,CACF,CAAC,EAED,eAAeF,EAAwBK,EAAY,CACjD,MAAMR,EAAwB,SAAS,eACrCV,CAAkB,EAEpB,GAAI,CAACU,EACH,OAEF,MAAMS,EAAoB,SAAS,eACjClB,CAA0B,EAG5BS,EAAsB,SAAWQ,EAAM,eACvCR,EAAsB,YAAcQ,EAAM,YAEtCC,IACED,EAAM,QAGRR,EAAsB,aACpB,mBACAT,CAA0B,EAE5BkB,EAAkB,UAAU,IAC1BjB,CAAqC,EAEvCiB,EAAkB,YAAcD,EAAM,OACtCC,EAAkB,aAAa,YAAa,WAAW,EAEnDD,EAAM,cACRR,EAAsB,MAAM,IAG9BA,EAAsB,gBAAgB,kBAAkB,EACxDS,EAAkB,gBAAgB,WAAW,EAC7CA,EAAkB,UAAU,OAC1BjB,CAAqC,GAI7C,CACF,CAEA,eAAekB,EAA0BC,EAAgB,CACvD,MAAMC,EAAqB,SAAS,eAClCnB,CAAuB,EAEnBoB,EAAoB,SAAS,eACjClB,CAA8B,EAE1BmB,EAAmB,SAAS,eAChCpB,CAA6B,EAG/B,GAAI,CAACkB,EACH,MAAM,IAAI,MAAM,+BAA+B,EAGjD,GAAI,CAACC,EACH,MAAM,IAAI,MAAM,8BAA8B,EAGhD,GAAIF,EAAS,CACX,MAAMI,EAAQF,EAAkB,QAAQ,UAAU,EAAI,EACtDD,EAAmB,YAAYG,CAAK,CACtC,SACMD,EAAkB,CACpB,MAAMC,EAAQD,EAAiB,QAAQ,UAAU,EAAI,EACrDF,EAAmB,YAAYG,CAAK,CACtC,MACEH,EAAmB,OAAO,CAGhC,CAEA,MAAMnF,EAAS,MAAMD,EAAU,EAE/B,GAAKe,EAAwB,EAI3B,MAAMmE,EAA0B,EAAI,EACpC,MAAMX,EAA+BtE,CAAM,MALb,CAC9B,MAAMiF,EAA0B,EAAK,EACrC,MACF,CAIF,GAAG,C", "sources": ["webpack://django-otp-webauthn/./src/utils.ts", "webpack://django-otp-webauthn/./node_modules/@simplewebauthn/browser/dist/bundle/index.js", "webpack://django-otp-webauthn/./src/register.ts"], "sourcesContent": ["import { Config } from \"./types\";\n\n/**\n * Finds the config object in the DOM and deserializes it.\n * @returns Config the config object\n */\nexport async function getConfig(): Promise<Config> {\n  let config: Config | null = null;\n  const configElement = document.getElementById(\n    \"otp_webauthn_config\",\n  ) as HTMLScriptElement;\n\n  if (configElement) {\n    config = JSON.parse(configElement.innerText) as Config;\n    return Object.freeze(config);\n  }\n  throw new Error(\"otp_webauthn_config element not found\");\n}\n", "/* [@simplewebauthn/browser@10.0.0] */\nfunction bufferToBase64URLString(buffer) {\n    const bytes = new Uint8Array(buffer);\n    let str = '';\n    for (const charCode of bytes) {\n        str += String.fromCharCode(charCode);\n    }\n    const base64String = btoa(str);\n    return base64String.replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=/g, '');\n}\n\nfunction base64URLStringToBuffer(base64URLString) {\n    const base64 = base64URLString.replace(/-/g, '+').replace(/_/g, '/');\n    const padLength = (4 - (base64.length % 4)) % 4;\n    const padded = base64.padEnd(base64.length + padLength, '=');\n    const binary = atob(padded);\n    const buffer = new ArrayBuffer(binary.length);\n    const bytes = new Uint8Array(buffer);\n    for (let i = 0; i < binary.length; i++) {\n        bytes[i] = binary.charCodeAt(i);\n    }\n    return buffer;\n}\n\nfunction browserSupportsWebAuthn() {\n    return (window?.PublicKeyCredential !== undefined &&\n        typeof window.PublicKeyCredential === 'function');\n}\n\nfunction toPublicKeyCredentialDescriptor(descriptor) {\n    const { id } = descriptor;\n    return {\n        ...descriptor,\n        id: base64URLStringToBuffer(id),\n        transports: descriptor.transports,\n    };\n}\n\nfunction isValidDomain(hostname) {\n    return (hostname === 'localhost' ||\n        /^([a-z0-9]+(-[a-z0-9]+)*\\.)+[a-z]{2,}$/i.test(hostname));\n}\n\nclass WebAuthnError extends Error {\n    constructor({ message, code, cause, name, }) {\n        super(message, { cause });\n        this.name = name ?? cause.name;\n        this.code = code;\n    }\n}\n\nfunction identifyRegistrationError({ error, options, }) {\n    const { publicKey } = options;\n    if (!publicKey) {\n        throw Error('options was missing required publicKey property');\n    }\n    if (error.name === 'AbortError') {\n        if (options.signal instanceof AbortSignal) {\n            return new WebAuthnError({\n                message: 'Registration ceremony was sent an abort signal',\n                code: 'ERROR_CEREMONY_ABORTED',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'ConstraintError') {\n        if (publicKey.authenticatorSelection?.requireResidentKey === true) {\n            return new WebAuthnError({\n                message: 'Discoverable credentials were required but no available authenticator supported it',\n                code: 'ERROR_AUTHENTICATOR_MISSING_DISCOVERABLE_CREDENTIAL_SUPPORT',\n                cause: error,\n            });\n        }\n        else if (publicKey.authenticatorSelection?.userVerification === 'required') {\n            return new WebAuthnError({\n                message: 'User verification was required but no available authenticator supported it',\n                code: 'ERROR_AUTHENTICATOR_MISSING_USER_VERIFICATION_SUPPORT',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'InvalidStateError') {\n        return new WebAuthnError({\n            message: 'The authenticator was previously registered',\n            code: 'ERROR_AUTHENTICATOR_PREVIOUSLY_REGISTERED',\n            cause: error,\n        });\n    }\n    else if (error.name === 'NotAllowedError') {\n        return new WebAuthnError({\n            message: error.message,\n            code: 'ERROR_PASSTHROUGH_SEE_CAUSE_PROPERTY',\n            cause: error,\n        });\n    }\n    else if (error.name === 'NotSupportedError') {\n        const validPubKeyCredParams = publicKey.pubKeyCredParams.filter((param) => param.type === 'public-key');\n        if (validPubKeyCredParams.length === 0) {\n            return new WebAuthnError({\n                message: 'No entry in pubKeyCredParams was of type \"public-key\"',\n                code: 'ERROR_MALFORMED_PUBKEYCREDPARAMS',\n                cause: error,\n            });\n        }\n        return new WebAuthnError({\n            message: 'No available authenticator supported any of the specified pubKeyCredParams algorithms',\n            code: 'ERROR_AUTHENTICATOR_NO_SUPPORTED_PUBKEYCREDPARAMS_ALG',\n            cause: error,\n        });\n    }\n    else if (error.name === 'SecurityError') {\n        const effectiveDomain = window.location.hostname;\n        if (!isValidDomain(effectiveDomain)) {\n            return new WebAuthnError({\n                message: `${window.location.hostname} is an invalid domain`,\n                code: 'ERROR_INVALID_DOMAIN',\n                cause: error,\n            });\n        }\n        else if (publicKey.rp.id !== effectiveDomain) {\n            return new WebAuthnError({\n                message: `The RP ID \"${publicKey.rp.id}\" is invalid for this domain`,\n                code: 'ERROR_INVALID_RP_ID',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'TypeError') {\n        if (publicKey.user.id.byteLength < 1 || publicKey.user.id.byteLength > 64) {\n            return new WebAuthnError({\n                message: 'User ID was not between 1 and 64 characters',\n                code: 'ERROR_INVALID_USER_ID_LENGTH',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'UnknownError') {\n        return new WebAuthnError({\n            message: 'The authenticator was unable to process the specified options, or could not create a new credential',\n            code: 'ERROR_AUTHENTICATOR_GENERAL_ERROR',\n            cause: error,\n        });\n    }\n    return error;\n}\n\nclass BaseWebAuthnAbortService {\n    createNewAbortSignal() {\n        if (this.controller) {\n            const abortError = new Error('Cancelling existing WebAuthn API call for new one');\n            abortError.name = 'AbortError';\n            this.controller.abort(abortError);\n        }\n        const newController = new AbortController();\n        this.controller = newController;\n        return newController.signal;\n    }\n    cancelCeremony() {\n        if (this.controller) {\n            const abortError = new Error('Manually cancelling existing WebAuthn API call');\n            abortError.name = 'AbortError';\n            this.controller.abort(abortError);\n            this.controller = undefined;\n        }\n    }\n}\nconst WebAuthnAbortService = new BaseWebAuthnAbortService();\n\nconst attachments = ['cross-platform', 'platform'];\nfunction toAuthenticatorAttachment(attachment) {\n    if (!attachment) {\n        return;\n    }\n    if (attachments.indexOf(attachment) < 0) {\n        return;\n    }\n    return attachment;\n}\n\nasync function startRegistration(optionsJSON) {\n    if (!browserSupportsWebAuthn()) {\n        throw new Error('WebAuthn is not supported in this browser');\n    }\n    const publicKey = {\n        ...optionsJSON,\n        challenge: base64URLStringToBuffer(optionsJSON.challenge),\n        user: {\n            ...optionsJSON.user,\n            id: base64URLStringToBuffer(optionsJSON.user.id),\n        },\n        excludeCredentials: optionsJSON.excludeCredentials?.map(toPublicKeyCredentialDescriptor),\n    };\n    const options = { publicKey };\n    options.signal = WebAuthnAbortService.createNewAbortSignal();\n    let credential;\n    try {\n        credential = (await navigator.credentials.create(options));\n    }\n    catch (err) {\n        throw identifyRegistrationError({ error: err, options });\n    }\n    if (!credential) {\n        throw new Error('Registration was not completed');\n    }\n    const { id, rawId, response, type } = credential;\n    let transports = undefined;\n    if (typeof response.getTransports === 'function') {\n        transports = response.getTransports();\n    }\n    let responsePublicKeyAlgorithm = undefined;\n    if (typeof response.getPublicKeyAlgorithm === 'function') {\n        try {\n            responsePublicKeyAlgorithm = response.getPublicKeyAlgorithm();\n        }\n        catch (error) {\n            warnOnBrokenImplementation('getPublicKeyAlgorithm()', error);\n        }\n    }\n    let responsePublicKey = undefined;\n    if (typeof response.getPublicKey === 'function') {\n        try {\n            const _publicKey = response.getPublicKey();\n            if (_publicKey !== null) {\n                responsePublicKey = bufferToBase64URLString(_publicKey);\n            }\n        }\n        catch (error) {\n            warnOnBrokenImplementation('getPublicKey()', error);\n        }\n    }\n    let responseAuthenticatorData;\n    if (typeof response.getAuthenticatorData === 'function') {\n        try {\n            responseAuthenticatorData = bufferToBase64URLString(response.getAuthenticatorData());\n        }\n        catch (error) {\n            warnOnBrokenImplementation('getAuthenticatorData()', error);\n        }\n    }\n    return {\n        id,\n        rawId: bufferToBase64URLString(rawId),\n        response: {\n            attestationObject: bufferToBase64URLString(response.attestationObject),\n            clientDataJSON: bufferToBase64URLString(response.clientDataJSON),\n            transports,\n            publicKeyAlgorithm: responsePublicKeyAlgorithm,\n            publicKey: responsePublicKey,\n            authenticatorData: responseAuthenticatorData,\n        },\n        type,\n        clientExtensionResults: credential.getClientExtensionResults(),\n        authenticatorAttachment: toAuthenticatorAttachment(credential.authenticatorAttachment),\n    };\n}\nfunction warnOnBrokenImplementation(methodName, cause) {\n    console.warn(`The browser extension that intercepted this WebAuthn API call incorrectly implemented ${methodName}. You should report this error to them.\\n`, cause);\n}\n\nfunction browserSupportsWebAuthnAutofill() {\n    if (!browserSupportsWebAuthn()) {\n        return new Promise((resolve) => resolve(false));\n    }\n    const globalPublicKeyCredential = window\n        .PublicKeyCredential;\n    if (globalPublicKeyCredential.isConditionalMediationAvailable === undefined) {\n        return new Promise((resolve) => resolve(false));\n    }\n    return globalPublicKeyCredential.isConditionalMediationAvailable();\n}\n\nfunction identifyAuthenticationError({ error, options, }) {\n    const { publicKey } = options;\n    if (!publicKey) {\n        throw Error('options was missing required publicKey property');\n    }\n    if (error.name === 'AbortError') {\n        if (options.signal instanceof AbortSignal) {\n            return new WebAuthnError({\n                message: 'Authentication ceremony was sent an abort signal',\n                code: 'ERROR_CEREMONY_ABORTED',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'NotAllowedError') {\n        return new WebAuthnError({\n            message: error.message,\n            code: 'ERROR_PASSTHROUGH_SEE_CAUSE_PROPERTY',\n            cause: error,\n        });\n    }\n    else if (error.name === 'SecurityError') {\n        const effectiveDomain = window.location.hostname;\n        if (!isValidDomain(effectiveDomain)) {\n            return new WebAuthnError({\n                message: `${window.location.hostname} is an invalid domain`,\n                code: 'ERROR_INVALID_DOMAIN',\n                cause: error,\n            });\n        }\n        else if (publicKey.rpId !== effectiveDomain) {\n            return new WebAuthnError({\n                message: `The RP ID \"${publicKey.rpId}\" is invalid for this domain`,\n                code: 'ERROR_INVALID_RP_ID',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'UnknownError') {\n        return new WebAuthnError({\n            message: 'The authenticator was unable to process the specified options, or could not create a new assertion signature',\n            code: 'ERROR_AUTHENTICATOR_GENERAL_ERROR',\n            cause: error,\n        });\n    }\n    return error;\n}\n\nasync function startAuthentication(optionsJSON, useBrowserAutofill = false) {\n    if (!browserSupportsWebAuthn()) {\n        throw new Error('WebAuthn is not supported in this browser');\n    }\n    let allowCredentials;\n    if (optionsJSON.allowCredentials?.length !== 0) {\n        allowCredentials = optionsJSON.allowCredentials?.map(toPublicKeyCredentialDescriptor);\n    }\n    const publicKey = {\n        ...optionsJSON,\n        challenge: base64URLStringToBuffer(optionsJSON.challenge),\n        allowCredentials,\n    };\n    const options = {};\n    if (useBrowserAutofill) {\n        if (!(await browserSupportsWebAuthnAutofill())) {\n            throw Error('Browser does not support WebAuthn autofill');\n        }\n        const eligibleInputs = document.querySelectorAll(\"input[autocomplete$='webauthn']\");\n        if (eligibleInputs.length < 1) {\n            throw Error('No <input> with \"webauthn\" as the only or last value in its `autocomplete` attribute was detected');\n        }\n        options.mediation = 'conditional';\n        publicKey.allowCredentials = [];\n    }\n    options.publicKey = publicKey;\n    options.signal = WebAuthnAbortService.createNewAbortSignal();\n    let credential;\n    try {\n        credential = (await navigator.credentials.get(options));\n    }\n    catch (err) {\n        throw identifyAuthenticationError({ error: err, options });\n    }\n    if (!credential) {\n        throw new Error('Authentication was not completed');\n    }\n    const { id, rawId, response, type } = credential;\n    let userHandle = undefined;\n    if (response.userHandle) {\n        userHandle = bufferToBase64URLString(response.userHandle);\n    }\n    return {\n        id,\n        rawId: bufferToBase64URLString(rawId),\n        response: {\n            authenticatorData: bufferToBase64URLString(response.authenticatorData),\n            clientDataJSON: bufferToBase64URLString(response.clientDataJSON),\n            signature: bufferToBase64URLString(response.signature),\n            userHandle,\n        },\n        type,\n        clientExtensionResults: credential.getClientExtensionResults(),\n        authenticatorAttachment: toAuthenticatorAttachment(credential.authenticatorAttachment),\n    };\n}\n\nfunction platformAuthenticatorIsAvailable() {\n    if (!browserSupportsWebAuthn()) {\n        return new Promise((resolve) => resolve(false));\n    }\n    return PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();\n}\n\nexport { WebAuthnAbortService, WebAuthnError, base64URLStringToBuffer, browserSupportsWebAuthn, browserSupportsWebAuthnAutofill, bufferToBase64URLString, platformAuthenticatorIsAvailable, startAuthentication, startRegistration };\n", "import { State, Config } from \"./types\";\nimport { getConfig } from \"./utils\";\nimport {\n  browserSupportsWebAuthn,\n  startRegistration,\n  WebAuthnError,\n} from \"@simplewebauthn/browser\";\n\n/**\n * This function is immediately invoked and sets up the registration button\n */\n(() =>\n  (async function () {\n    const REGISTER_BUTTON_ID = \"passkey-register-button\";\n    const REGISTER_STATUS_MESSAGE_ID = \"passkey-register-status-message\";\n    const REGISTER_STATUS_MESSAGE_VISIBLE_CLASS = \"visible\";\n    const REGISTER_PLACEHOLDER_ID = \"passkey-registration-placeholder\";\n    const REGISTER_FALLBACK_TEMPLATE_ID =\n      \"passkey-registration-unavailable-template\";\n    const REGISTER_AVAILABLE_TEMPLATE_ID =\n      \"passkey-registration-available-template\";\n\n    const EVENT_REGISTER_START = \"otp_webauthn.register_start\";\n    const EVENT_REGISTER_COMPLETE = \"otp_webauthn.register_complete\";\n    const EVENT_REGISTER_FAILED = \"otp_webauthn.register_failed\";\n\n    async function setupPasskeyRegistrationButton(\n      config: Config,\n    ): Promise<void> {\n      const passkeyRegisterButton = document.getElementById(REGISTER_BUTTON_ID);\n      if (!passkeyRegisterButton) {\n        return;\n      }\n\n      const buttonLabel =\n        passkeyRegisterButton.textContent || gettext(\"Register a Passkey\");\n\n      // Register button click handler\n      passkeyRegisterButton.addEventListener(\"click\", async (_) => {\n        passkeyRegisterButton.dispatchEvent(\n          new CustomEvent(EVENT_REGISTER_START, { bubbles: true }),\n        );\n        await setPasskeyRegisterState({\n          buttonDisabled: true,\n          buttonLabel: gettext(\"Registering...\"),\n        });\n\n        // Begin registration: fetch options and challenge\n        const response = await fetch(config.beginRegistrationUrl, {\n          method: \"POST\",\n          credentials: \"same-origin\",\n          headers: {\n            \"X-CSRFToken\": config.csrfToken,\n            Accept: \"application/json\",\n          },\n        });\n\n        if (!response.ok) {\n          setPasskeyRegisterState({\n            buttonDisabled: false,\n            buttonLabel,\n            requestFocus: true,\n            status: gettext(\n              \"Registration failed. Unable to fetch registration options from server.\",\n            ),\n          });\n          passkeyRegisterButton.dispatchEvent(\n            new CustomEvent(EVENT_REGISTER_FAILED, {\n              detail: {\n                response,\n              },\n              bubbles: true,\n            }),\n          );\n          return;\n        }\n\n        let attResp;\n\n        try {\n          attResp = await startRegistration(await response.json());\n        } catch (error: unknown) {\n          console.error(error);\n          if (error instanceof Error || error instanceof WebAuthnError) {\n            switch (error.name) {\n              case \"AbortError\":\n                setPasskeyRegisterState({\n                  buttonDisabled: false,\n                  buttonLabel,\n                  status: gettext(\"Registration aborted.\"),\n                  requestFocus: true,\n                });\n                break;\n              case \"InvalidStateError\":\n                setPasskeyRegisterState({\n                  buttonDisabled: false,\n                  buttonLabel,\n                  status: gettext(\n                    \"Registration failed. You most likely already have a Passkey registered for this site.\",\n                  ),\n                  requestFocus: true,\n                });\n                break;\n              case \"NotAllowedError\":\n                setPasskeyRegisterState({\n                  buttonDisabled: false,\n                  buttonLabel,\n                  status: gettext(\"Registration aborted or not allowed.\"),\n                  requestFocus: true,\n                });\n                break;\n              case \"SecurityError\":\n                setPasskeyRegisterState({\n                  buttonDisabled: false,\n                  buttonLabel,\n                  status: gettext(\n                    \"Registration failed. A technical problem occurred that prevents you from registering a Passkey for this site.\",\n                  ),\n                  requestFocus: true,\n                });\n                break;\n              default:\n                setPasskeyRegisterState({\n                  buttonDisabled: false,\n                  buttonLabel,\n                  status: gettext(\n                    \"Registration failed. An unknown error occurred.\",\n                  ),\n                  requestFocus: true,\n                });\n                throw error;\n            }\n            passkeyRegisterButton.dispatchEvent(\n              new CustomEvent(EVENT_REGISTER_FAILED, {\n                detail: {\n                  error,\n                },\n                bubbles: true,\n              }),\n            );\n            return;\n          }\n        }\n\n        setPasskeyRegisterState({\n          buttonDisabled: true,\n          buttonLabel: gettext(\"Finishing registration...\"),\n        });\n\n        // Complete\n        const verificationResp = await fetch(config.completeRegistrationUrl, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            \"X-CSRFToken\": config.csrfToken,\n          },\n          credentials: \"same-origin\",\n          body: JSON.stringify(attResp),\n        });\n\n        if (!verificationResp.ok) {\n          setPasskeyRegisterState({\n            buttonDisabled: false,\n            buttonLabel,\n            status: gettext(\n              \"Registration failed. The server was unable to verify this passkey.\",\n            ),\n            requestFocus: true,\n          });\n          passkeyRegisterButton.dispatchEvent(\n            new CustomEvent(EVENT_REGISTER_FAILED, {\n              detail: {\n                response: verificationResp,\n              },\n              bubbles: true,\n            }),\n          );\n          return;\n        }\n\n        // Check if the response is a JSON object\n        if (\n          !verificationResp.headers\n            .get(\"content-type\")\n            ?.includes(\"application/json\")\n        ) {\n          setPasskeyRegisterState({\n            buttonDisabled: false,\n            buttonLabel,\n            status: gettext(\"Registration failed. A server error occurred.\"),\n            requestFocus: true,\n          });\n          passkeyRegisterButton.dispatchEvent(\n            new CustomEvent(EVENT_REGISTER_FAILED, {\n              detail: {\n                response: verificationResp,\n              },\n              bubbles: true,\n            }),\n          );\n          return;\n        }\n\n        // Wait for the results of verification\n        const verificationJSON = await verificationResp.json();\n\n        // Show UI appropriate for the `verified` status\n        if (verificationJSON && verificationJSON.id) {\n          setPasskeyRegisterState({\n            buttonDisabled: false,\n            buttonLabel,\n            status: gettext(\"Registration successful!\"),\n            requestFocus: true,\n          });\n          passkeyRegisterButton.dispatchEvent(\n            new CustomEvent(EVENT_REGISTER_COMPLETE, {\n              detail: {\n                response: verificationJSON,\n                id: verificationJSON.id,\n              },\n              bubbles: true,\n            }),\n          );\n        } else {\n          const msg =\n            verificationJSON.error ||\n            gettext(\"An error occurred during registration.\");\n          setPasskeyRegisterState({\n            buttonDisabled: false,\n            buttonLabel,\n            status: msg,\n            requestFocus: true,\n          });\n          passkeyRegisterButton.dispatchEvent(\n            new CustomEvent(EVENT_REGISTER_FAILED, {\n              detail: {\n                response: verificationResp,\n              },\n              bubbles: true,\n            }),\n          );\n        }\n      });\n\n      async function setPasskeyRegisterState(state: State): Promise<void> {\n        const passkeyRegisterButton = document.getElementById(\n          REGISTER_BUTTON_ID,\n        ) as HTMLButtonElement;\n        if (!passkeyRegisterButton) {\n          return;\n        }\n        const passkeyStatusText = document.getElementById(\n          REGISTER_STATUS_MESSAGE_ID,\n        ) as HTMLElement;\n\n        passkeyRegisterButton.disabled = state.buttonDisabled;\n        passkeyRegisterButton.textContent = state.buttonLabel;\n\n        if (passkeyStatusText) {\n          if (state.status) {\n            // If there is a status message, we want to make sure screen readers\n            // announce it to the user for clarity as to what is happening.\n            passkeyRegisterButton.setAttribute(\n              \"aria-describedby\",\n              REGISTER_STATUS_MESSAGE_ID,\n            );\n            passkeyStatusText.classList.add(\n              REGISTER_STATUS_MESSAGE_VISIBLE_CLASS,\n            );\n            passkeyStatusText.textContent = state.status;\n            passkeyStatusText.setAttribute(\"aria-live\", \"assertive\");\n\n            if (state.requestFocus) {\n              passkeyRegisterButton.focus();\n            }\n          } else {\n            passkeyRegisterButton.removeAttribute(\"aria-describedby\");\n            passkeyStatusText.removeAttribute(\"aria-live\");\n            passkeyStatusText.classList.remove(\n              REGISTER_STATUS_MESSAGE_VISIBLE_CLASS,\n            );\n          }\n        }\n      }\n    }\n\n    async function setPasskeyRegisterVisible(visible: boolean): Promise<void> {\n      const placeholderElement = document.getElementById(\n        REGISTER_PLACEHOLDER_ID,\n      );\n      const availableTemplate = document.getElementById(\n        REGISTER_AVAILABLE_TEMPLATE_ID,\n      ) as HTMLTemplateElement;\n      const fallbackTemplate = document.getElementById(\n        REGISTER_FALLBACK_TEMPLATE_ID,\n      ) as HTMLTemplateElement;\n\n      if (!placeholderElement) {\n        throw new Error(\"Placeholder element not found\");\n      }\n\n      if (!availableTemplate) {\n        throw new Error(\"Available template not found\");\n      }\n\n      if (visible) {\n        const clone = availableTemplate.content.cloneNode(true);\n        placeholderElement.replaceWith(clone);\n      } else {\n        if (fallbackTemplate) {\n          const clone = fallbackTemplate.content.cloneNode(true);\n          placeholderElement.replaceWith(clone);\n        } else {\n          placeholderElement.remove();\n        }\n      }\n    }\n\n    const config = await getConfig();\n\n    if (!browserSupportsWebAuthn()) {\n      await setPasskeyRegisterVisible(false);\n      return;\n    } else {\n      await setPasskeyRegisterVisible(true);\n      await setupPasskeyRegistrationButton(config);\n    }\n  })())();\n"], "names": ["getConfig", "config", "configElement", "bufferToBase64URLString", "buffer", "bytes", "str", "charCode", "base64URLStringToBuffer", "base64URLString", "base64", "<PERSON><PERSON><PERSON><PERSON>", "padded", "binary", "i", "browserSupportsWebAuthn", "toPublicKeyCredentialDescriptor", "descriptor", "id", "isValidDomain", "hostname", "WebAuthnError", "message", "code", "cause", "name", "identifyRegistrationError", "error", "options", "public<PERSON>ey", "param", "effectiveDomain", "BaseWebAuthnAbortService", "abortError", "newController", "WebAuthnAbortService", "attachments", "toAuthenticatorAttachment", "attachment", "startRegistration", "optionsJSON", "credential", "err", "rawId", "response", "type", "transports", "responsePublicKeyAlgorithm", "warnOnBrokenImplementation", "responsePublicKey", "_public<PERSON>ey", "responseAuthenticatorData", "methodName", "browserSupportsWebAuthnAutofill", "resolve", "globalPublicKeyCredential", "identifyAuthenticationError", "startAuthentication", "useBrowserAutofill", "allowCredentials", "userHandle", "platformAuthenticatorIsAvailable", "REGISTER_BUTTON_ID", "REGISTER_STATUS_MESSAGE_ID", "REGISTER_STATUS_MESSAGE_VISIBLE_CLASS", "REGISTER_PLACEHOLDER_ID", "REGISTER_FALLBACK_TEMPLATE_ID", "REGISTER_AVAILABLE_TEMPLATE_ID", "EVENT_REGISTER_START", "EVENT_REGISTER_COMPLETE", "EVENT_REGISTER_FAILED", "setupPasskeyRegistrationButton", "passkeyRegisterButton", "buttonLabel", "_", "setPasskeyRegisterState", "attResp", "verificationResp", "verificationJSON", "msg", "state", "passkeyStatusText", "setPasskeyRegisterVisible", "visible", "placeholderElement", "availableTemplate", "fallbackTemplate", "clone"], "sourceRoot": ""}