{"version": 3, "file": "otp_webauthn_auth.js", "mappings": "4BAMO,eAAeA,GAAS,CAC7B,IAAIC,EAAwB,KAC5B,MAAMC,EAAgB,SAAS,eAC7B,qBAAqB,EAGvB,GAAIA,EACF,OAAAD,EAAS,KAAK,MAAMC,EAAc,SAAS,EACpC,OAAO,OAAOD,CAAM,EAE7B,MAAM,IAAI,MAAM,uCAAuC,CACzD,CChBA,SAASE,EAAwBC,EAAQ,CACrC,MAAMC,EAAQ,IAAI,WAAWD,CAAM,EACnC,IAAIE,EAAM,GACV,UAAWC,KAAYF,EACnBC,GAAO,OAAO,aAAaC,CAAQ,EAGvC,OADqB,KAAKD,CAAG,EACT,QAAQ,MAAO,GAAG,EAAE,QAAQ,MAAO,GAAG,EAAE,QAAQ,KAAM,EAAE,CAChF,CAEA,SAASE,EAAwBC,EAAiB,CAC9C,MAAMC,EAASD,EAAgB,QAAQ,KAAM,GAAG,EAAE,QAAQ,KAAM,GAAG,EAC7DE,GAAa,EAAKD,EAAO,OAAS,GAAM,EACxCE,EAASF,EAAO,OAAOA,EAAO,OAASC,EAAW,GAAG,EACrDE,EAAS,KAAKD,CAAM,EACpBR,EAAS,IAAI,YAAYS,EAAO,MAAM,EACtCR,EAAQ,IAAI,WAAWD,CAAM,EACnC,QAASU,EAAI,EAAGA,EAAID,EAAO,OAAQC,IAC/BT,EAAMS,CAAC,EAAID,EAAO,WAAWC,CAAC,EAElC,OAAOV,CACX,CAEA,SAASW,GAA0B,CAC/B,OAAQ,QAAQ,sBAAwB,QACpC,OAAO,OAAO,qBAAwB,UAC9C,CAEA,SAASC,EAAgCC,EAAY,CACjD,KAAM,CAAE,GAAAC,CAAG,EAAID,EACf,MAAO,CACH,GAAGA,EACH,GAAIT,EAAwBU,CAAE,EAC9B,WAAYD,EAAW,UAC3B,CACJ,CAEA,SAASE,EAAcC,EAAU,CAC7B,OAAQA,IAAa,aACjB,0CAA0C,KAAKA,CAAQ,CAC/D,CAEA,MAAMC,UAAsB,KAAM,CAC9B,YAAY,CAAE,QAAAC,EAAS,KAAAC,EAAM,MAAAC,EAAO,KAAAC,CAAM,EAAG,CACzC,MAAMH,EAAS,CAAE,MAAAE,CAAM,CAAC,EACxB,KAAK,KAAOC,GAAQD,EAAM,KAC1B,KAAK,KAAOD,CAChB,CACJ,CAEA,SAASG,EAA0B,CAAE,MAAAC,EAAO,QAAAC,CAAS,EAAG,CACpD,KAAM,CAAE,UAAAC,CAAU,EAAID,EACtB,GAAI,CAACC,EACD,MAAM,MAAM,iDAAiD,EAEjE,GAAIF,EAAM,OAAS,cACf,GAAIC,EAAQ,kBAAkB,YAC1B,OAAO,IAAIP,EAAc,CACrB,QAAS,iDACT,KAAM,yBACN,MAAOM,CACX,CAAC,UAGAA,EAAM,OAAS,kBAAmB,CACvC,GAAIE,EAAU,wBAAwB,qBAAuB,GACzD,OAAO,IAAIR,EAAc,CACrB,QAAS,qFACT,KAAM,8DACN,MAAOM,CACX,CAAC,EAEA,GAAIE,EAAU,wBAAwB,mBAAqB,WAC5D,OAAO,IAAIR,EAAc,CACrB,QAAS,6EACT,KAAM,wDACN,MAAOM,CACX,CAAC,CAET,KACK,IAAIA,EAAM,OAAS,oBACpB,OAAO,IAAIN,EAAc,CACrB,QAAS,8CACT,KAAM,4CACN,MAAOM,CACX,CAAC,EAEA,GAAIA,EAAM,OAAS,kBACpB,OAAO,IAAIN,EAAc,CACrB,QAASM,EAAM,QACf,KAAM,uCACN,MAAOA,CACX,CAAC,EAEA,GAAIA,EAAM,OAAS,oBAEpB,OAD8BE,EAAU,iBAAiB,OAAQC,GAAUA,EAAM,OAAS,YAAY,EAC5E,SAAW,EAC1B,IAAIT,EAAc,CACrB,QAAS,wDACT,KAAM,mCACN,MAAOM,CACX,CAAC,EAEE,IAAIN,EAAc,CACrB,QAAS,wFACT,KAAM,wDACN,MAAOM,CACX,CAAC,EAEA,GAAIA,EAAM,OAAS,gBAAiB,CACrC,MAAMI,EAAkB,OAAO,SAAS,SACxC,GAAKZ,EAAcY,CAAe,GAO7B,GAAIF,EAAU,GAAG,KAAOE,EACzB,OAAO,IAAIV,EAAc,CACrB,QAAS,cAAcQ,EAAU,GAAG,EAAE,+BACtC,KAAM,sBACN,MAAOF,CACX,CAAC,MAXD,QAAO,IAAIN,EAAc,CACrB,QAAS,GAAG,OAAO,SAAS,QAAQ,wBACpC,KAAM,uBACN,MAAOM,CACX,CAAC,CAST,SACSA,EAAM,OAAS,aACpB,GAAIE,EAAU,KAAK,GAAG,WAAa,GAAKA,EAAU,KAAK,GAAG,WAAa,GACnE,OAAO,IAAIR,EAAc,CACrB,QAAS,8CACT,KAAM,+BACN,MAAOM,CACX,CAAC,UAGAA,EAAM,OAAS,eACpB,OAAO,IAAIN,EAAc,CACrB,QAAS,sGACT,KAAM,oCACN,MAAOM,CACX,CAAC,EAEL,OAAOA,CACX,CAEA,MAAMK,CAAyB,CAC3B,sBAAuB,CACnB,GAAI,KAAK,WAAY,CACjB,MAAMC,EAAa,IAAI,MAAM,mDAAmD,EAChFA,EAAW,KAAO,aAClB,KAAK,WAAW,MAAMA,CAAU,CACpC,CACA,MAAMC,EAAgB,IAAI,gBAC1B,YAAK,WAAaA,EACXA,EAAc,MACzB,CACA,gBAAiB,CACb,GAAI,KAAK,WAAY,CACjB,MAAMD,EAAa,IAAI,MAAM,gDAAgD,EAC7EA,EAAW,KAAO,aAClB,KAAK,WAAW,MAAMA,CAAU,EAChC,KAAK,WAAa,MACtB,CACJ,CACJ,CACA,MAAME,EAAuB,IAAIH,EAE3BI,EAAc,CAAC,iBAAkB,UAAU,EACjD,SAASC,EAA0BC,EAAY,CAC3C,GAAKA,GAGD,EAAAF,EAAY,QAAQE,CAAU,EAAI,GAGtC,OAAOA,CACX,CAEA,eAAeC,EAAkBC,EAAa,CAC1C,GAAI,CAACzB,EAAwB,EACzB,MAAM,IAAI,MAAM,2CAA2C,EAW/D,MAAMa,EAAU,CAAE,UATA,CACd,GAAGY,EACH,UAAWhC,EAAwBgC,EAAY,SAAS,EACxD,KAAM,CACF,GAAGA,EAAY,KACf,GAAIhC,EAAwBgC,EAAY,KAAK,EAAE,CACnD,EACA,mBAAoBA,EAAY,oBAAoB,IAAIxB,CAA+B,CAC3F,CAC4B,EAC5BY,EAAQ,OAASO,EAAqB,qBAAqB,EAC3D,IAAIM,EACJ,GAAI,CACAA,EAAc,MAAM,UAAU,YAAY,OAAOb,CAAO,CAC5D,OACOc,EAAK,CACR,MAAMhB,EAA0B,CAAE,MAAOgB,EAAK,QAAAd,CAAQ,CAAC,CAC3D,CACA,GAAI,CAACa,EACD,MAAM,IAAI,MAAM,gCAAgC,EAEpD,KAAM,CAAE,GAAAvB,EAAI,MAAAyB,EAAO,SAAAC,EAAU,KAAAC,CAAK,EAAIJ,EACtC,IAAIK,EACA,OAAOF,EAAS,eAAkB,aAClCE,EAAaF,EAAS,cAAc,GAExC,IAAIG,EACJ,GAAI,OAAOH,EAAS,uBAA0B,WAC1C,GAAI,CACAG,EAA6BH,EAAS,sBAAsB,CAChE,OACOjB,EAAO,CACVqB,EAA2B,0BAA2BrB,CAAK,CAC/D,CAEJ,IAAIsB,EACJ,GAAI,OAAOL,EAAS,cAAiB,WACjC,GAAI,CACA,MAAMM,EAAaN,EAAS,aAAa,EACrCM,IAAe,OACfD,EAAoB9C,EAAwB+C,CAAU,EAE9D,OACOvB,EAAO,CACVqB,EAA2B,iBAAkBrB,CAAK,CACtD,CAEJ,IAAIwB,EACJ,GAAI,OAAOP,EAAS,sBAAyB,WACzC,GAAI,CACAO,EAA4BhD,EAAwByC,EAAS,qBAAqB,CAAC,CACvF,OACOjB,EAAO,CACVqB,EAA2B,yBAA0BrB,CAAK,CAC9D,CAEJ,MAAO,CACH,GAAAT,EACA,MAAOf,EAAwBwC,CAAK,EACpC,SAAU,CACN,kBAAmBxC,EAAwByC,EAAS,iBAAiB,EACrE,eAAgBzC,EAAwByC,EAAS,cAAc,EAC/D,WAAAE,EACA,mBAAoBC,EACpB,UAAWE,EACX,kBAAmBE,CACvB,EACA,KAAAN,EACA,uBAAwBJ,EAAW,0BAA0B,EAC7D,wBAAyBJ,EAA0BI,EAAW,uBAAuB,CACzF,CACJ,CACA,SAASO,EAA2BI,EAAY5B,EAAO,CACnD,QAAQ,KAAK,yFAAyF4B,CAAU;AAAA,EAA6C5B,CAAK,CACtK,CAEA,SAAS6B,GAAkC,CACvC,GAAI,CAACtC,EAAwB,EACzB,OAAO,IAAI,QAASuC,GAAYA,EAAQ,EAAK,CAAC,EAElD,MAAMC,EAA4B,OAC7B,oBACL,OAAIA,EAA0B,kCAAoC,OACvD,IAAI,QAASD,GAAYA,EAAQ,EAAK,CAAC,EAE3CC,EAA0B,gCAAgC,CACrE,CAEA,SAASC,EAA4B,CAAE,MAAA7B,EAAO,QAAAC,CAAS,EAAG,CACtD,KAAM,CAAE,UAAAC,CAAU,EAAID,EACtB,GAAI,CAACC,EACD,MAAM,MAAM,iDAAiD,EAEjE,GAAIF,EAAM,OAAS,cACf,GAAIC,EAAQ,kBAAkB,YAC1B,OAAO,IAAIP,EAAc,CACrB,QAAS,mDACT,KAAM,yBACN,MAAOM,CACX,CAAC,MAGJ,IAAIA,EAAM,OAAS,kBACpB,OAAO,IAAIN,EAAc,CACrB,QAASM,EAAM,QACf,KAAM,uCACN,MAAOA,CACX,CAAC,EAEA,GAAIA,EAAM,OAAS,gBAAiB,CACrC,MAAMI,EAAkB,OAAO,SAAS,SACxC,GAAKZ,EAAcY,CAAe,GAO7B,GAAIF,EAAU,OAASE,EACxB,OAAO,IAAIV,EAAc,CACrB,QAAS,cAAcQ,EAAU,IAAI,+BACrC,KAAM,sBACN,MAAOF,CACX,CAAC,MAXD,QAAO,IAAIN,EAAc,CACrB,QAAS,GAAG,OAAO,SAAS,QAAQ,wBACpC,KAAM,uBACN,MAAOM,CACX,CAAC,CAST,SACSA,EAAM,OAAS,eACpB,OAAO,IAAIN,EAAc,CACrB,QAAS,+GACT,KAAM,oCACN,MAAOM,CACX,CAAC,EAEL,OAAOA,CACX,CAEA,eAAe8B,EAAoBjB,EAAakB,EAAqB,GAAO,CACxE,GAAI,CAAC3C,EAAwB,EACzB,MAAM,IAAI,MAAM,2CAA2C,EAE/D,IAAI4C,EACAnB,EAAY,kBAAkB,SAAW,IACzCmB,EAAmBnB,EAAY,kBAAkB,IAAIxB,CAA+B,GAExF,MAAMa,EAAY,CACd,GAAGW,EACH,UAAWhC,EAAwBgC,EAAY,SAAS,EACxD,iBAAAmB,CACJ,EACM/B,EAAU,CAAC,EACjB,GAAI8B,EAAoB,CACpB,GAAI,CAAE,MAAML,EAAgC,EACxC,MAAM,MAAM,4CAA4C,EAG5D,GADuB,SAAS,iBAAiB,iCAAiC,EAC/D,OAAS,EACxB,MAAM,MAAM,mGAAmG,EAEnHzB,EAAQ,UAAY,cACpBC,EAAU,iBAAmB,CAAC,CAClC,CACAD,EAAQ,UAAYC,EACpBD,EAAQ,OAASO,EAAqB,qBAAqB,EAC3D,IAAIM,EACJ,GAAI,CACAA,EAAc,MAAM,UAAU,YAAY,IAAIb,CAAO,CACzD,OACOc,EAAK,CACR,MAAMc,EAA4B,CAAE,MAAOd,EAAK,QAAAd,CAAQ,CAAC,CAC7D,CACA,GAAI,CAACa,EACD,MAAM,IAAI,MAAM,kCAAkC,EAEtD,KAAM,CAAE,GAAAvB,EAAI,MAAAyB,EAAO,SAAAC,EAAU,KAAAC,CAAK,EAAIJ,EACtC,IAAImB,EACJ,OAAIhB,EAAS,aACTgB,EAAazD,EAAwByC,EAAS,UAAU,GAErD,CACH,GAAA1B,EACA,MAAOf,EAAwBwC,CAAK,EACpC,SAAU,CACN,kBAAmBxC,EAAwByC,EAAS,iBAAiB,EACrE,eAAgBzC,EAAwByC,EAAS,cAAc,EAC/D,UAAWzC,EAAwByC,EAAS,SAAS,EACrD,WAAAgB,CACJ,EACA,KAAAf,EACA,uBAAwBJ,EAAW,0BAA0B,EAC7D,wBAAyBJ,EAA0BI,EAAW,uBAAuB,CACzF,CACJ,CAEA,SAASoB,GAAmC,CACxC,OAAK9C,EAAwB,EAGtB,oBAAoB,8CAA8C,EAF9D,IAAI,QAASuC,GAAYA,EAAQ,EAAK,CAAC,CAGtD,EChXG,gBAAkB,CACjB,MAAMQ,EAAyB,8BACzBC,EACJ,sCACIC,EAA4C,UAC5CC,EAA8B,mCAC9BC,EACJ,4CACIC,EACJ,0CAEIC,EAA2B,kCAC3BC,EAA8B,qCAC9BC,EAA4B,mCAElC,eAAeC,EAAqBtE,EAAgB,CAClD,GAAI,CAACA,EAAO,+BACV,OAIF,MAAMuE,EAAa,SAAS,cAC1BvE,EAAO,8BAA8B,EAGvC,GAAI,CAACuE,EAAY,CACf,QAAQ,MACN,4CAA4CvE,EAAO,8BAA8B,sCAAsC,EAEzH,MACF,CAGA,MAAMwE,EACJD,EAAW,aAAa,cAAc,GAAK,GAC7CA,EAAW,aACT,eAEAC,EAA6B,WAAW,EAG1CD,EAAW,cACT,IAAI,YAAYJ,EAA0B,CACxC,OAAQ,CAAE,aAAc,EAAK,EAC7B,QAAS,E,CACV,CAAC,EAIJ,MAAMxB,EAAW,MAAM,MAAM3C,EAAO,uBAAwB,CAC1D,OAAQ,OACR,YAAa,cACb,QAAS,CACP,cAAeA,EAAO,UACtB,OAAQ,kB,EAEX,EAED,GAAI,CAAC2C,EAAS,GAAI,CAChB,QAAQ,MACN,iEAAiE,EAEnE4B,EAAW,cACT,IAAI,YAAYF,EAA2B,CACzC,OAAQ,CACN,aAAc,GACd,SAAA1B,C,EAEF,QAAS,E,CACV,CAAC,EAEJ,MACF,CAEA,IAAI8B,EACJ,GAAI,CAEFA,EAAU,MAAMjB,EAAoB,MAAMb,EAAS,KAAK,EAAG,EAAI,CACjE,OAASjB,EAAgB,CACvB,QAAQ,MACN,yDACAA,CAAK,EAEP6C,EAAW,cACT,IAAI,YAAYF,EAA2B,CACzC,OAAQ,CACN,aAAc,GACd,MAAA3C,C,EAEF,QAAS,E,CACV,CAAC,EAEJ,MACF,CAEA,MAAMgD,EAAO,SAAS,cACpB,oBAAoB,EAEtB,IAAIC,EAA4B3E,EAAO,0BACnC0E,GAAQA,EAAK,QACfC,GAA6B,SAAS,mBAAmBD,EAAK,KAAK,CAAC,IAItE,MAAME,EAAmB,MAAM,MAAMD,EAA2B,CAC9D,OAAQ,OACR,QAAS,CACP,eAAgB,mBAChB,cAAe3E,EAAO,S,EAExB,YAAa,cACb,KAAM,KAAK,UAAUyE,CAAO,C,CAC7B,EAGD,GACE,CAACG,EAAiB,QACf,IAAI,cAAc,GACjB,SAAS,kBAAkB,EAC/B,CACAL,EAAW,cACT,IAAI,YAAYF,EAA2B,CACzC,OAAQ,CACN,aAAc,GACd,SAAUO,C,EAEZ,QAAS,E,CACV,CAAC,EAEJ,MAAM,QAAQ,+CAA+C,CAAC,EAC9D,MACF,CAGA,MAAMC,EAAmB,MAAMD,EAAiB,KAAK,EAGrD,GAAI,CAACA,EAAiB,IAAM,WAAYC,EAAkB,CACxDN,EAAW,cACT,IAAI,YAAYF,EAA2B,CACzC,OAAQ,CACN,aAAc,GACd,SAAUO,C,EAEZ,QAAS,E,CACV,CAAC,EAEJ,MAAMC,EAAiB,MAAM,EAC7B,MACF,CAGA,GAAIA,GAAoBA,EAAiB,GACvCN,EAAW,cACT,IAAI,YAAYH,EAA6B,CAC3C,OAAQ,CACN,aAAc,GACd,SAAUQ,C,EAEZ,QAAS,E,CACV,CAAC,EAGAC,EAAiB,eACnB,OAAO,SAAS,KAAOA,EAAiB,kBAErC,CACLN,EAAW,cACT,IAAI,YAAYF,EAA2B,CACzC,OAAQ,CACN,aAAc,GACd,SAAUO,C,EAEZ,QAAS,E,CACV,CAAC,EAEJ,MAAME,EACJD,EAAiB,OACjB,QAAQ,wCAAwC,EAClD,MAAMC,CAAG,CACX,CACF,CAEA,eAAeC,EACb/E,EAAc,CAEd,MAAMgF,EAAsB,SAAS,eACnCnB,CAAsB,EAExB,GAAI,CAACmB,EACH,OAGF,MAAMC,EACJD,EAAoB,aAAe,QAAQ,qBAAqB,EAElEA,EAAoB,iBAAiB,QAAS,MAAOE,GAAM,CACzDF,EAAoB,cAClB,IAAI,YAAYb,EAA0B,CACxC,OAAQ,CACN,aAAc,E,EAEhB,QAAS,E,CACV,CAAC,EAEJ,MAAMgB,EAAsB,CAC1B,eAAgB,GAChB,YAAa,QAAQ,cAAc,C,CACpC,EAGD,MAAMxC,EAAW,MAAM,MAAM3C,EAAO,uBAAwB,CAC1D,OAAQ,OACR,YAAa,cACb,QAAS,CACP,cAAeA,EAAO,UACtB,OAAQ,kB,EAEX,EAED,GAAI,CAAC2C,EAAS,GAAI,CAChB,MAAMwC,EAAsB,CAC1B,eAAgB,GAChB,YAAAF,EACA,aAAc,GACd,OAAQ,QACN,qEAAqE,C,CAExE,EACDD,EAAoB,cAClB,IAAI,YAAYX,EAA2B,CACzC,OAAQ,CACN,aAAc,GACd,SAAA1B,C,EAEF,QAAS,E,CACV,CAAC,EAEJ,MACF,CAEA,IAAI8B,EAEJ,GAAI,CACFA,EAAU,MAAMjB,EAAoB,MAAMb,EAAS,KAAK,CAAC,CAC3D,OAASjB,EAAgB,CACvB,GAAIA,aAAiB,OAASA,aAAiBN,EAAe,CAG5D,OAFA,QAAQ,MAAMM,CAAK,EAEXA,EAAM,KAAM,CAClB,IAAK,aACH,MAAMyD,EAAsB,CAC1B,eAAgB,GAChB,YAAAF,EACA,aAAc,GACd,OAAQ,QAAQ,uBAAuB,C,CACxC,EACD,MACF,IAAK,kBACH,MAAME,EAAsB,CAC1B,eAAgB,GAChB,YAAAF,EACA,aAAc,GACd,OAAQ,QAAQ,uCAAuC,C,CACxD,EACD,MACF,QACE,YAAME,EAAsB,CAC1B,eAAgB,GAChB,YAAAF,EACA,aAAc,GACd,OAAQ,QACN,iDAAiD,C,CAEpD,EACKvD,CACV,CACAsD,EAAoB,cAClB,IAAI,YAAYX,EAA2B,CACzC,OAAQ,CACN,aAAc,GACd,MAAA3C,C,EAEF,QAAS,E,CACV,CAAC,EAEJ,MACF,CACF,CAEA,MAAMyD,EAAsB,CAC1B,eAAgB,GAChB,YAAa,QAAQ,2BAA2B,C,CACjD,EAGD,MAAMT,EAAO,SAAS,cACpB,oBAAoB,EAEtB,IAAIC,EAA4B3E,EAAO,0BACnC0E,GAAQA,EAAK,QACfC,GAA6B,SAAS,mBAAmBD,EAAK,KAAK,CAAC,IAItE,MAAME,EAAmB,MAAM,MAAMD,EAA2B,CAC9D,OAAQ,OACR,QAAS,CACP,eAAgB,mBAChB,cAAe3E,EAAO,S,EAExB,YAAa,cACb,KAAM,KAAK,UAAUyE,CAAO,C,CAC7B,EAED,GACE,CAACG,EAAiB,QACf,IAAI,cAAc,GACjB,SAAS,kBAAkB,EAC/B,CACA,MAAMO,EAAsB,CAC1B,eAAgB,GAChB,YAAAF,EACA,aAAc,GACd,OAAQ,QACN,wDAAwD,C,CAE3D,EACDD,EAAoB,cAClB,IAAI,YAAYX,EAA2B,CACzC,OAAQ,CACN,aAAc,GACd,SAAUO,C,EAEZ,QAAS,E,CACV,CAAC,EAEJ,MACF,CAGA,MAAMC,EAAmB,MAAMD,EAAiB,KAAK,EAErD,GAAI,CAACA,EAAiB,GAAI,CACxB,MAAME,EACJD,EAAiB,QACjB,QAAQ,iDAAiD,EAC3D,MAAMM,EAAsB,CAC1B,eAAgB,GAChB,YAAAF,EACA,aAAc,GACd,OAAQH,C,CACT,EACDE,EAAoB,cAClB,IAAI,YAAYX,EAA2B,CACzC,OAAQ,CACN,aAAc,GACd,SAAUO,C,EAEZ,QAAS,E,CACV,CAAC,EAEJ,MACF,CAGA,GAAIC,GAAoBA,EAAiB,GACvC,MAAMM,EAAsB,CAC1B,eAAgB,GAChB,YAAAF,EACA,OAAQ,QAAQ,0BAA0B,C,CAC3C,EAEDD,EAAoB,cAClB,IAAI,YAAYZ,EAA6B,CAC3C,OAAQ,CACN,aAAc,GACd,SAAUQ,EACV,GAAIC,EAAiB,E,EAEvB,QAAS,E,CACV,CAAC,EAIAA,EAAiB,eACnB,OAAO,SAAS,KAAOA,EAAiB,kBAErC,CACL,MAAMC,EACJD,EAAiB,OACjB,QAAQ,wCAAwC,EAClD,MAAMM,EAAsB,CAC1B,eAAgB,GAChB,YAAAF,EACA,aAAc,GACd,OAAQH,C,CACT,CACH,CACF,CAAC,CACH,CAEA,eAAeK,EAAsBC,EAAY,CAC/C,MAAMC,EAAoB,SAAS,eACjCxB,CAAsB,EAExB,GAAI,CAACwB,EACH,OAEF,MAAMC,EAAoB,SAAS,eACjCxB,CAA8B,EAGhCuB,EAAkB,SAAWD,EAAM,eACnCC,EAAkB,YAAcD,EAAM,YAElCE,IAGEF,EAAM,QACRC,EAAkB,aAChB,mBACAvB,CAA8B,EAEhCwB,EAAkB,UAAU,IAC1BvB,CAAyC,EAE3CuB,EAAkB,YAAcF,EAAM,OACtCE,EAAkB,aAAa,YAAa,WAAW,EAEnDF,EAAM,cACRC,EAAkB,MAAM,IAG1BA,EAAkB,gBAAgB,kBAAkB,EACpDC,EAAkB,gBAAgB,WAAW,EAC7CA,EAAkB,UAAU,OAC1BvB,CAAyC,GAIjD,CAEA,eAAewB,EACbC,EAAgB,CAEhB,MAAMC,EAAqB,SAAS,eAClCzB,CAA2B,EAEvB0B,EAAoB,SAAS,eACjCxB,CAAkC,EAE9ByB,EAAmB,SAAS,eAChC1B,CAAiC,EAGnC,GAAI,CAACwB,EACH,MAAM,IAAI,MAAM,+BAA+B,EAGjD,GAAI,CAACC,EACH,MAAM,IAAI,MAAM,8BAA8B,EAGhD,GAAIF,EAAS,CACX,MAAMI,EAAQF,EAAkB,QAAQ,UAAU,EAAI,EACtDD,EAAmB,YAAYG,CAAK,CACtC,SACMD,EAAkB,CACpB,MAAMC,EAAQD,EAAiB,QAAQ,UAAU,EAAI,EACrDF,EAAmB,YAAYG,CAAK,CACtC,MACEH,EAAmB,OAAO,CAGhC,CAEA,MAAMzF,EAAS,MAAMD,EAAU,EAE7BC,EAAO,gCACN,MAAMoD,EAAgC,GAEvCkB,EAAqBtE,CAAM,EAGxBc,EAAwB,GAG3B,MAAMyE,EAA8B,EAAI,EACxCR,EAA+B/E,CAAM,GAHrC,MAAMuF,EAA8B,EAAK,CAK7C,GAAG,C", "sources": ["webpack://django-otp-webauthn/./src/utils.ts", "webpack://django-otp-webauthn/./node_modules/@simplewebauthn/browser/dist/bundle/index.js", "webpack://django-otp-webauthn/./src/auth.ts"], "sourcesContent": ["import { Config } from \"./types\";\n\n/**\n * Finds the config object in the DOM and deserializes it.\n * @returns Config the config object\n */\nexport async function getConfig(): Promise<Config> {\n  let config: Config | null = null;\n  const configElement = document.getElementById(\n    \"otp_webauthn_config\",\n  ) as HTMLScriptElement;\n\n  if (configElement) {\n    config = JSON.parse(configElement.innerText) as Config;\n    return Object.freeze(config);\n  }\n  throw new Error(\"otp_webauthn_config element not found\");\n}\n", "/* [@simplewebauthn/browser@10.0.0] */\nfunction bufferToBase64URLString(buffer) {\n    const bytes = new Uint8Array(buffer);\n    let str = '';\n    for (const charCode of bytes) {\n        str += String.fromCharCode(charCode);\n    }\n    const base64String = btoa(str);\n    return base64String.replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=/g, '');\n}\n\nfunction base64URLStringToBuffer(base64URLString) {\n    const base64 = base64URLString.replace(/-/g, '+').replace(/_/g, '/');\n    const padLength = (4 - (base64.length % 4)) % 4;\n    const padded = base64.padEnd(base64.length + padLength, '=');\n    const binary = atob(padded);\n    const buffer = new ArrayBuffer(binary.length);\n    const bytes = new Uint8Array(buffer);\n    for (let i = 0; i < binary.length; i++) {\n        bytes[i] = binary.charCodeAt(i);\n    }\n    return buffer;\n}\n\nfunction browserSupportsWebAuthn() {\n    return (window?.PublicKeyCredential !== undefined &&\n        typeof window.PublicKeyCredential === 'function');\n}\n\nfunction toPublicKeyCredentialDescriptor(descriptor) {\n    const { id } = descriptor;\n    return {\n        ...descriptor,\n        id: base64URLStringToBuffer(id),\n        transports: descriptor.transports,\n    };\n}\n\nfunction isValidDomain(hostname) {\n    return (hostname === 'localhost' ||\n        /^([a-z0-9]+(-[a-z0-9]+)*\\.)+[a-z]{2,}$/i.test(hostname));\n}\n\nclass WebAuthnError extends Error {\n    constructor({ message, code, cause, name, }) {\n        super(message, { cause });\n        this.name = name ?? cause.name;\n        this.code = code;\n    }\n}\n\nfunction identifyRegistrationError({ error, options, }) {\n    const { publicKey } = options;\n    if (!publicKey) {\n        throw Error('options was missing required publicKey property');\n    }\n    if (error.name === 'AbortError') {\n        if (options.signal instanceof AbortSignal) {\n            return new WebAuthnError({\n                message: 'Registration ceremony was sent an abort signal',\n                code: 'ERROR_CEREMONY_ABORTED',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'ConstraintError') {\n        if (publicKey.authenticatorSelection?.requireResidentKey === true) {\n            return new WebAuthnError({\n                message: 'Discoverable credentials were required but no available authenticator supported it',\n                code: 'ERROR_AUTHENTICATOR_MISSING_DISCOVERABLE_CREDENTIAL_SUPPORT',\n                cause: error,\n            });\n        }\n        else if (publicKey.authenticatorSelection?.userVerification === 'required') {\n            return new WebAuthnError({\n                message: 'User verification was required but no available authenticator supported it',\n                code: 'ERROR_AUTHENTICATOR_MISSING_USER_VERIFICATION_SUPPORT',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'InvalidStateError') {\n        return new WebAuthnError({\n            message: 'The authenticator was previously registered',\n            code: 'ERROR_AUTHENTICATOR_PREVIOUSLY_REGISTERED',\n            cause: error,\n        });\n    }\n    else if (error.name === 'NotAllowedError') {\n        return new WebAuthnError({\n            message: error.message,\n            code: 'ERROR_PASSTHROUGH_SEE_CAUSE_PROPERTY',\n            cause: error,\n        });\n    }\n    else if (error.name === 'NotSupportedError') {\n        const validPubKeyCredParams = publicKey.pubKeyCredParams.filter((param) => param.type === 'public-key');\n        if (validPubKeyCredParams.length === 0) {\n            return new WebAuthnError({\n                message: 'No entry in pubKeyCredParams was of type \"public-key\"',\n                code: 'ERROR_MALFORMED_PUBKEYCREDPARAMS',\n                cause: error,\n            });\n        }\n        return new WebAuthnError({\n            message: 'No available authenticator supported any of the specified pubKeyCredParams algorithms',\n            code: 'ERROR_AUTHENTICATOR_NO_SUPPORTED_PUBKEYCREDPARAMS_ALG',\n            cause: error,\n        });\n    }\n    else if (error.name === 'SecurityError') {\n        const effectiveDomain = window.location.hostname;\n        if (!isValidDomain(effectiveDomain)) {\n            return new WebAuthnError({\n                message: `${window.location.hostname} is an invalid domain`,\n                code: 'ERROR_INVALID_DOMAIN',\n                cause: error,\n            });\n        }\n        else if (publicKey.rp.id !== effectiveDomain) {\n            return new WebAuthnError({\n                message: `The RP ID \"${publicKey.rp.id}\" is invalid for this domain`,\n                code: 'ERROR_INVALID_RP_ID',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'TypeError') {\n        if (publicKey.user.id.byteLength < 1 || publicKey.user.id.byteLength > 64) {\n            return new WebAuthnError({\n                message: 'User ID was not between 1 and 64 characters',\n                code: 'ERROR_INVALID_USER_ID_LENGTH',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'UnknownError') {\n        return new WebAuthnError({\n            message: 'The authenticator was unable to process the specified options, or could not create a new credential',\n            code: 'ERROR_AUTHENTICATOR_GENERAL_ERROR',\n            cause: error,\n        });\n    }\n    return error;\n}\n\nclass BaseWebAuthnAbortService {\n    createNewAbortSignal() {\n        if (this.controller) {\n            const abortError = new Error('Cancelling existing WebAuthn API call for new one');\n            abortError.name = 'AbortError';\n            this.controller.abort(abortError);\n        }\n        const newController = new AbortController();\n        this.controller = newController;\n        return newController.signal;\n    }\n    cancelCeremony() {\n        if (this.controller) {\n            const abortError = new Error('Manually cancelling existing WebAuthn API call');\n            abortError.name = 'AbortError';\n            this.controller.abort(abortError);\n            this.controller = undefined;\n        }\n    }\n}\nconst WebAuthnAbortService = new BaseWebAuthnAbortService();\n\nconst attachments = ['cross-platform', 'platform'];\nfunction toAuthenticatorAttachment(attachment) {\n    if (!attachment) {\n        return;\n    }\n    if (attachments.indexOf(attachment) < 0) {\n        return;\n    }\n    return attachment;\n}\n\nasync function startRegistration(optionsJSON) {\n    if (!browserSupportsWebAuthn()) {\n        throw new Error('WebAuthn is not supported in this browser');\n    }\n    const publicKey = {\n        ...optionsJSON,\n        challenge: base64URLStringToBuffer(optionsJSON.challenge),\n        user: {\n            ...optionsJSON.user,\n            id: base64URLStringToBuffer(optionsJSON.user.id),\n        },\n        excludeCredentials: optionsJSON.excludeCredentials?.map(toPublicKeyCredentialDescriptor),\n    };\n    const options = { publicKey };\n    options.signal = WebAuthnAbortService.createNewAbortSignal();\n    let credential;\n    try {\n        credential = (await navigator.credentials.create(options));\n    }\n    catch (err) {\n        throw identifyRegistrationError({ error: err, options });\n    }\n    if (!credential) {\n        throw new Error('Registration was not completed');\n    }\n    const { id, rawId, response, type } = credential;\n    let transports = undefined;\n    if (typeof response.getTransports === 'function') {\n        transports = response.getTransports();\n    }\n    let responsePublicKeyAlgorithm = undefined;\n    if (typeof response.getPublicKeyAlgorithm === 'function') {\n        try {\n            responsePublicKeyAlgorithm = response.getPublicKeyAlgorithm();\n        }\n        catch (error) {\n            warnOnBrokenImplementation('getPublicKeyAlgorithm()', error);\n        }\n    }\n    let responsePublicKey = undefined;\n    if (typeof response.getPublicKey === 'function') {\n        try {\n            const _publicKey = response.getPublicKey();\n            if (_publicKey !== null) {\n                responsePublicKey = bufferToBase64URLString(_publicKey);\n            }\n        }\n        catch (error) {\n            warnOnBrokenImplementation('getPublicKey()', error);\n        }\n    }\n    let responseAuthenticatorData;\n    if (typeof response.getAuthenticatorData === 'function') {\n        try {\n            responseAuthenticatorData = bufferToBase64URLString(response.getAuthenticatorData());\n        }\n        catch (error) {\n            warnOnBrokenImplementation('getAuthenticatorData()', error);\n        }\n    }\n    return {\n        id,\n        rawId: bufferToBase64URLString(rawId),\n        response: {\n            attestationObject: bufferToBase64URLString(response.attestationObject),\n            clientDataJSON: bufferToBase64URLString(response.clientDataJSON),\n            transports,\n            publicKeyAlgorithm: responsePublicKeyAlgorithm,\n            publicKey: responsePublicKey,\n            authenticatorData: responseAuthenticatorData,\n        },\n        type,\n        clientExtensionResults: credential.getClientExtensionResults(),\n        authenticatorAttachment: toAuthenticatorAttachment(credential.authenticatorAttachment),\n    };\n}\nfunction warnOnBrokenImplementation(methodName, cause) {\n    console.warn(`The browser extension that intercepted this WebAuthn API call incorrectly implemented ${methodName}. You should report this error to them.\\n`, cause);\n}\n\nfunction browserSupportsWebAuthnAutofill() {\n    if (!browserSupportsWebAuthn()) {\n        return new Promise((resolve) => resolve(false));\n    }\n    const globalPublicKeyCredential = window\n        .PublicKeyCredential;\n    if (globalPublicKeyCredential.isConditionalMediationAvailable === undefined) {\n        return new Promise((resolve) => resolve(false));\n    }\n    return globalPublicKeyCredential.isConditionalMediationAvailable();\n}\n\nfunction identifyAuthenticationError({ error, options, }) {\n    const { publicKey } = options;\n    if (!publicKey) {\n        throw Error('options was missing required publicKey property');\n    }\n    if (error.name === 'AbortError') {\n        if (options.signal instanceof AbortSignal) {\n            return new WebAuthnError({\n                message: 'Authentication ceremony was sent an abort signal',\n                code: 'ERROR_CEREMONY_ABORTED',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'NotAllowedError') {\n        return new WebAuthnError({\n            message: error.message,\n            code: 'ERROR_PASSTHROUGH_SEE_CAUSE_PROPERTY',\n            cause: error,\n        });\n    }\n    else if (error.name === 'SecurityError') {\n        const effectiveDomain = window.location.hostname;\n        if (!isValidDomain(effectiveDomain)) {\n            return new WebAuthnError({\n                message: `${window.location.hostname} is an invalid domain`,\n                code: 'ERROR_INVALID_DOMAIN',\n                cause: error,\n            });\n        }\n        else if (publicKey.rpId !== effectiveDomain) {\n            return new WebAuthnError({\n                message: `The RP ID \"${publicKey.rpId}\" is invalid for this domain`,\n                code: 'ERROR_INVALID_RP_ID',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'UnknownError') {\n        return new WebAuthnError({\n            message: 'The authenticator was unable to process the specified options, or could not create a new assertion signature',\n            code: 'ERROR_AUTHENTICATOR_GENERAL_ERROR',\n            cause: error,\n        });\n    }\n    return error;\n}\n\nasync function startAuthentication(optionsJSON, useBrowserAutofill = false) {\n    if (!browserSupportsWebAuthn()) {\n        throw new Error('WebAuthn is not supported in this browser');\n    }\n    let allowCredentials;\n    if (optionsJSON.allowCredentials?.length !== 0) {\n        allowCredentials = optionsJSON.allowCredentials?.map(toPublicKeyCredentialDescriptor);\n    }\n    const publicKey = {\n        ...optionsJSON,\n        challenge: base64URLStringToBuffer(optionsJSON.challenge),\n        allowCredentials,\n    };\n    const options = {};\n    if (useBrowserAutofill) {\n        if (!(await browserSupportsWebAuthnAutofill())) {\n            throw Error('Browser does not support WebAuthn autofill');\n        }\n        const eligibleInputs = document.querySelectorAll(\"input[autocomplete$='webauthn']\");\n        if (eligibleInputs.length < 1) {\n            throw Error('No <input> with \"webauthn\" as the only or last value in its `autocomplete` attribute was detected');\n        }\n        options.mediation = 'conditional';\n        publicKey.allowCredentials = [];\n    }\n    options.publicKey = publicKey;\n    options.signal = WebAuthnAbortService.createNewAbortSignal();\n    let credential;\n    try {\n        credential = (await navigator.credentials.get(options));\n    }\n    catch (err) {\n        throw identifyAuthenticationError({ error: err, options });\n    }\n    if (!credential) {\n        throw new Error('Authentication was not completed');\n    }\n    const { id, rawId, response, type } = credential;\n    let userHandle = undefined;\n    if (response.userHandle) {\n        userHandle = bufferToBase64URLString(response.userHandle);\n    }\n    return {\n        id,\n        rawId: bufferToBase64URLString(rawId),\n        response: {\n            authenticatorData: bufferToBase64URLString(response.authenticatorData),\n            clientDataJSON: bufferToBase64URLString(response.clientDataJSON),\n            signature: bufferToBase64URLString(response.signature),\n            userHandle,\n        },\n        type,\n        clientExtensionResults: credential.getClientExtensionResults(),\n        authenticatorAttachment: toAuthenticatorAttachment(credential.authenticatorAttachment),\n    };\n}\n\nfunction platformAuthenticatorIsAvailable() {\n    if (!browserSupportsWebAuthn()) {\n        return new Promise((resolve) => resolve(false));\n    }\n    return PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();\n}\n\nexport { WebAuthnAbortService, WebAuthnError, base64URLStringToBuffer, browserSupportsWebAuthn, browserSupportsWebAuthnAutofill, bufferToBase64URLString, platformAuthenticatorIsAvailable, startAuthentication, startRegistration };\n", "import { State, Config } from \"./types\";\nimport { getConfig } from \"./utils\";\nimport {\n  browserSupportsWebAuthn,\n  browserSupportsWebAuthnAutofill,\n  startAuthentication,\n  WebAuthnError,\n} from \"@simplewebauthn/browser\";\n\n/**\n * This function is immediately invoked and sets up the registration button\n */\n(() =>\n  (async function () {\n    const VERIFICATION_BUTTON_ID = \"passkey-verification-button\";\n    const VERIFICATION_STATUS_MESSAGE_ID =\n      \"passkey-verification-status-message\";\n    const VERIFICATION_STATUS_MESSAGE_VISIBLE_CLASS = \"visible\";\n    const VERIFICATION_PLACEHOLDER_ID = \"passkey-verification-placeholder\";\n    const VERIFICATION_FALLBACK_TEMPLATE_ID =\n      \"passkey-verification-unavailable-template\";\n    const VERIFICATION_AVAILABLE_TEMPLATE_ID =\n      \"passkey-verification-available-template\";\n\n    const EVENT_VERIFICATION_START = \"otp_webauthn.verification_start\";\n    const EVENT_VERIFICATION_COMPLETE = \"otp_webauthn.verification_complete\";\n    const EVENT_VERIFICATION_FAILED = \"otp_webauthn.verification_failed\";\n\n    async function setupPasskeyAutofill(config: Config) {\n      if (!config.autocompleteLoginFieldSelector) {\n        return;\n      }\n\n      // Find the login field\n      const loginField = document.querySelector(\n        config.autocompleteLoginFieldSelector,\n      ) as HTMLInputElement;\n\n      if (!loginField) {\n        console.error(\n          `Could not find login field with selector ${config.autocompleteLoginFieldSelector}. WebAuthn autofill cannot continue.`,\n        );\n        return;\n      }\n\n      // Add \"webauthn\" to the autocomplete attribute, necessary for the browser to trigger the autofill UI\n      const originalAutocompleteString =\n        loginField.getAttribute(\"autocomplete\") || \"\";\n      loginField.setAttribute(\n        \"autocomplete\",\n        // Order of the values is important; webauthn must be the last value\n        originalAutocompleteString + \" webauthn\",\n      );\n\n      loginField.dispatchEvent(\n        new CustomEvent(EVENT_VERIFICATION_START, {\n          detail: { fromAutofill: true },\n          bubbles: true,\n        }),\n      );\n\n      // Begin authentication: fetch options and challenge\n      const response = await fetch(config.beginAuthenticationUrl, {\n        method: \"POST\",\n        credentials: \"same-origin\",\n        headers: {\n          \"X-CSRFToken\": config.csrfToken,\n          Accept: \"application/json\",\n        },\n      });\n\n      if (!response.ok) {\n        console.error(\n          \"Unable to fetch options from server. Will not attempt autofill.\",\n        );\n        loginField.dispatchEvent(\n          new CustomEvent(EVENT_VERIFICATION_FAILED, {\n            detail: {\n              fromAutofill: true,\n              response,\n            },\n            bubbles: true,\n          }),\n        );\n        return;\n      }\n\n      let attResp;\n      try {\n        // Important: the second argument to startAuthentication is to call for the browser autofill UI\n        attResp = await startAuthentication(await response.json(), true);\n      } catch (error: unknown) {\n        console.error(\n          \"Got error during the webauthn credential autofill call\",\n          error,\n        );\n        loginField.dispatchEvent(\n          new CustomEvent(EVENT_VERIFICATION_FAILED, {\n            detail: {\n              fromAutofill: true,\n              error,\n            },\n            bubbles: true,\n          }),\n        );\n        return;\n      }\n      // Find out if there is a hidden 'next' field on the page\n      const next = document.querySelector(\n        \"input[name='next']\",\n      ) as HTMLInputElement;\n      let completeAuthenticationUrl = config.completeAuthenticationUrl;\n      if (next && next.value) {\n        completeAuthenticationUrl += `?next=${encodeURIComponent(next.value)}`;\n      }\n\n      // Complete\n      const verificationResp = await fetch(completeAuthenticationUrl, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"X-CSRFToken\": config.csrfToken,\n        },\n        credentials: \"same-origin\",\n        body: JSON.stringify(attResp),\n      });\n\n      // Check if the verification response is not JSON, which points to a server error\n      if (\n        !verificationResp.headers\n          .get(\"content-type\")\n          ?.includes(\"application/json\")\n      ) {\n        loginField.dispatchEvent(\n          new CustomEvent(EVENT_VERIFICATION_FAILED, {\n            detail: {\n              fromAutofill: true,\n              response: verificationResp,\n            },\n            bubbles: true,\n          }),\n        );\n        alert(gettext(\"Verification failed. A server error occurred.\"));\n        return;\n      }\n\n      // Wait for the results of verification\n      const verificationJSON = await verificationResp.json();\n\n      // Handle failed verification\n      if (!verificationResp.ok && \"detail\" in verificationJSON) {\n        loginField.dispatchEvent(\n          new CustomEvent(EVENT_VERIFICATION_FAILED, {\n            detail: {\n              fromAutofill: true,\n              response: verificationResp,\n            },\n            bubbles: true,\n          }),\n        );\n        alert(verificationJSON.detail);\n        return;\n      }\n\n      // Show UI appropriate for the `verified` status\n      if (verificationJSON && verificationJSON.id) {\n        loginField.dispatchEvent(\n          new CustomEvent(EVENT_VERIFICATION_COMPLETE, {\n            detail: {\n              fromAutofill: true,\n              response: verificationResp,\n            },\n            bubbles: true,\n          }),\n        );\n        // Redirect to the next page\n        if (verificationJSON.redirect_url) {\n          window.location.href = verificationJSON.redirect_url;\n        }\n      } else {\n        loginField.dispatchEvent(\n          new CustomEvent(EVENT_VERIFICATION_FAILED, {\n            detail: {\n              fromAutofill: true,\n              response: verificationResp,\n            },\n            bubbles: true,\n          }),\n        );\n        const msg =\n          verificationJSON.error ||\n          gettext(\"An error occurred during verification.\");\n        alert(msg);\n      }\n    }\n\n    async function setupPasskeyVerificationButton(\n      config: Config,\n    ): Promise<void> {\n      const passkeyVerifyButton = document.getElementById(\n        VERIFICATION_BUTTON_ID,\n      );\n      if (!passkeyVerifyButton) {\n        return;\n      }\n\n      const buttonLabel =\n        passkeyVerifyButton.textContent || gettext(\"Verify with Passkey\");\n\n      passkeyVerifyButton.addEventListener(\"click\", async (_) => {\n        passkeyVerifyButton.dispatchEvent(\n          new CustomEvent(EVENT_VERIFICATION_START, {\n            detail: {\n              fromAutofill: false,\n            },\n            bubbles: true,\n          }),\n        );\n        await setPasskeyVerifyState({\n          buttonDisabled: true,\n          buttonLabel: gettext(\"Verifying...\"),\n        });\n\n        // Begin authentication: fetch options and challenge\n        const response = await fetch(config.beginAuthenticationUrl, {\n          method: \"POST\",\n          credentials: \"same-origin\",\n          headers: {\n            \"X-CSRFToken\": config.csrfToken,\n            Accept: \"application/json\",\n          },\n        });\n\n        if (!response.ok) {\n          await setPasskeyVerifyState({\n            buttonDisabled: false,\n            buttonLabel,\n            requestFocus: true,\n            status: gettext(\n              \"Verification failed. Could not retrieve parameters from the server.\",\n            ),\n          });\n          passkeyVerifyButton.dispatchEvent(\n            new CustomEvent(EVENT_VERIFICATION_FAILED, {\n              detail: {\n                fromAutofill: false,\n                response,\n              },\n              bubbles: true,\n            }),\n          );\n          return;\n        }\n\n        let attResp;\n\n        try {\n          attResp = await startAuthentication(await response.json());\n        } catch (error: unknown) {\n          if (error instanceof Error || error instanceof WebAuthnError) {\n            console.error(error);\n\n            switch (error.name) {\n              case \"AbortError\":\n                await setPasskeyVerifyState({\n                  buttonDisabled: false,\n                  buttonLabel,\n                  requestFocus: true,\n                  status: gettext(\"Verification aborted.\"),\n                });\n                break;\n              case \"NotAllowedError\":\n                await setPasskeyVerifyState({\n                  buttonDisabled: false,\n                  buttonLabel,\n                  requestFocus: true,\n                  status: gettext(\"Verification canceled or not allowed.\"),\n                });\n                break;\n              default:\n                await setPasskeyVerifyState({\n                  buttonDisabled: false,\n                  buttonLabel,\n                  requestFocus: true,\n                  status: gettext(\n                    \"Verification failed. An unknown error occurred.\",\n                  ),\n                });\n                throw error;\n            }\n            passkeyVerifyButton.dispatchEvent(\n              new CustomEvent(EVENT_VERIFICATION_FAILED, {\n                detail: {\n                  fromAutofill: false,\n                  error,\n                },\n                bubbles: true,\n              }),\n            );\n            return;\n          }\n        }\n\n        await setPasskeyVerifyState({\n          buttonDisabled: true,\n          buttonLabel: gettext(\"Finishing verification...\"),\n        });\n\n        // Find out if there is a hidden 'next' field on the page\n        const next = document.querySelector(\n          \"input[name='next']\",\n        ) as HTMLInputElement;\n        let completeAuthenticationUrl = config.completeAuthenticationUrl;\n        if (next && next.value) {\n          completeAuthenticationUrl += `?next=${encodeURIComponent(next.value)}`;\n        }\n\n        // Complete\n        const verificationResp = await fetch(completeAuthenticationUrl, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            \"X-CSRFToken\": config.csrfToken,\n          },\n          credentials: \"same-origin\",\n          body: JSON.stringify(attResp),\n        });\n\n        if (\n          !verificationResp.headers\n            .get(\"content-type\")\n            ?.includes(\"application/json\")\n        ) {\n          await setPasskeyVerifyState({\n            buttonDisabled: false,\n            buttonLabel,\n            requestFocus: true,\n            status: gettext(\n              \"Verification failed. An unknown server error occurred.\",\n            ),\n          });\n          passkeyVerifyButton.dispatchEvent(\n            new CustomEvent(EVENT_VERIFICATION_FAILED, {\n              detail: {\n                fromAutofill: false,\n                response: verificationResp,\n              },\n              bubbles: true,\n            }),\n          );\n          return;\n        }\n\n        // Wait for the results of verification\n        const verificationJSON = await verificationResp.json();\n\n        if (!verificationResp.ok) {\n          const msg =\n            verificationJSON.detail ||\n            gettext(\"Verification failed. An unknown error occurred.\");\n          await setPasskeyVerifyState({\n            buttonDisabled: false,\n            buttonLabel,\n            requestFocus: true,\n            status: msg,\n          });\n          passkeyVerifyButton.dispatchEvent(\n            new CustomEvent(EVENT_VERIFICATION_FAILED, {\n              detail: {\n                fromAutofill: false,\n                response: verificationResp,\n              },\n              bubbles: true,\n            }),\n          );\n          return;\n        }\n\n        // Show UI appropriate for the `verified` status\n        if (verificationJSON && verificationJSON.id) {\n          await setPasskeyVerifyState({\n            buttonDisabled: false,\n            buttonLabel,\n            status: gettext(\"Verification successful!\"),\n          });\n\n          passkeyVerifyButton.dispatchEvent(\n            new CustomEvent(EVENT_VERIFICATION_COMPLETE, {\n              detail: {\n                fromAutofill: false,\n                response: verificationResp,\n                id: verificationJSON.id,\n              },\n              bubbles: true,\n            }),\n          );\n\n          // Redirect to the next page\n          if (verificationJSON.redirect_url) {\n            window.location.href = verificationJSON.redirect_url;\n          }\n        } else {\n          const msg =\n            verificationJSON.error ||\n            gettext(\"An error occurred during verification.\");\n          await setPasskeyVerifyState({\n            buttonDisabled: false,\n            buttonLabel,\n            requestFocus: true,\n            status: msg,\n          });\n        }\n      });\n    }\n\n    async function setPasskeyVerifyState(state: State): Promise<void> {\n      const passkeyAuthButton = document.getElementById(\n        VERIFICATION_BUTTON_ID,\n      ) as HTMLButtonElement;\n      if (!passkeyAuthButton) {\n        return;\n      }\n      const passkeyStatusText = document.getElementById(\n        VERIFICATION_STATUS_MESSAGE_ID,\n      ) as HTMLElement;\n\n      passkeyAuthButton.disabled = state.buttonDisabled;\n      passkeyAuthButton.textContent = state.buttonLabel;\n\n      if (passkeyStatusText) {\n        // If there is a status message, we want to make sure screen readers\n        // announce it to the user for clarity as to what is happening.\n        if (state.status) {\n          passkeyAuthButton.setAttribute(\n            \"aria-describedby\",\n            VERIFICATION_STATUS_MESSAGE_ID,\n          );\n          passkeyStatusText.classList.add(\n            VERIFICATION_STATUS_MESSAGE_VISIBLE_CLASS,\n          );\n          passkeyStatusText.textContent = state.status;\n          passkeyStatusText.setAttribute(\"aria-live\", \"assertive\");\n\n          if (state.requestFocus) {\n            passkeyAuthButton.focus();\n          }\n        } else {\n          passkeyAuthButton.removeAttribute(\"aria-describedby\");\n          passkeyStatusText.removeAttribute(\"aria-live\");\n          passkeyStatusText.classList.remove(\n            VERIFICATION_STATUS_MESSAGE_VISIBLE_CLASS,\n          );\n        }\n      }\n    }\n\n    async function setPasskeyVerificationVisible(\n      visible: boolean,\n    ): Promise<void> {\n      const placeholderElement = document.getElementById(\n        VERIFICATION_PLACEHOLDER_ID,\n      );\n      const availableTemplate = document.getElementById(\n        VERIFICATION_AVAILABLE_TEMPLATE_ID,\n      ) as HTMLTemplateElement;\n      const fallbackTemplate = document.getElementById(\n        VERIFICATION_FALLBACK_TEMPLATE_ID,\n      ) as HTMLTemplateElement;\n\n      if (!placeholderElement) {\n        throw new Error(\"Placeholder element not found\");\n      }\n\n      if (!availableTemplate) {\n        throw new Error(\"Available template not found\");\n      }\n\n      if (visible) {\n        const clone = availableTemplate.content.cloneNode(true);\n        placeholderElement.replaceWith(clone);\n      } else {\n        if (fallbackTemplate) {\n          const clone = fallbackTemplate.content.cloneNode(true);\n          placeholderElement.replaceWith(clone);\n        } else {\n          placeholderElement.remove();\n        }\n      }\n    }\n\n    const config = await getConfig();\n    if (\n      config.autocompleteLoginFieldSelector &&\n      (await browserSupportsWebAuthnAutofill())\n    ) {\n      setupPasskeyAutofill(config);\n    }\n\n    if (!browserSupportsWebAuthn()) {\n      await setPasskeyVerificationVisible(false);\n    } else {\n      await setPasskeyVerificationVisible(true);\n      setupPasskeyVerificationButton(config);\n    }\n  })())();\n"], "names": ["getConfig", "config", "configElement", "bufferToBase64URLString", "buffer", "bytes", "str", "charCode", "base64URLStringToBuffer", "base64URLString", "base64", "<PERSON><PERSON><PERSON><PERSON>", "padded", "binary", "i", "browserSupportsWebAuthn", "toPublicKeyCredentialDescriptor", "descriptor", "id", "isValidDomain", "hostname", "WebAuthnError", "message", "code", "cause", "name", "identifyRegistrationError", "error", "options", "public<PERSON>ey", "param", "effectiveDomain", "BaseWebAuthnAbortService", "abortError", "newController", "WebAuthnAbortService", "attachments", "toAuthenticatorAttachment", "attachment", "startRegistration", "optionsJSON", "credential", "err", "rawId", "response", "type", "transports", "responsePublicKeyAlgorithm", "warnOnBrokenImplementation", "responsePublicKey", "_public<PERSON>ey", "responseAuthenticatorData", "methodName", "browserSupportsWebAuthnAutofill", "resolve", "globalPublicKeyCredential", "identifyAuthenticationError", "startAuthentication", "useBrowserAutofill", "allowCredentials", "userHandle", "platformAuthenticatorIsAvailable", "VERIFICATION_BUTTON_ID", "VERIFICATION_STATUS_MESSAGE_ID", "VERIFICATION_STATUS_MESSAGE_VISIBLE_CLASS", "VERIFICATION_PLACEHOLDER_ID", "VERIFICATION_FALLBACK_TEMPLATE_ID", "VERIFICATION_AVAILABLE_TEMPLATE_ID", "EVENT_VERIFICATION_START", "EVENT_VERIFICATION_COMPLETE", "EVENT_VERIFICATION_FAILED", "setupPasskeyAutofill", "loginField", "originalAutocompleteString", "attResp", "next", "completeAuthenticationUrl", "verificationResp", "verificationJSON", "msg", "setupPasskeyVerificationButton", "passkeyVerifyButton", "buttonLabel", "_", "setPasskeyVerifyState", "state", "passkeyAuthButton", "passkeyStatusText", "setPasskeyVerificationVisible", "visible", "placeholderElement", "availableTemplate", "fallbackTemplate", "clone"], "sourceRoot": ""}