!function(){"use strict";var I=function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e]},v=function(t,r){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t(r.apply(null,n))}},A=function(n){return function(){return n}},h=function(n){return n};function l(r){for(var o=[],n=1;n<arguments.length;n++)o[n-1]=arguments[n];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=o.concat(n);return r.apply(null,t)}}var n,e,t,r,o,i,u,a,S=function(t){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return!t.apply(null,n)}},c=function(n){return function(){throw new Error(n)}},s=function(n){return n()},f=A(!1),d=A(!0),m=function(e){return function(n){return function(n){if(null===n)return"null";var e=typeof n;return"object"===e&&Array.prototype.isPrototypeOf(n)?"array":"object"===e&&String.prototype.isPrototypeOf(n)?"string":e}(n)===e}},b=m("string"),g=m("object"),p=m("array"),y=m("boolean"),w=m("function"),x=m("number"),T=Object.prototype.hasOwnProperty,O=function(u){return function(){for(var n=new Array(arguments.length),e=0;e<n.length;e++)n[e]=arguments[e];if(0===n.length)throw new Error("Can't merge zero objects");for(var t={},r=0;r<n.length;r++){var o=n[r];for(var i in o)T.call(o,i)&&(t[i]=u(t[i],o[i]))}return t}},k=O(function(n,e){return g(n)&&g(e)?k(n,e):e}),C=O(function(n,e){return e}),E=f,D=d,M=function(){return B},B=(r={fold:function(n,e){return n()},is:E,isSome:E,isNone:D,getOr:t=function(n){return n},getOrThunk:e=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:function(){return null},getOrUndefined:function(){return undefined},or:t,orThunk:e,map:M,ap:M,each:function(){},bind:M,flatten:M,exists:E,forall:D,filter:M,equals:n=function(n){return n.isNone()},equals_:n,toArray:function(){return[]},toString:A("none()")},Object.freeze&&Object.freeze(r),r),R=function(t){var n=function(){return t},e=function(){return o},r=function(n){return n(t)},o={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:D,isNone:E,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:e,orThunk:e,map:function(n){return R(n(t))},ap:function(n){return n.fold(M,function(n){return R(n(t))})},each:function(n){n(t)},bind:r,flatten:n,exists:r,forall:r,filter:function(n){return n(t)?o:B},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(E,function(n){return e(t,n)})},toArray:function(){return[t]},toString:function(){return"some("+t+")"}};return o},F={some:R,none:M,from:function(n){return null===n||n===undefined?B:R(n)}},N=Object.keys,V=function(n,e){for(var t=N(n),r=0,o=t.length;r<o;r++){var i=t[r];e(n[i],i,n)}},H=function(n,r){return z(n,function(n,e,t){return{k:e,v:r(n,e,t)}})},z=function(r,o){var i={};return V(r,function(n,e){var t=o(n,e,r);i[t.k]=t.v}),i},j=function(n,t){var r=[];return V(n,function(n,e){r.push(t(n,e))}),r},L=A("touchstart"),U=A("touchmove"),P=A("touchend"),$=A("mousedown"),W=A("mousemove"),G=A("mouseup"),_=A("mouseover"),q=A("keydown"),Y=A("input"),K=A("change"),X=A("click"),J=A("transitionend"),Q=A("selectstart"),Z=function(t){var r,o=!1;return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return o||(o=!0,r=t.apply(null,n)),r}},nn=function(n,e){var t=function(n,e){for(var t=0;t<n.length;t++){var r=n[t];if(r.test(e))return r}return undefined}(n,e);if(!t)return{major:0,minor:0};var r=function(n){return Number(e.replace(t,"$"+n))};return tn(r(1),r(2))},en=function(){return tn(0,0)},tn=function(n,e){return{major:n,minor:e}},rn={nu:tn,detect:function(n,e){var t=String(e).toLowerCase();return 0===n.length?en():nn(n,t)},unknown:en},on="Firefox",un=function(n,e){return function(){return e===n}},an=function(n){var e=n.current;return{current:e,version:n.version,isEdge:un("Edge",e),isChrome:un("Chrome",e),isIE:un("IE",e),isOpera:un("Opera",e),isFirefox:un(on,e),isSafari:un("Safari",e)}},cn={unknown:function(){return an({current:undefined,version:rn.unknown()})},nu:an,edge:A("Edge"),chrome:A("Chrome"),ie:A("IE"),opera:A("Opera"),firefox:A(on),safari:A("Safari")},sn="Windows",fn="Android",ln="Solaris",dn="FreeBSD",mn=function(n,e){return function(){return e===n}},gn=function(n){var e=n.current;return{current:e,version:n.version,isWindows:mn(sn,e),isiOS:mn("iOS",e),isAndroid:mn(fn,e),isOSX:mn("OSX",e),isLinux:mn("Linux",e),isSolaris:mn(ln,e),isFreeBSD:mn(dn,e)}},vn={unknown:function(){return gn({current:undefined,version:rn.unknown()})},nu:gn,windows:A(sn),ios:A("iOS"),android:A(fn),linux:A("Linux"),osx:A("OSX"),solaris:A(ln),freebsd:A(dn)},pn=(o=Array.prototype.indexOf)===undefined?function(n,e){return kn(n,e)}:function(n,e){return o.call(n,e)},hn=function(n,e){return-1<pn(n,e)},bn=function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var i=n[o];r[o]=e(i,o,n)}return r},yn=function(n,e){for(var t=0,r=n.length;t<r;t++)e(n[t],t,n)},wn=function(n,e){for(var t=[],r=0,o=n.length;r<o;r++){var i=n[r];e(i,r,n)&&t.push(i)}return t},xn=function(n,e,t){return function(n,e){for(var t=n.length-1;0<=t;t--)e(n[t],t,n)}(n,function(n){t=e(t,n)}),t},Sn=function(n,e,t){return yn(n,function(n){t=e(t,n)}),t},Tn=function(n,e){for(var t=0,r=n.length;t<r;t++){var o=n[t];if(e(o,t,n))return F.some(o)}return F.none()},On=function(n,e){for(var t=0,r=n.length;t<r;t++)if(e(n[t],t,n))return F.some(t);return F.none()},kn=function(n,e){for(var t=0,r=n.length;t<r;++t)if(n[t]===e)return t;return-1},Cn=Array.prototype.push,En=function(n){for(var e=[],t=0,r=n.length;t<r;++t){if(!Array.prototype.isPrototypeOf(n[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+n);Cn.apply(e,n[t])}return e},Dn=function(n,e){var t=bn(n,e);return En(t)},In=function(n,e){for(var t=0,r=n.length;t<r;++t)if(!0!==e(n[t],t,n))return!1;return!0},An=Array.prototype.slice,Mn=function(n){var e=An.call(n,0);return e.reverse(),e},Bn=function(n){return[n]},Rn=(w(Array.from)&&Array.from,function(n,e){var t=String(e).toLowerCase();return Tn(n,function(n){return n.search(t)})}),Fn=function(n,t){return Rn(n,t).map(function(n){var e=rn.detect(n.versionRegexes,t);return{current:n.name,version:e}})},Nn=function(n,t){return Rn(n,t).map(function(n){var e=rn.detect(n.versionRegexes,t);return{current:n.name,version:e}})},Vn=function(n,e){return-1!==n.indexOf(e)},Hn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,zn=function(e){return function(n){return Vn(n,e)}},jn=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return Vn(n,"edge/")&&Vn(n,"chrome")&&Vn(n,"safari")&&Vn(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Hn],search:function(n){return Vn(n,"chrome")&&!Vn(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return Vn(n,"msie")||Vn(n,"trident")}},{name:"Opera",versionRegexes:[Hn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:zn("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:zn("firefox")},{name:"Safari",versionRegexes:[Hn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(Vn(n,"safari")||Vn(n,"mobile/"))&&Vn(n,"applewebkit")}}],Ln=[{name:"Windows",search:zn("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return Vn(n,"iphone")||Vn(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:zn("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:zn("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:zn("linux"),versionRegexes:[]},{name:"Solaris",search:zn("sunos"),versionRegexes:[]},{name:"FreeBSD",search:zn("freebsd"),versionRegexes:[]}],Un={browsers:A(jn),oses:A(Ln)},Pn=function(n){var e,t,r,o,i,u,a,c,s,f,l,d=Un.browsers(),m=Un.oses(),g=Fn(d,n).fold(cn.unknown,cn.nu),v=Nn(m,n).fold(vn.unknown,vn.nu);return{browser:g,os:v,deviceType:(t=g,r=n,o=(e=v).isiOS()&&!0===/ipad/i.test(r),i=e.isiOS()&&!o,u=e.isAndroid()&&3===e.version.major,a=e.isAndroid()&&4===e.version.major,c=o||u||a&&!0===/mobile/i.test(r),s=e.isiOS()||e.isAndroid(),f=s&&!c,l=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(r),{isiPad:A(o),isiPhone:A(i),isTablet:A(c),isPhone:A(f),isTouch:A(s),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:A(l)})}},$n={detect:Z(function(){var n=navigator.userAgent;return Pn(n)})},Wn={tap:A("alloy.tap")},Gn=A("alloy.focus"),_n=A("alloy.blur.post"),qn=A("alloy.receive"),Yn=A("alloy.execute"),Kn=A("alloy.focus.item"),Xn=Wn.tap,Jn=$n.detect().deviceType.isTouch()?Wn.tap:X,Qn=A("alloy.longpress"),Zn=A("alloy.system.init"),ne=A("alloy.system.scroll"),ee=A("alloy.system.attached"),te=A("alloy.system.detached"),re=function(n,e){ae(n,n.element(),e,{})},oe=function(n,e,t){ae(n,n.element(),e,t)},ie=function(n){re(n,Yn())},ue=function(n,e,t){ae(n,e,t,{})},ae=function(n,e,t,r){var o=k({target:e},r);n.getSystem().triggerEvent(t,e,H(o,A))},ce=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:A(n)}},se={fromHtml:function(n,e){var t=(e||document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||1<t.childNodes.length)throw console.error("HTML does not have a single root node",n),"HTML must have a single root node";return ce(t.childNodes[0])},fromTag:function(n,e){var t=(e||document).createElement(n);return ce(t)},fromText:function(n,e){var t=(e||document).createTextNode(n);return ce(t)},fromDom:ce,fromPoint:function(n,e,t){var r=n.dom();return F.from(r.elementFromPoint(e,t)).map(ce)}},fe=(Node.ATTRIBUTE_NODE,Node.CDATA_SECTION_NODE,Node.COMMENT_NODE,Node.DOCUMENT_NODE),le=(Node.DOCUMENT_TYPE_NODE,Node.DOCUMENT_FRAGMENT_NODE,Node.ELEMENT_NODE),de=Node.TEXT_NODE,me=(Node.PROCESSING_INSTRUCTION_NODE,Node.ENTITY_REFERENCE_NODE,Node.ENTITY_NODE,Node.NOTATION_NODE,function(n){return n.dom().nodeName.toLowerCase()}),ge=function(e){return function(n){return n.dom().nodeType===e}},ve=ge(le),pe=ge(de),he=function(n){var e=pe(n)?n.dom().parentNode:n.dom();return e!==undefined&&null!==e&&e.ownerDocument.body.contains(e)},be=Z(function(){return ye(se.fromDom(document))}),ye=function(n){var e=n.dom().body;if(null===e||e===undefined)throw"Body is not available yet";return se.fromDom(e)},we=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(e.length!==t.length)throw new Error('Wrong number of arguments to struct. Expected "['+e.length+']", got '+t.length+" arguments");var r={};return yn(e,function(n,e){r[n]=A(t[e])}),r}},xe=function(n){return n.slice(0).sort()},Se=function(n,e){throw new Error("All required keys ("+xe(n).join(", ")+") were not specified. Specified keys were: "+xe(e).join(", ")+".")},Te=function(n){throw new Error("Unsupported keys for object: "+xe(n).join(", "))},Oe=function(e,n){if(!p(n))throw new Error("The "+e+" fields must be an array. Was: "+n+".");yn(n,function(n){if(!b(n))throw new Error("The value "+n+" in the "+e+" fields was not a string.")})},ke=function(n){var t=xe(n);Tn(t,function(n,e){return e<t.length-1&&n===t[e+1]}).each(function(n){throw new Error("The field: "+n+" occurs more than once in the combined fields: ["+t.join(", ")+"].")})},Ce=function(o,i){var u=o.concat(i);if(0===u.length)throw new Error("You must specify at least one required or optional field.");return Oe("required",o),Oe("optional",i),ke(u),function(e){var t=N(e);In(o,function(n){return hn(t,n)})||Se(o,t);var n=wn(t,function(n){return!hn(u,n)});0<n.length&&Te(n);var r={};return yn(o,function(n){r[n]=A(e[n])}),yn(i,function(n){r[n]=A(Object.prototype.hasOwnProperty.call(e,n)?F.some(e[n]):F.none())}),r}},Ee="undefined"!=typeof window?window:Function("return this;")(),De=function(n,e){return function(n,e){for(var t=e!==undefined&&null!==e?e:Ee,r=0;r<n.length&&t!==undefined&&null!==t;++r)t=t[n[r]];return t}(n.split("."),e)},Ie={getOrDie:function(n,e){var t=De(n,e);if(t===undefined||null===t)throw n+" not available on this browser";return t}},Ae=le,Me=fe,Be=function(n,e){var t=n.dom();if(t.nodeType!==Ae)return!1;if(t.matches!==undefined)return t.matches(e);if(t.msMatchesSelector!==undefined)return t.msMatchesSelector(e);if(t.webkitMatchesSelector!==undefined)return t.webkitMatchesSelector(e);if(t.mozMatchesSelector!==undefined)return t.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")},Re=function(n){return n.nodeType!==Ae&&n.nodeType!==Me||0===n.childElementCount},Fe=function(n,e){var t=e===undefined?document:e.dom();return Re(t)?[]:bn(t.querySelectorAll(n),se.fromDom)},Ne=function(n,e){var t=e===undefined?document:e.dom();return Re(t)?F.none():F.from(t.querySelector(n)).map(se.fromDom)},Ve=function(n,e){return n.dom()===e.dom()},He=($n.detect().browser.isIE(),function(n){return se.fromDom(n.dom().ownerDocument)}),ze=function(n){var e=n.dom();return F.from(e.parentNode).map(se.fromDom)},je=function(n){var e=n.dom();return bn(e.childNodes,se.fromDom)},Le=function(n){return e=0,t=n.dom().childNodes,F.from(t[e]).map(se.fromDom);var e,t},Ue=(we("element","offset"),function(e,t){Le(e).fold(function(){Pe(e,t)},function(n){e.dom().insertBefore(t.dom(),n.dom())})}),Pe=function(n,e){n.dom().appendChild(e.dom())},$e=function(e,n){yn(n,function(n){Pe(e,n)})},We=function(n){n.dom().textContent="",yn(je(n),function(n){Ge(n)})},Ge=function(n){var e=n.dom();null!==e.parentNode&&e.parentNode.removeChild(e)},_e=function(n){re(n,te());var e=n.components();yn(e,_e)},qe=function(n){var e=n.components();yn(e,qe),re(n,ee())},Ye=function(n,e){Ke(n,e,Pe)},Ke=function(n,e,t){n.getSystem().addToWorld(e),t(n.element(),e.element()),he(n.element())&&qe(e),n.syncComponents()},Xe=function(n){_e(n),Ge(n.element()),n.getSystem().removeFromWorld(n)},Je=function(e){var n=ze(e.element()).bind(function(n){return e.getSystem().getByDom(n).fold(F.none,F.some)});Xe(e),n.each(function(n){n.syncComponents()})},Qe=function(t){return{is:function(n){return t===n},isValue:d,isError:f,getOr:A(t),getOrThunk:A(t),getOrDie:A(t),or:function(n){return Qe(t)},orThunk:function(n){return Qe(t)},fold:function(n,e){return e(t)},map:function(n){return Qe(n(t))},mapError:function(n){return Qe(t)},each:function(n){n(t)},bind:function(n){return n(t)},exists:function(n){return n(t)},forall:function(n){return n(t)},toOption:function(){return F.some(t)}}},Ze=function(t){return{is:f,isValue:f,isError:d,getOr:h,getOrThunk:function(n){return n()},getOrDie:function(){return c(String(t))()},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,e){return n(t)},map:function(n){return Ze(t)},mapError:function(n){return Ze(n(t))},each:I,bind:function(n){return Ze(t)},exists:f,forall:d,toOption:F.none}},nt={value:Qe,error:Ze},et=function(u){if(!p(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var a=[],t={};return yn(u,function(n,r){var e=N(n);if(1!==e.length)throw new Error("one and only one name per case");var o=e[0],i=n[o];if(t[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!p(i))throw new Error("case arguments must be an array");a.push(o),t[o]=function(){var n=arguments.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+n);for(var t=new Array(n),e=0;e<t.length;e++)t[e]=arguments[e];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[r].apply(null,t)},match:function(n){var e=N(n);if(a.length!==e.length)throw new Error("Wrong number of arguments to match. Expected: "+a.join(",")+"\nActual: "+e.join(","));if(!In(a,function(n){return hn(e,n)}))throw new Error("Not all branches were specified when using match. Specified: "+e.join(", ")+"\nRequired: "+a.join(", "));return n[o].apply(null,t)},log:function(n){console.log(n,{constructors:a,constructor:o,params:t})}}}}),t},tt=et([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),rt=function(n){return tt.defaultedThunk(A(n))},ot=tt.strict,it=tt.asOption,ut=tt.defaultedThunk,at=(tt.asDefaultedOptionThunk,tt.mergeWithThunk),ct=(et([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),function(n){var e=[],t=[];return yn(n,function(n){n.fold(function(n){e.push(n)},function(n){t.push(n)})}),{errors:e,values:t}}),st=function(n){return v(nt.error,En)(n)},ft=function(n,e){var t,r,o=ct(n);return 0<o.errors.length?st(o.errors):(t=o.values,r=e,nt.value(k.apply(undefined,[r].concat(t))))},lt=function(n){var e=ct(n);return 0<e.errors.length?st(e.errors):nt.value(e.values)},dt=function(e){return function(n){return n.hasOwnProperty(e)?F.from(n[e]):F.none()}},mt=function(n,e){return dt(e)(n)},gt=function(n,e){var t={};return t[n]=e,t},vt=function(n,e){return t=n,r={},yn(e,function(n){t[n]!==undefined&&t.hasOwnProperty(n)&&(r[n]=t[n])}),r;var t,r},pt=function(n,e){return t=e,r={},V(n,function(n,e){hn(t,e)||(r[e]=n)}),r;var t,r},ht=function(n){return dt(n)},bt=function(n,e){return t=n,r=e,function(n){return dt(t)(n).getOr(r)};var t,r},yt=function(n,e){return mt(n,e)},wt=function(n,e){return gt(n,e)},xt=function(n){return e={},yn(n,function(n){e[n.key]=n.value}),e;var e},St=function(n,e){return ft(n,e)},Tt=function(n,e){return r=e,(t=n).hasOwnProperty(r)&&t[r]!==undefined&&null!==t[r];var t,r},Ot=et([{setOf:["validator","valueType"]},{arrOf:["valueType"]},{objOf:["fields"]},{itemOf:["validator"]},{choiceOf:["key","branches"]},{thunk:["description"]},{func:["args","outputSchema"]}]),kt=et([{field:["name","presence","type"]},{state:["name"]}]),Ct=function(){return Ie.getOrDie("JSON")},Et=function(n,e,t){return Ct().stringify(n,e,t)},Dt=function(n){return g(n)&&100<N(n).length?" removed due to size":Et(n,null,2)},It=function(n,e){return nt.error([{path:n,getErrorInfo:e}])},At=et([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),Mt=function(t,r,o){return mt(r,o).fold(function(){return n=o,e=r,It(t,function(){return'Could not find valid *strict* value for "'+n+'" in '+Dt(e)});var n,e},nt.value)},Bt=function(n,e,t){var r=mt(n,e).fold(function(){return t(n)},h);return nt.value(r)},Rt=function(o,a,n,c){return n.fold(function(i,e,n,t){var r=function(n){return t.extract(o.concat([i]),c,n).map(function(n){return gt(e,c(n))})},u=function(n){return n.fold(function(){var n=gt(e,c(F.none()));return nt.value(n)},function(n){return t.extract(o.concat([i]),c,n).map(function(n){return gt(e,c(F.some(n)))})})};return n.fold(function(){return Mt(o,a,i).bind(r)},function(n){return Bt(a,i,n).bind(r)},function(){return(n=a,e=i,nt.value(mt(n,e))).bind(u);var n,e},function(n){return(e=a,t=i,r=n,o=mt(e,t).map(function(n){return!0===n?r(e):n}),nt.value(o)).bind(u);var e,t,r,o},function(n){var e=n(a);return Bt(a,i,A({})).map(function(n){return k(e,n)}).bind(r)})},function(n,e){var t=e(a);return nt.value(gt(n,c(t)))})},Ft=function(r){return{extract:function(t,n,e){return r(e,n).fold(function(n){return e=n,It(t,function(){return e});var e},nt.value)},toString:function(){return"val"},toDsl:function(){return Ot.itemOf(r)}}},Nt=function(n){var c=Vt(n),s=xn(n,function(e,n){return n.fold(function(n){return k(e,wt(n,!0))},A(e))},{});return{extract:function(n,e,t){var r,o,i,u=y(t)?[]:(o=N(r=t),wn(o,function(n){return Tt(r,n)})),a=wn(u,function(n){return!Tt(s,n)});return 0===a.length?c.extract(n,e,t):(i=a,It(n,function(){return"There are unsupported fields: ["+i.join(", ")+"] specified"}))},toString:c.toString,toDsl:c.toDsl}},Vt=function(a){return{extract:function(n,e,t){return r=n,o=t,i=e,u=bn(a,function(n){return Rt(r,o,n,i)}),ft(u,{});var r,o,i,u},toString:function(){return"obj{\n"+bn(a,function(n){return n.fold(function(n,e,t,r){return n+" -> "+r.toString()},function(n,e){return"state("+n+")"})}).join("\n")+"}"},toDsl:function(){return Ot.objOf(bn(a,function(n){return n.fold(function(n,e,t,r){return kt.field(n,t,r)},function(n,e){return kt.state(n)})}))}}},Ht=function(t,i){var e=function(n,e){return(o=Ft(t),{extract:function(t,r,n){var e=bn(n,function(n,e){return o.extract(t.concat(["["+e+"]"]),r,n)});return lt(e)},toString:function(){return"array("+o.toString()+")"},toDsl:function(){return Ot.arrOf(o)}}).extract(n,h,e);var o};return{extract:function(t,r,o){var n=N(o);return e(t,n).bind(function(n){var e=bn(n,function(n){return At.field(n,n,ot(),i)});return Vt(e).extract(t,r,o)})},toString:function(){return"setOf("+i.toString()+")"},toDsl:function(){return Ot.setOf(t,i)}}},zt=A(Ft(nt.value)),jt=At.state,Lt=At.field,Ut=function(t,e,r,o,i){return yt(o,i).fold(function(){return n=o,e=i,It(t,function(){return'The chosen schema: "'+e+'" did not exist in branches: '+Dt(n)});var n,e},function(n){return Vt(n).extract(t.concat(["branch: "+i]),e,r)})},Pt=function(o,i){return{extract:function(e,t,r){return yt(r,o).fold(function(){return n=o,It(e,function(){return'Choice schema did not contain choice key: "'+n+'"'});var n},function(n){return Ut(e,t,r,i,n)})},toString:function(){return"chooseOn("+o+"). Possible values: "+N(i)},toDsl:function(){return Ot.choiceOf(o,i)}}},$t=Ft(nt.value),Wt=function(n,e,t,r){return e.extract([n],t,r).fold(function(n){return nt.error({input:r,errors:n})},nt.value)},Gt=function(n,e,t){return Wt(n,e,A,t)},_t=function(n){return n.fold(function(n){throw new Error(Kt(n))},h)},qt=function(n,e,t){return _t(Wt(n,e,h,t))},Yt=function(n,e,t){return _t(Gt(n,e,t))},Kt=function(n){return"Errors: \n"+(e=n.errors,t=10<e.length?e.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):e,bn(t,function(n){return"Failed path: ("+n.path.join(" > ")+")\n"+n.getErrorInfo()}))+"\n\nInput object: "+Dt(n.input);var e,t},Xt=function(n,e){return Pt(n,e)},Jt=A($t),Qt=(i=w,u="function",Ft(function(n){var e=typeof n;return i(n)?nt.value(n):nt.error("Expected type: "+u+" but got: "+e)})),Zt=function(n){return Lt(n,n,ot(),zt())},nr=function(n,e){return Lt(n,n,ot(),e)},er=function(n){return nr(n,Qt)},tr=function(n,e){return Lt(n,n,ot(),Vt(e))},rr=function(n){return Lt(n,n,it(),zt())},or=function(n,e){return Lt(n,n,it(),Vt(e))},ir=function(n,e){return Lt(n,n,it(),Nt(e))},ur=function(n,e){return Lt(n,n,rt(e),zt())},ar=function(n,e,t){return Lt(n,n,rt(e),t)},cr=function(n,e){return jt(n,e)},sr=function(n){if(!Tt(n,"can")&&!Tt(n,"abort")&&!Tt(n,"run"))throw new Error("EventHandler defined by: "+Et(n,null,2)+" does not have can, abort, or run!");return qt("Extracting event.handler",Nt([ur("can",A(!0)),ur("abort",A(!1)),ur("run",I)]),n)},fr=function(t){var e,r,o,i,n=(e=t,r=function(n){return n.can},function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return Sn(e,function(n,e){return n&&r(e).apply(undefined,t)},!0)}),u=(o=t,i=function(n){return n.abort},function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return Sn(o,function(n,e){return n||i(e).apply(undefined,t)},!1)});return sr({can:n,abort:u,run:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];yn(t,function(n){n.run.apply(undefined,e)})}})},lr=function(n){return xt(n)},dr=function(n,e){return{key:n,value:sr({abort:e})}},mr=function(n,e){return{key:n,value:sr({run:e})}},gr=function(n,e,t){return{key:n,value:sr({run:function(n){e.apply(undefined,[n].concat(t))}})}},vr=function(n){return function(r){return{key:n,value:sr({run:function(n,e){var t;t=e,Ve(n.element(),t.event().target())&&r(n,e)}})}}},pr=function(n,e,t){var u,r,o=e.partUids()[t];return r=o,mr(u=n,function(n,i){n.getSystem().getByUid(r).each(function(n){var e,t,r,o;t=(e=n).element(),r=u,o=i,e.getSystem().triggerEvent(r,t,o.event())})})},hr=function(n){return mr(n,function(n,e){e.cut()})},br=vr(ee()),yr=vr(te()),wr=vr(Zn()),xr=(a=Yn(),function(n){return mr(a,n)}),Sr=function(n){return bn(n,function(n){return r=e="/*",o=(t=n).length-e.length,""!==r&&(t.length<r.length||t.substr(o,o+r.length)!==r)?n:n.substring(0,n.length-"/*".length);var e,t,r,o})},Tr=function(n,e){var t=n.toString(),r=t.indexOf(")")+1,o=t.indexOf("("),i=t.substring(o+1,r-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:e,parameters:Sr(i)}},n},Or=Ce(["tag"],["classes","attributes","styles","value","innerHtml","domChildren","defChildren"]),kr=function(n){return{tag:n.tag(),classes:n.classes().getOr([]),attributes:n.attributes().getOr({}),styles:n.styles().getOr({}),value:n.value().getOr("<none>"),innerHtml:n.innerHtml().getOr("<none>"),defChildren:n.defChildren().fold(function(){return"<none>"},function(n){return Et(n,null,2)}),domChildren:n.domChildren().fold(function(){return"<none>"},function(n){return 0===n.length?"0 children, but still specified":String(n.length)})}},Cr=Ce([],["classes","attributes","styles","value","innerHtml","defChildren","domChildren"]),Er=function(e,n,t){return n.fold(function(){return t.fold(function(){return{}},function(n){return wt(e,n)})},function(n){return t.fold(function(){return wt(e,n)},function(n){return wt(e,n)})})},Dr=function(t,r,o){return wr(function(n,e){o(n,t,r)})},Ir=function(n,e,t,r,o,i){var u,a,c=n,s=or(e,[(u="config",a=n,Lt(u,u,it(),a))]);return Br(c,s,e,t,r,o,i)},Ar=function(o,i,u){var n,e,t,r,a,c;return n=function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];var r=[t].concat(n);return t.config({name:A(o)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+o+". Using API: "+u)},function(n){var e=Array.prototype.slice.call(r,1);return i.apply(undefined,[t,n.config,n.state].concat(e))})},e=u,t=i.toString(),r=t.indexOf(")")+1,a=t.indexOf("("),c=t.substring(a+1,r-1).split(/,\s*/),n.toFunctionAnnotation=function(){return{name:e,parameters:Sr(c.slice(0,1).concat(c.slice(3)))}},n},Mr=function(n){return{key:n,value:undefined}},Br=function(t,n,r,o,e,i,u){var a=function(n){return Tt(n,r)?n[r]():F.none()},c=H(e,function(n,e){return Ar(r,n,e)}),s=H(i,function(n,e){return Tr(n,e)}),f=k(s,c,{revoke:l(Mr,r),config:function(n){var e=Yt(r+"-config",t,n);return{key:r,value:{config:e,me:f,configAsRaw:Z(function(){return qt(r+"-config",t,n)}),initialConfig:n,state:u}}},schema:function(){return n},exhibit:function(n,t){return a(n).bind(function(e){return yt(o,"exhibit").map(function(n){return n(t,e.config,e.state)})}).getOr(Cr({}))},name:function(){return r},handlers:function(n){return a(n).bind(function(e){return yt(o,"events").map(function(n){return n(e.config,e.state)})}).getOr({})}});return f},Rr=function(n,e){return Fr(n,e,{validate:w,label:"function"})},Fr=function(r,o,i){if(0===o.length)throw new Error("You must specify at least one required field.");return Oe("required",o),ke(o),function(e){var t=N(e);In(o,function(n){return hn(t,n)})||Se(o,t),r(o,t);var n=wn(o,function(n){return!i.validate(e[n],n)});return 0<n.length&&function(n,e){throw new Error("All values need to be of type: "+e+". Keys ("+xe(n).join(", ")+") were not.")}(n,i.label),e}},Nr=function(e,n){var t=wn(n,function(n){return!hn(e,n)});0<t.length&&Te(t)},Vr=I,Hr=function(n){return Rr(Nr,n)},zr={init:function(){return jr({readState:function(){return"No State required"}})}},jr=function(n){return Rr(Vr,["readState"])(n),n},Lr=function(n){return xt(n)},Ur=Nt([Zt("fields"),Zt("name"),ur("active",{}),ur("apis",{}),ur("state",zr),ur("extra",{})]),Pr=function(n){var e,t,r,o,i,u,a,c,s=qt("Creating behaviour: "+n.name,Ur,n);return e=s.fields,t=s.name,r=s.active,o=s.apis,i=s.extra,u=s.state,a=Nt(e),c=or(t,[ir("config",e)]),Br(a,c,t,r,o,i,u)},$r=Nt([Zt("branchKey"),Zt("branches"),Zt("name"),ur("active",{}),ur("apis",{}),ur("state",zr),ur("extra",{})]),Wr=A(undefined),Gr=function(n,e,t){if(!(b(t)||y(t)||x(t)))throw console.error("Invalid call to Attr.set. Key ",e,":: Value ",t,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(e,t+"")},_r=function(n,e,t){Gr(n.dom(),e,t)},qr=function(n,e){var t=n.dom();V(e,function(n,e){Gr(t,e,n)})},Yr=function(n,e){var t=n.dom().getAttribute(e);return null===t?undefined:t},Kr=function(n,e){var t=n.dom();return!(!t||!t.hasAttribute)&&t.hasAttribute(e)},Xr=function(n,e){n.dom().removeAttribute(e)},Jr=function(n,e){var t=Yr(n,e);return t===undefined||""===t?[]:t.split(" ")},Qr=function(n){return n.dom().classList!==undefined},Zr=function(n){return Jr(n,"class")},no=function(n,e){return o=e,i=Jr(t=n,r="class").concat([o]),_r(t,r,i.join(" ")),!0;var t,r,o,i},eo=function(n,e){return o=e,0<(i=wn(Jr(t=n,r="class"),function(n){return n!==o})).length?_r(t,r,i.join(" ")):Xr(t,r),!1;var t,r,o,i},to=function(n,e){Qr(n)?n.dom().classList.add(e):no(n,e)},ro=function(n,e){var t;Qr(n)?n.dom().classList.remove(e):eo(n,e),0===(Qr(t=n)?t.dom().classList:Zr(t)).length&&Xr(t,"class")},oo=function(n,e){return Qr(n)?n.dom().classList.toggle(e):(r=e,hn(Zr(t=n),r)?eo(t,r):no(t,r));var t,r},io=function(n,e){return Qr(n)&&n.dom().classList.contains(e)},uo=function(n,e,t){ro(n,t),to(n,e)},ao=Object.freeze({toAlpha:function(n,e,t){uo(n.element(),e.alpha(),e.omega())},toOmega:function(n,e,t){uo(n.element(),e.omega(),e.alpha())},isAlpha:function(n,e,t){return io(n.element(),e.alpha())},isOmega:function(n,e,t){return io(n.element(),e.omega())},clear:function(n,e,t){ro(n.element(),e.alpha()),ro(n.element(),e.omega())}}),co=[Zt("alpha"),Zt("omega")],so=Pr({fields:co,name:"swapping",apis:ao}),fo=function(n){var e=n,t=function(){return e};return{get:t,set:function(n){e=n},clone:function(){return fo(t())}}};function lo(n,e,t,r,o){return n(t,r)?F.some(t):w(o)&&o(t)?F.none():e(t,r,o)}var mo=function(n,e,t){for(var r=n.dom(),o=w(t)?t:A(!1);r.parentNode;){r=r.parentNode;var i=se.fromDom(r);if(e(i))return F.some(i);if(o(i))break}return F.none()},go=function(n,e,t){return lo(function(n){return e(n)},mo,n,e,t)},vo=function(n,r){var o=function(n){for(var e=0;e<n.childNodes.length;e++){if(r(se.fromDom(n.childNodes[e])))return F.some(se.fromDom(n.childNodes[e]));var t=o(n.childNodes[e]);if(t.isSome())return t}return F.none()};return o(n.dom())},po=function(n){n.dom().focus()},ho=function(n){n.dom().blur()},bo=function(n){var e=n!==undefined?n.dom():document;return F.from(e.activeElement).map(se.fromDom)},yo=function(e){return bo(He(e)).filter(function(n){return e.dom().contains(n.dom())})},wo=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),xo=tinymce.util.Tools.resolve("tinymce.ThemeManager"),So=function(n){var e=document.createElement("a");e.target="_blank",e.href=n.href,e.rel="noreferrer noopener";var t=document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),document.body.appendChild(e),e.dispatchEvent(t),document.body.removeChild(e)},To={formatChanged:A("formatChanged"),orientationChanged:A("orientationChanged"),dropupDismissed:A("dropupDismissed")},Oo=function(n){return n.dom().innerHTML},ko=function(n,e){var t,r,o=He(n).dom(),i=se.fromDom(o.createDocumentFragment()),u=(t=e,(r=(o||document).createElement("div")).innerHTML=t,je(se.fromDom(r)));$e(i,u),We(n),Pe(n,i)},Co=function(n){return e=n,t=!1,se.fromDom(e.dom().cloneNode(t));var e,t},Eo=function(n){var e,t,r,o=Co(n);return e=o,t=se.fromTag("div"),r=se.fromDom(e.dom().cloneNode(!0)),Pe(t,r),Oo(t)},Do=function(n){return Eo(n)},Io=Object.freeze({events:function(a){return lr([mr(qn(),function(o,i){var n,e,u=a.channels(),t=N(u),r=(n=t,(e=i).universal()?n:wn(n,function(n){return hn(e.channels(),n)}));yn(r,function(n){var e=u[n](),t=e.schema(),r=Yt("channel["+n+"] data\nReceiver: "+Do(o.element()),t,i.data());e.onReceive()(o,r)})})])}}),Ao=function(n){for(var e=[],t=function(n){e.push(n)},r=0;r<n.length;r++)n[r].each(t);return e},Mo=function(n,e){for(var t=0;t<n.length;t++){var r=e(n[t],t);if(r.isSome())return r}return F.none()},Bo="unknown",Ro=[],Fo=["alloy/data/Fields","alloy/debugging/Debugging"],No=function(){var n=new Error;if(n.stack!==undefined){var e=n.stack.split("\n");return Tn(e,function(e){return 0<e.indexOf("alloy")&&!On(Fo,function(n){return-1<e.indexOf(n)}).isSome()}).getOr(Bo)}return Bo},Vo={logEventCut:I,logEventStopped:I,logNoParent:I,logEventNoHandlers:I,logEventResponse:I,write:I},Ho=function(n,e,t){var r,o="*"===Ro||hn(Ro,n)?(r=[],{logEventCut:function(n,e,t){r.push({outcome:"cut",target:e,purpose:t})},logEventStopped:function(n,e,t){r.push({outcome:"stopped",target:e,purpose:t})},logNoParent:function(n,e,t){r.push({outcome:"no-parent",target:e,purpose:t})},logEventNoHandlers:function(n,e){r.push({outcome:"no-handlers-left",target:e})},logEventResponse:function(n,e,t){r.push({outcome:"response",purpose:t,target:e})},write:function(){hn(["mousemove","mouseover","mouseout",Zn()],n)||console.log(n,{event:n,target:e.dom(),sequence:bn(r,function(n){return hn(["cut","stopped","response"],n.outcome)?"{"+n.purpose+"} "+n.outcome+" at ("+Do(n.target)+")":n.outcome})})}}):Vo,i=t(o);return o.write(),i},zo=A([Zt("menu"),Zt("selectedMenu")]),jo=A([Zt("item"),Zt("selectedItem")]),Lo=(A(Nt(jo().concat(zo()))),A(Nt(jo()))),Uo=tr("initSize",[Zt("numColumns"),Zt("numRows")]),Po=function(n,e,t){var r;return No(),Lt(e,e,t,(r=function(t){return nt.value(function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t.apply(undefined,n)})},Ft(function(n){return r(n)})))},$o=function(n){return Po(0,n,rt(I))},Wo=function(n){return Po(0,n,rt(F.none))},Go=function(n){return Po(0,n,ot())},_o=function(n){return Po(0,n,ot())},qo=function(n,e){return cr(n,A(e))},Yo=function(n){return cr(n,h)},Ko=A(Uo),Xo=[nr("channels",Ht(nt.value,Nt([Go("onReceive"),ur("schema",Jt())])))],Jo=Pr({fields:Xo,name:"receiving",active:Io}),Qo=function(n,e){var t=ti(n,e),r=e.aria();r.update()(n,r,t)},Zo=function(n,e,t){oo(n.element(),e.toggleClass()),Qo(n,e)},ni=function(n,e,t){to(n.element(),e.toggleClass()),Qo(n,e)},ei=function(n,e,t){ro(n.element(),e.toggleClass()),Qo(n,e)},ti=function(n,e){return io(n.element(),e.toggleClass())},ri=function(n,e,t){(e.selected()?ni:ei)(n,e,t)},oi=Object.freeze({onLoad:ri,toggle:Zo,isOn:ti,on:ni,off:ei}),ii=Object.freeze({exhibit:function(n,e,t){return Cr({})},events:function(n,e){var t,r,o,i=(t=n,r=e,o=Zo,xr(function(n){o(n,t,r)})),u=Dr(n,e,ri);return lr(En([n.toggleOnExecute()?[i]:[],[u]]))}}),ui=function(n,e,t){_r(n.element(),"aria-expanded",t)},ai=[ur("selected",!1),Zt("toggleClass"),ur("toggleOnExecute",!0),ar("aria",{mode:"none"},Xt("mode",{pressed:[ur("syncWithExpanded",!1),qo("update",function(n,e,t){_r(n.element(),"aria-pressed",t),e.syncWithExpanded()&&ui(n,e,t)})],checked:[qo("update",function(n,e,t){_r(n.element(),"aria-checked",t)})],expanded:[qo("update",ui)],selected:[qo("update",function(n,e,t){_r(n.element(),"aria-selected",t)})],none:[qo("update",I)]}))],ci=Pr({fields:ai,name:"toggling",active:ii,apis:oi}),si=function(t,r){return Jo.config({channels:wt(To.formatChanged(),{onReceive:function(n,e){e.command===t&&r(n,e.state)}})})},fi=function(n){return Jo.config({channels:wt(To.orientationChanged(),{onReceive:n})})},li=function(n,e){return{key:n,value:{onReceive:e}}},di="tinymce-mobile",mi={resolve:function(n){return di+"-"+n},prefix:A(di)},gi=function(n,e){e.ignore()||(po(n.element()),e.onFocus()(n))},vi=Object.freeze({focus:gi,blur:function(n,e){e.ignore()||ho(n.element())},isFocused:function(n){return e=n.element(),t=He(e).dom(),e.dom()===t.activeElement;var e,t}}),pi=Object.freeze({exhibit:function(n,e){return e.ignore()?Cr({}):Cr({attributes:{tabindex:"-1"}})},events:function(t){return lr([mr(Gn(),function(n,e){gi(n,t),e.stop()})])}}),hi=[$o("onFocus"),ur("ignore",!1)],bi=Pr({fields:hi,name:"focusing",active:pi,apis:vi}),yi=function(n){return n.style!==undefined},wi=function(n,e,t){if(!b(t))throw console.error("Invalid call to CSS.set. Property ",e,":: Value ",t,":: Element ",n),new Error("CSS value must be a string: "+t);yi(n)&&n.style.setProperty(e,t)},xi=function(n,e,t){var r=n.dom();wi(r,e,t)},Si=function(n,e){var t=n.dom();V(e,function(n,e){wi(t,e,n)})},Ti=function(n,e){var t=n.dom(),r=window.getComputedStyle(t).getPropertyValue(e),o=""!==r||he(n)?r:Oi(t,e);return null===o?undefined:o},Oi=function(n,e){return yi(n)?n.style.getPropertyValue(e):""},ki=function(n,e){var t=n.dom(),r=Oi(t,e);return F.from(r).filter(function(n){return 0<n.length})},Ci=function(n,e){var t,r,o=n.dom();r=e,yi(t=o)&&t.style.removeProperty(r),Kr(n,"style")&&""===Yr(n,"style").replace(/^\s+|\s+$/g,"")&&Xr(n,"style")},Ei=function(n){return n.dom().offsetWidth};function Di(r,o){var n=function(n){var e=o(n);if(e<=0||null===e){var t=Ti(n,r);return parseFloat(t)||0}return e},i=function(o,n){return Sn(n,function(n,e){var t=Ti(o,e),r=t===undefined?0:parseInt(t,10);return isNaN(r)?n:n+r},0)};return{set:function(n,e){if(!x(e)&&!e.match(/^[0-9]+$/))throw r+".set accepts only positive integer values. Value was "+e;var t=n.dom();yi(t)&&(t.style[r]=e+"px")},get:n,getOuter:n,aggregate:i,max:function(n,e,t){var r=i(n,t);return r<e?e-r:0}}}var Ii,Ai,Mi=Di("height",function(n){var e=n.dom();return he(n)?e.getBoundingClientRect().height:e.offsetHeight}),Bi=function(n){return Mi.get(n)},Ri=function(n,e,t){return wn(function(n,e){for(var t=w(e)?e:A(!1),r=n.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,u=se.fromDom(i);if(o.push(u),!0===t(u))break;r=i}return o}(n,t),e)},Fi=function(n,e){return wn(ze(t=n).map(je).map(function(n){return wn(n,function(n){return!Ve(t,n)})}).getOr([]),e);var t},Ni=function(n,e){return Fe(e,n)},Vi=function(n){return Ne(n)},Hi=function(n,e,t){return mo(n,function(n){return Be(n,e)},t)},zi=function(n,e){return Ne(e,n)},ji=function(n,e,t){return lo(Be,Hi,n,e,t)},Li=function(n,e,t){var r=Mn(n.slice(0,e)),o=Mn(n.slice(e+1));return Tn(r.concat(o),t)},Ui=function(n,e,t){var r=Mn(n.slice(0,e));return Tn(r,t)},Pi=function(n,e,t){var r=n.slice(0,e),o=n.slice(e+1);return Tn(o.concat(r),t)},$i=function(n,e,t){var r=n.slice(e+1);return Tn(r,t)},Wi=function(t){return function(n){var e=n.raw();return hn(t,e.which)}},Gi=function(n){return function(e){return In(n,function(n){return n(e)})}},_i=function(n){return!0===n.raw().shiftKey},qi=function(n){return!0===n.raw().ctrlKey},Yi=S(_i),Ki=function(n,e){return{matches:n,classification:e}},Xi=function(n,e,t,r){var o=n+e;return r<o?t:o<t?r:o},Ji=function(n,e,t){return n<=e?e:t<=n?t:n},Qi=function(e,t,n){var r=Ni(e.element(),"."+t.highlightClass());yn(r,function(n){ro(n,t.highlightClass()),e.getSystem().getByDom(n).each(function(n){t.onDehighlight()(e,n)})})},Zi=function(n,e,t,r){var o=nu(n,e,t,r);Qi(n,e),to(r.element(),e.highlightClass()),o||e.onHighlight()(n,r)},nu=function(n,e,t,r){return io(r.element(),e.highlightClass())},eu=function(n,e,t,r){var o=Ni(n.element(),"."+e.itemClass());return F.from(o[r]).fold(function(){return nt.error("No element found with index "+r)},n.getSystem().getByDom)},tu=function(e,n,t){return zi(e.element(),"."+n.itemClass()).bind(function(n){return e.getSystem().getByDom(n).toOption()})},ru=function(e,n,t){var r=Ni(e.element(),"."+n.itemClass());return(0<r.length?F.some(r[r.length-1]):F.none()).bind(function(n){return e.getSystem().getByDom(n).toOption()})},ou=function(t,e,n,r){var o=Ni(t.element(),"."+e.itemClass());return On(o,function(n){return io(n,e.highlightClass())}).bind(function(n){var e=Xi(n,r,0,o.length-1);return t.getSystem().getByDom(o[e]).toOption()})},iu=Object.freeze({dehighlightAll:Qi,dehighlight:function(n,e,t,r){var o=nu(n,e,t,r);ro(r.element(),e.highlightClass()),o&&e.onDehighlight()(n,r)},highlight:Zi,highlightFirst:function(e,t,r){tu(e,t,r).each(function(n){Zi(e,t,r,n)})},highlightLast:function(e,t,r){ru(e,t,r).each(function(n){Zi(e,t,r,n)})},highlightAt:function(e,t,r,n){eu(e,t,r,n).fold(function(n){throw new Error(n)},function(n){Zi(e,t,r,n)})},highlightBy:function(e,t,r,n){var o=Ni(e.element(),"."+t.itemClass()),i=Ao(bn(o,function(n){return e.getSystem().getByDom(n).toOption()}));Tn(i,n).each(function(n){Zi(e,t,r,n)})},isHighlighted:nu,getHighlighted:function(e,n,t){return zi(e.element(),"."+n.highlightClass()).bind(function(n){return e.getSystem().getByDom(n).toOption()})},getFirst:tu,getLast:ru,getPrevious:function(n,e,t){return ou(n,e,0,-1)},getNext:function(n,e,t){return ou(n,e,0,1)}}),uu=[Zt("highlightClass"),Zt("itemClass"),$o("onHighlight"),$o("onDehighlight")],au=Pr({fields:uu,name:"highlighting",apis:iu}),cu=function(){return{get:function(n){return yo(n.element())},set:function(n,e){n.getSystem().triggerFocus(e,n.element())}}},su=function(n,e,a,t,r,i){var u=function(e,t,r,o){var n,i,u=a(e,t,r,o);return(n=u,i=t.event(),Tn(n,function(n){return n.matches(i)}).map(function(n){return n.classification})).bind(function(n){return n(e,t,r,o)})},o={schema:function(){return n.concat([ur("focusManager",cu()),qo("handler",o),qo("state",e)])},processKey:u,toEvents:function(r,o){var n=t(r,o),e=lr(i.map(function(t){return mr(Gn(),function(n,e){t(n,r,o,e),e.stop()})}).toArray().concat([mr(q(),function(n,e){u(n,e,r,o).each(function(n){e.stop()})})]));return k(n,e)},toApis:r};return o},fu=function(n){var e=[rr("onEscape"),rr("onEnter"),ur("selector",'[data-alloy-tabstop="true"]'),ur("firstTabstop",0),ur("useTabstopAt",A(!0)),rr("visibilitySelector")].concat([n]),u=function(n,e){var t=n.visibilitySelector().bind(function(n){return ji(e,n)}).getOr(e);return 0<Bi(t)},a=function(e,n,t,r,o){return o(n,t,function(n){return u(e=r,t=n)&&e.useTabstopAt()(t);var e,t}).fold(function(){return r.cyclic()?F.some(!0):F.none()},function(n){return r.focusManager().set(e,n),F.some(!0)})},i=function(e,n,t,r){var o,i,u=Ni(e.element(),t.selector());return(o=e,i=t,i.focusManager().get(o).bind(function(n){return ji(n,i.selector())})).bind(function(n){return On(u,l(Ve,n)).bind(function(n){return a(e,u,n,t,r)})})},t=A([Ki(Gi([_i,Wi([9])]),function(n,e,t,r){var o=t.cyclic()?Li:Ui;return i(n,0,t,o)}),Ki(Wi([9]),function(n,e,t,r){var o=t.cyclic()?Pi:$i;return i(n,0,t,o)}),Ki(Wi([27]),function(e,t,n,r){return n.onEscape().bind(function(n){return n(e,t)})}),Ki(Gi([Yi,Wi([13])]),function(e,t,n,r){return n.onEnter().bind(function(n){return n(e,t)})})]),r=A({}),o=A({});return su(e,zr.init,t,r,o,F.some(function(e,t){var n,r,o,i;(n=e,r=t,o=Ni(n.element(),r.selector()),i=wn(o,function(n){return u(r,n)}),F.from(i[r.firstTabstop()])).each(function(n){t.focusManager().set(e,n)})}))},lu=fu(cr("cyclic",A(!1))),du=fu(cr("cyclic",A(!0))),mu=function(n){return"input"===me(n)&&"radio"!==Yr(n,"type")||"textarea"===me(n)},gu=function(n,e,t){return mu(t)&&Wi([32])(e.event())?F.none():(ue(n,t,Yn()),F.some(!0))},vu=[ur("execute",gu),ur("useSpace",!1),ur("useEnter",!0),ur("useControlEnter",!1),ur("useDown",!1)],pu=function(n,e,t){return t.execute()(n,e,n.element())},hu=A({}),bu=A({}),yu=su(vu,zr.init,function(n,e,t,r){var o=t.useSpace()&&!mu(n.element())?[32]:[],i=t.useEnter()?[13]:[],u=t.useDown()?[40]:[],a=o.concat(i).concat(u);return[Ki(Wi(a),pu)].concat(t.useControlEnter()?[Ki(Gi([qi,Wi([13])]),pu)]:[])},hu,bu,F.none()),wu=function(n){var t=fo(F.none());return jr({readState:A({}),setGridSize:function(n,e){t.set(F.some({numRows:A(n),numColumns:A(e)}))},getNumRows:function(){return t.get().map(function(n){return n.numRows()})},getNumColumns:function(){return t.get().map(function(n){return n.numColumns()})}})},xu=Object.freeze({flatgrid:wu,init:function(n){return n.state()(n)}}),Su=function(e,t){return function(n){return"rtl"===Tu(n)?t:e}},Tu=function(n){return"rtl"===Ti(n,"direction")?"rtl":"ltr"},Ou=function(i){return function(n,e,t,r){var o=i(n.element());return Du(o,n,e,t,r)}},ku=function(n,e){var t=Su(n,e);return Ou(t)},Cu=function(n,e){var t=Su(e,n);return Ou(t)},Eu=function(o){return function(n,e,t,r){return Du(o,n,e,t,r)}},Du=function(e,t,n,r,o){return r.focusManager().get(t).bind(function(n){return e(t.element(),n,r,o)}).map(function(n){return r.focusManager().set(t,n),!0})},Iu=Eu,Au=Eu,Mu=Eu,Bu=function(n){var e,t=n.dom();return!((e=t).offsetWidth<=0&&e.offsetHeight<=0)},Ru=Ce(["index","candidates"],[]),Fu=function(n,e,t){return Nu(n,e,t,Bu)},Nu=function(n,e,t,r){var o,i=l(Ve,e),u=Ni(n,t),a=wn(u,Bu);return On(o=a,i).map(function(n){return Ru({index:n,candidates:o})})},Vu=function(n,e){return On(n,function(n){return Ve(e,n)})},Hu=function(t,n,r,e){return e(Math.floor(n/r),n%r).bind(function(n){var e=n.row()*r+n.column();return 0<=e&&e<t.length?F.some(t[e]):F.none()})},zu=function(o,n,i,u,a){return Hu(o,n,u,function(n,e){var t=n===i-1?o.length-n*u:u,r=Xi(e,a,0,t-1);return F.some({row:A(n),column:A(r)})})},ju=function(i,n,u,a,c){return Hu(i,n,a,function(n,e){var t=Xi(n,c,0,u-1),r=t===u-1?i.length-t*a:a,o=Ji(e,0,r-1);return F.some({row:A(t),column:A(o)})})},Lu=[Zt("selector"),ur("execute",gu),Wo("onEscape"),ur("captureTab",!1),Ko()],Uu=function(o){return function(n,e,t,r){return Fu(n,e,t.selector()).bind(function(n){return o(n.candidates(),n.index(),r.getNumRows().getOr(t.initSize().numRows()),r.getNumColumns().getOr(t.initSize().numColumns()))})}},Pu=function(n,e,t,r){return t.captureTab()?F.some(!0):F.none()},$u=Uu(function(n,e,t,r){return zu(n,e,t,r,-1)}),Wu=Uu(function(n,e,t,r){return zu(n,e,t,r,1)}),Gu=Uu(function(n,e,t,r){return ju(n,e,t,r,-1)}),_u=Uu(function(n,e,t,r){return ju(n,e,t,r,1)}),qu=A([Ki(Wi([37]),ku($u,Wu)),Ki(Wi([39]),Cu($u,Wu)),Ki(Wi([38]),Iu(Gu)),Ki(Wi([40]),Au(_u)),Ki(Gi([_i,Wi([9])]),Pu),Ki(Gi([Yi,Wi([9])]),Pu),Ki(Wi([27]),function(n,e,t,r){return t.onEscape()(n,e)}),Ki(Wi([32].concat([13])),function(e,t,r,n){return(o=e,i=r,i.focusManager().get(o).bind(function(n){return ji(n,i.selector())})).bind(function(n){return r.execute()(e,t,n)});var o,i})]),Yu=A({}),Ku=su(Lu,wu,qu,Yu,{},F.some(function(e,t,n){zi(e.element(),t.selector()).each(function(n){t.focusManager().set(e,n)})})),Xu=function(n,e,t,o){return Fu(n,t,e).bind(function(n){var e=n.index(),t=n.candidates(),r=Xi(e,o,0,t.length-1);return F.from(t[r])})},Ju=[Zt("selector"),ur("getInitial",F.none),ur("execute",gu),ur("executeOnMove",!1),ur("allowVertical",!0)],Qu=function(e,t,r){return(n=e,o=r,o.focusManager().get(n).bind(function(n){return ji(n,o.selector())})).bind(function(n){return r.execute()(e,t,n)});var n,o},Zu=function(n,e,t){return Xu(n,t.selector(),e,-1)},na=function(n,e,t){return Xu(n,t.selector(),e,1)},ea=function(r){return function(n,e,t){return r(n,e,t).bind(function(){return t.executeOnMove()?Qu(n,e,t):F.some(!0)})}},ta=A({}),ra=A({}),oa=su(Ju,zr.init,function(n,e,t,r){var o=[37].concat(t.allowVertical()?[38]:[]),i=[39].concat(t.allowVertical()?[40]:[]);return[Ki(Wi(o),ea(ku(Zu,na))),Ki(Wi(i),ea(Cu(Zu,na))),Ki(Wi([13]),Qu),Ki(Wi([32]),Qu)]},ta,ra,F.some(function(e,t){t.getInitial()(e).or(zi(e.element(),t.selector())).each(function(n){t.focusManager().set(e,n)})})),ia=Ce(["rowIndex","columnIndex","cell"],[]),ua=function(n,e,t){return F.from(n[e]).bind(function(n){return F.from(n[t]).map(function(n){return ia({rowIndex:e,columnIndex:t,cell:n})})})},aa=function(n,e,t,r){var o=n[e].length,i=Xi(t,r,0,o-1);return ua(n,e,i)},ca=function(n,e,t,r){var o=Xi(t,r,0,n.length-1),i=n[o].length,u=Ji(e,0,i-1);return ua(n,o,u)},sa=function(n,e,t,r){var o=n[e].length,i=Ji(t+r,0,o-1);return ua(n,e,i)},fa=function(n,e,t,r){var o=Ji(t+r,0,n.length-1),i=n[o].length,u=Ji(e,0,i-1);return ua(n,o,u)},la=[tr("selectors",[Zt("row"),Zt("cell")]),ur("cycles",!0),ur("previousSelector",F.none),ur("execute",gu)],da=function(n,e){return function(t,r,i){var u=i.cycles()?n:e;return ji(r,i.selectors().row()).bind(function(n){var e=Ni(n,i.selectors().cell());return Vu(e,r).bind(function(r){var o=Ni(t,i.selectors().row());return Vu(o,n).bind(function(n){var e,t=(e=i,bn(o,function(n){return Ni(n,e.selectors().cell())}));return u(t,n,r).map(function(n){return n.cell()})})})})}},ma=da(function(n,e,t){return aa(n,e,t,-1)},function(n,e,t){return sa(n,e,t,-1)}),ga=da(function(n,e,t){return aa(n,e,t,1)},function(n,e,t){return sa(n,e,t,1)}),va=da(function(n,e,t){return ca(n,t,e,-1)},function(n,e,t){return fa(n,t,e,-1)}),pa=da(function(n,e,t){return ca(n,t,e,1)},function(n,e,t){return fa(n,t,e,1)}),ha=A([Ki(Wi([37]),ku(ma,ga)),Ki(Wi([39]),Cu(ma,ga)),Ki(Wi([38]),Iu(va)),Ki(Wi([40]),Au(pa)),Ki(Wi([32].concat([13])),function(e,t,r){return yo(e.element()).bind(function(n){return r.execute()(e,t,n)})})]),ba=A({}),ya=A({}),wa=su(la,zr.init,ha,ba,ya,F.some(function(e,t){t.previousSelector()(e).orThunk(function(){var n=t.selectors();return zi(e.element(),n.cell())}).each(function(n){t.focusManager().set(e,n)})})),xa=[Zt("selector"),ur("execute",gu),ur("moveOnTab",!1)],Sa=function(e,t,r){return r.focusManager().get(e).bind(function(n){return r.execute()(e,t,n)})},Ta=function(n,e,t){return Xu(n,t.selector(),e,-1)},Oa=function(n,e,t){return Xu(n,t.selector(),e,1)},ka=A([Ki(Wi([38]),Mu(Ta)),Ki(Wi([40]),Mu(Oa)),Ki(Gi([_i,Wi([9])]),function(n,e,t){return t.moveOnTab()?Mu(Ta)(n,e,t):F.none()}),Ki(Gi([Yi,Wi([9])]),function(n,e,t){return t.moveOnTab()?Mu(Oa)(n,e,t):F.none()}),Ki(Wi([13]),Sa),Ki(Wi([32]),Sa)]),Ca=A({}),Ea=A({}),Da=su(xa,zr.init,ka,Ca,Ea,F.some(function(e,t){zi(e.element(),t.selector()).each(function(n){t.focusManager().set(e,n)})})),Ia=[Wo("onSpace"),Wo("onEnter"),Wo("onShiftEnter"),Wo("onLeft"),Wo("onRight"),Wo("onTab"),Wo("onShiftTab"),Wo("onUp"),Wo("onDown"),Wo("onEscape"),rr("focusIn")],Aa=su(Ia,zr.init,function(n,e,t){return[Ki(Wi([32]),t.onSpace()),Ki(Gi([Yi,Wi([13])]),t.onEnter()),Ki(Gi([_i,Wi([13])]),t.onShiftEnter()),Ki(Gi([_i,Wi([9])]),t.onShiftTab()),Ki(Gi([Yi,Wi([9])]),t.onTab()),Ki(Wi([38]),t.onUp()),Ki(Wi([40]),t.onDown()),Ki(Wi([37]),t.onLeft()),Ki(Wi([39]),t.onRight()),Ki(Wi([32]),t.onSpace()),Ki(Wi([27]),t.onEscape())]},function(){return{}},function(){return{}},F.some(function(e,t){return t.focusIn().bind(function(n){return n(e,t)})})),Ma=lu.schema(),Ba=du.schema(),Ra=oa.schema(),Fa=Ku.schema(),Na=wa.schema(),Va=yu.schema(),Ha=Da.schema(),za=Aa.schema(),ja=(Ai=qt("Creating behaviour: "+(Ii={branchKey:"mode",branches:Object.freeze({acyclic:Ma,cyclic:Ba,flow:Ra,flatgrid:Fa,matrix:Na,execution:Va,menu:Ha,special:za}),name:"keying",active:{events:function(n,e){return n.handler().toEvents(n,e)}},apis:{focusIn:function(n){n.getSystem().triggerFocus(n.element(),n.element())},setGridSize:function(n,e,t,r,o){Tt(t,"setGridSize")?t.setGridSize(r,o):console.error("Layout does not support setGridSize")}},state:xu}).name,$r,Ii),Ir(Xt(Ai.branchKey,Ai.branches),Ai.name,Ai.active,Ai.apis,Ai.extra,Ai.state)),La=function(r,n){return e=r,t={},o=bn(n,function(n){return e=n.name(),t="Cannot configure "+n.name()+" for "+r,Lt(e,e,it(),Ft(function(n){return nt.error("The field: "+e+" is forbidden. "+t)}));var e,t}).concat([cr("dump",h)]),Lt(e,e,rt(t),Vt(o));var e,t,o},Ua=function(n){return n.dump()},Pa="placeholder",$a=et([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Wa=function(n,e,t,r){return t.uiType===Pa?(i=t,u=r,(o=n).exists(function(n){return n!==i.owner})?$a.single(!0,A(i)):yt(u,i.name).fold(function(){throw new Error("Unknown placeholder component: "+i.name+"\nKnown: ["+N(u)+"]\nNamespace: "+o.getOr("none")+"\nSpec: "+Et(i,null,2))},function(n){return n.replace()})):$a.single(!1,A(t));var o,i,u},Ga=function(i,u,a,c){return Wa(i,0,a,c).fold(function(n,e){var t=e(u,a.config,a.validated),r=yt(t,"components").getOr([]),o=Dn(r,function(n){return Ga(i,u,n,c)});return[k(t,{components:o})]},function(n,e){return e(u,a.config,a.validated)})},_a=function(e,t,n,r){var o,i,u,a=H(r,function(n,e){return r=n,o=!1,{name:A(t=e),required:function(){return r.fold(function(n,e){return n},function(n,e){return n})},used:function(){return o},replace:function(){if(!0===o)throw new Error("Trying to use the same placeholder more than once: "+t);return o=!0,r}};var t,r,o}),c=(o=e,i=t,u=a,Dn(n,function(n){return Ga(o,i,n,u)}));return V(a,function(n){if(!1===n.used()&&n.required())throw new Error("Placeholder: "+n.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+Et(t.components(),null,2))}),c},qa=$a.single,Ya=$a.multiple,Ka=A(Pa),Xa=0,Ja=function(n){var e=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++Xa+String(e)},Qa=et([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Za=ur("factory",{sketch:h}),nc=ur("schema",[]),ec=Zt("name"),tc=Lt("pname","pname",ut(function(n){return"<alloy."+Ja(n.name)+">"}),Jt()),rc=ur("defaults",A({})),oc=ur("overrides",A({})),ic=Vt([Za,nc,ec,tc,rc,oc]),uc=Vt([Za,nc,ec,rc,oc]),ac=Vt([Za,nc,ec,tc,rc,oc]),cc=Vt([Za,nc,ec,Zt("unit"),tc,rc,oc]),sc=function(n){var e=function(n){return n.name()};return n.fold(e,e,e,e)},fc=function(t,r){return function(n){var e=Yt("Converting part type",r,n);return t(e)}},lc=fc(Qa.required,ic),dc=(fc(Qa.external,uc),fc(Qa.optional,ac)),mc=fc(Qa.group,cc),gc=A("entirety"),vc=function(n,e,t,r){var o=t;return k(e.defaults()(n,t,r),t,{uid:n.partUids()[e.name()]},e.overrides()(n,t,r),{"debug.sketcher":wt("part-"+e.name(),o)})},pc=function(o,n){var i={};return yn(n,function(n){var e;(e=n,e.fold(F.some,F.none,F.some,F.some)).each(function(t){var r=hc(o,t.pname());i[t.name()]=function(n){var e=qt("Part: "+t.name()+" in "+o,Vt(t.schema()),n);return k(r,{config:n,validated:e})}})}),i},hc=function(n,e){return{uiType:Ka(),owner:n,name:e}},bc=function(n,e,t){return r=e,i={},o={},yn(t,function(n){n.fold(function(r){i[r.pname()]=qa(!0,function(n,e,t){return r.factory().sketch(vc(n,r,e,t))})},function(n){var e=r.parts()[n.name()]();o[n.name()]=A(vc(r,n,e[gc()]()))},function(r){i[r.pname()]=qa(!1,function(n,e,t){return r.factory().sketch(vc(n,r,e,t))})},function(o){i[o.pname()]=Ya(!0,function(e,n,t){var r=e[o.name()]();return bn(r,function(n){return o.factory().sketch(k(o.defaults()(e,n),n,o.overrides()(e,n)))})})})}),{internals:A(i),externals:A(o)};var r,i,o},yc=function(n,e,t){return _a(F.some(n),e,e.components(),t)},wc=function(n,e,t){var r=e.partUids()[t];return n.getSystem().getByUid(r).toOption()},xc=function(n,e,t){return wc(n,e,t).getOrDie("Could not find part: "+t)},Sc=function(e,n){var t=bn(n,sc);return xt(bn(t,function(n){return{key:n,value:e+"-"+n}}))},Tc=function(e){return Lt("partUids","partUids",at(function(n){return Sc(n.uid,e)}),Jt())},Oc=Ja("alloy-premade"),kc=Ja("api"),Cc=function(n){return wt(Oc,n)},Ec=function(o){return n=function(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];var r=n.config(kc);return o.apply(undefined,[r].concat([n].concat(e)))},e=o.toString(),t=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,t-1).split(/,\s*/),n.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:Sr(i.slice(1))}},n;var n,e,t,r,i},Dc=A(kc),Ic=A("alloy-id-"),Ac=A("data-alloy-id"),Mc=Ic(),Bc=Ac(),Rc=function(n){var e=ve(n)?Yr(n,Bc):null;return F.from(e)},Fc=function(n){return Ja(n)},Nc=function(n,e,t,r,o){var i,u,a=(u=o,(0<(i=r).length?[tr("parts",i)]:[]).concat([Zt("uid"),ur("dom",{}),ur("components",[]),Yo("originalSpec"),ur("debug.sketcher",{})]).concat(u));return Yt(n+" [SpecSchema]",Nt(a.concat(e)),t)},Vc=function(n,e,t,r,o){var i=Hc(o),u=Dn(t,function(n){return n.fold(F.none,F.some,F.none,F.none).map(function(n){return tr(n.name(),n.schema().concat([Yo(gc())]))}).toArray()}),a=Tc(t),c=Nc(n,e,i,u,[a]),s=bc(0,c,t),f=yc(n,c,s.internals());return k(r(c,f,i,s.externals()),{"debug.sketcher":wt(n,o)})},Hc=function(n){return k({uid:Fc("uid")},n)},zc=Nt([Zt("name"),Zt("factory"),Zt("configFields"),ur("apis",{}),ur("extraApis",{})]),jc=Nt([Zt("name"),Zt("factory"),Zt("configFields"),Zt("partFields"),ur("apis",{}),ur("extraApis",{})]),Lc=function(n){var a=qt("Sketcher for "+n.name,zc,n),e=H(a.apis,Ec),t=H(a.extraApis,function(n,e){return Tr(n,e)});return k({name:A(a.name),partFields:A([]),configFields:A(a.configFields),sketch:function(n){return e=a.name,t=a.configFields,r=a.factory,i=Hc(o=n),u=Nc(e,t,i,[],[]),k(r(u,i),{"debug.sketcher":wt(e,o)});var e,t,r,o,i,u}},e,t)},Uc=function(n){var e=qt("Sketcher for "+n.name,jc,n),t=pc(e.name,e.partFields),r=H(e.apis,Ec),o=H(e.extraApis,function(n,e){return Tr(n,e)});return k({name:A(e.name),partFields:A(e.partFields),configFields:A(e.configFields),sketch:function(n){return Vc(e.name,e.configFields,e.partFields,e.factory,n)},parts:A(t)},r,o)},Pc=Lc({name:"Button",factory:function(n){var e,t,r,o=(e=n.action(),t=function(n,e){e.stop(),ie(n)},r=$n.detect().deviceType.isTouch()?[mr(Xn(),t)]:[mr(X(),t),mr($(),function(n,e){e.cut()})],lr(En([e.map(function(t){return mr(Yn(),function(n,e){t(n),e.stop()})}).toArray(),r]))),i=yt(n.dom(),"attributes").bind(ht("type")),u=yt(n.dom(),"tag");return{uid:n.uid(),dom:n.dom(),components:n.components(),events:o,behaviours:k(Lr([bi.config({}),ja.config({mode:"execution",useSpace:!0,useEnter:!0})]),Ua(n.buttonBehaviours())),domModification:{attributes:k(i.fold(function(){return u.is("button")?{type:"button"}:{}},function(n){return{}}),{role:n.role().getOr("button")})},eventOrder:n.eventOrder()}},configFields:[ur("uid",undefined),Zt("dom"),ur("components",[]),La("buttonBehaviours",[bi,ja]),rr("action"),rr("role"),ur("eventOrder",{})]}),$c=Pr({fields:[],name:"unselecting",active:Object.freeze({events:function(n){return lr([dr(Q(),A(!0))])},exhibit:function(n,e){return Cr({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),Wc=function(n){var e,t,r,o=se.fromHtml(n),i=je(o),u=(t=(e=o).dom().attributes!==undefined?e.dom().attributes:[],Sn(t,function(n,e){return"class"===e.name?n:k(n,wt(e.name,e.value))},{})),a=(r=o,Array.prototype.slice.call(r.dom().classList,0)),c=0===i.length?{}:{innerHtml:Oo(o)};return k({tag:me(o),classes:a,attributes:u},c)},Gc=function(n){var e,o,t=(e=n,o={prefix:mi.prefix()},e.replace(/\$\{([^{}]*)\}/g,function(n,e){var t,r=o[e];return"string"==(t=typeof r)||"number"===t?r.toString():n}));return Wc(t)},_c=function(n){return{dom:Gc(n)}},qc=function(n){return Lr([ci.config({toggleClass:mi.resolve("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),si(n,function(n,e){(e?ci.on:ci.off)(n)})])},Yc=function(n,e,t){return Pc.sketch({dom:Gc('<span class="${prefix}-toolbar-button ${prefix}-icon-'+n+' ${prefix}-icon"></span>'),action:e,buttonBehaviours:k(Lr([$c.config({})]),t)})},Kc={forToolbar:Yc,forToolbarCommand:function(n,e){return Yc(e,function(){n.execCommand(e)},{})},forToolbarStateAction:function(n,e,t,r){var o=qc(t);return Yc(e,r,o)},forToolbarStateCommand:function(n,e){var t=qc(e);return Yc(e,function(){n.execCommand(e)},t)}},Xc=function(t,r){return{left:A(t),top:A(r),translate:function(n,e){return Xc(t+n,r+e)}}},Jc=Xc,Qc=function(n,e,t){return Math.max(e,Math.min(t,n))},Zc=function(n,e,t,r,o,i,u){var a=t-e;if(r<n.left)return e-1;if(r>n.right)return t+1;var c,s,f,l,d=Math.min(n.right,Math.max(r,n.left))-n.left,m=Qc(d/n.width*a+e,e-1,t+1),g=Math.round(m);return i&&e<=m&&m<=t?(c=m,s=e,f=t,l=o,u.fold(function(){var n=c-s,e=Math.round(n/l)*l;return Qc(s+e,s-1,f+1)},function(n){var e=(c-n)%l,t=Math.round(e/l),r=Math.floor((c-n)/l),o=Math.floor((f-n)/l),i=n+Math.min(o,r+t)*l;return Math.max(n,i)})):g},ns="slider.change.value",es=$n.detect().deviceType.isTouch(),ts=function(n){return function(n){var e=n.event().raw();if(es){var t=e;return t.touches!==undefined&&1===t.touches.length?F.some(t.touches[0]).map(function(n){return Jc(n.clientX,n.clientY)}):F.none()}var r=e;return r.clientX!==undefined?F.some(r).map(function(n){return Jc(n.clientX,n.clientY)}):F.none()}(n).map(function(n){return n.left()})},rs=function(n,e){oe(n,ns,{value:e})},os=function(i,u,a,n){return ts(n).map(function(n){var e,t,r,o;return e=i,r=n,o=Zc(a,(t=u).min(),t.max(),r,t.stepSize(),t.snapToGrid(),t.snapStart()),rs(e,o),n})},is=function(n,e){var t,r,o,i,u=(t=e.value().get(),r=e.min(),o=e.max(),i=e.stepSize(),t<r?t:o<t?o:t===r?r-1:Math.max(r,t-i));rs(n,u)},us=function(n,e){var t,r,o,i,u=(t=e.value().get(),r=e.min(),o=e.max(),i=e.stepSize(),o<t?t:t<r?r:t===o?o+1:Math.min(o,t+i));rs(n,u)},as=$n.detect().deviceType.isTouch(),cs=function(n,r){return dc({name:n+"-edge",overrides:function(n){var e=lr([gr(L(),r,[n])]),t=lr([gr($(),r,[n]),gr(W(),function(n,e){e.mouseIsDown().get()&&r(n,e)},[n])]);return{events:as?e:t}}})},ss=[cs("left",function(n,e){rs(n,e.min()-1)}),cs("right",function(n,e){rs(n,e.max()+1)}),lc({name:"thumb",defaults:A({dom:{styles:{position:"absolute"}}}),overrides:function(n){return{events:lr([pr(L(),n,"spectrum"),pr(U(),n,"spectrum"),pr(P(),n,"spectrum")])}}}),lc({schema:[cr("mouseIsDown",function(){return fo(!1)})],name:"spectrum",overrides:function(r){var t=function(n,e){var t=n.element().dom().getBoundingClientRect();os(n,r,t,e)},n=lr([mr(L(),t),mr(U(),t)]),e=lr([mr($(),t),mr(W(),function(n,e){r.mouseIsDown().get()&&t(n,e)})]);return{behaviours:Lr(as?[]:[ja.config({mode:"special",onLeft:function(n){return is(n,r),F.some(!0)},onRight:function(n){return us(n,r),F.some(!0)}}),bi.config({})]),events:as?n:e}}})],fs=function(n,e,t){e.store().manager().onLoad(n,e,t)},ls=function(n,e,t){e.store().manager().onUnload(n,e,t)},ds=Object.freeze({onLoad:fs,onUnload:ls,setValue:function(n,e,t,r){e.store().manager().setValue(n,e,t,r)},getValue:function(n,e,t){return e.store().manager().getValue(n,e,t)}}),ms=Object.freeze({events:function(t,r){var n=t.resetOnDom()?[br(function(n,e){fs(n,t,r)}),yr(function(n,e){ls(n,t,r)})]:[Dr(t,r,fs)];return lr(n)}}),gs=function(){var n=fo(null);return jr({set:n.set,get:n.get,isNotSet:function(){return null===n.get()},clear:function(){n.set(null)},readState:function(){return{mode:"memory",value:n.get()}}})},vs=function(){var n=fo({});return jr({readState:function(){return{mode:"dataset",dataset:n.get()}},set:n.set,get:n.get})},ps=Object.freeze({memory:gs,dataset:vs,manual:function(){return jr({readState:function(){}})},init:function(n){return n.store().manager().state(n)}}),hs=function(n,e,t,r){e.store().getDataKey(),t.set({}),e.store().setData()(n,r),e.onSetValue()(n,r)},bs=[rr("initialValue"),Zt("getFallbackEntry"),Zt("getDataKey"),Zt("setData"),qo("manager",{setValue:hs,getValue:function(n,e,t){var r=e.store().getDataKey()(n),o=t.get();return yt(o,r).fold(function(){return e.store().getFallbackEntry()(r)},function(n){return n})},onLoad:function(e,t,r){t.store().initialValue().each(function(n){hs(e,t,r,n)})},onUnload:function(n,e,t){t.set({})},state:vs})],ys=[Zt("getValue"),ur("setValue",I),rr("initialValue"),qo("manager",{setValue:function(n,e,t,r){e.store().setValue()(n,r),e.onSetValue()(n,r)},getValue:function(n,e,t){return e.store().getValue()(n)},onLoad:function(e,t,n){t.store().initialValue().each(function(n){t.store().setValue()(e,n)})},onUnload:I,state:zr.init})],ws=[rr("initialValue"),qo("manager",{setValue:function(n,e,t,r){t.set(r),e.onSetValue()(n,r)},getValue:function(n,e,t){return t.get()},onLoad:function(n,e,t){e.store().initialValue().each(function(n){t.isNotSet()&&t.set(n)})},onUnload:function(n,e,t){t.clear()},state:gs})],xs=[ar("store",{mode:"memory"},Xt("mode",{memory:ws,manual:ys,dataset:bs})),$o("onSetValue"),ur("resetOnDom",!1)],Ss=Pr({fields:xs,name:"representing",active:ms,apis:ds,extra:{setValueFrom:function(n,e){var t=Ss.getValue(e);Ss.setValue(n,t)}},state:ps}),Ts=$n.detect().deviceType.isTouch(),Os=[Zt("min"),Zt("max"),ur("stepSize",1),ur("onChange",I),ur("onInit",I),ur("onDragStart",I),ur("onDragEnd",I),ur("snapToGrid",!1),rr("snapStart"),Zt("getInitialValue"),La("sliderBehaviours",[ja,Ss]),cr("value",function(n){return fo(n.min)})].concat(Ts?[]:[cr("mouseIsDown",function(){return fo(!1)})]),ks=Di("width",function(n){return n.dom().offsetWidth}),Cs=function(n,e){ks.set(n,e)},Es=function(n){return ks.get(n)},Ds=$n.detect().deviceType.isTouch(),Is=Uc({name:"Slider",configFields:Os,partFields:ss,factory:function(c,n,e,t){var s=c.max()-c.min(),f=function(n){var e=n.element().dom().getBoundingClientRect();return(e.left+e.right)/2},o=function(n){return xc(n,c,"thumb")},i=function(n){var e,t,r,o,i=xc(n,c,"spectrum").element().dom().getBoundingClientRect(),u=n.element().dom().getBoundingClientRect(),a=(e=n,t=i,(o=(r=c).value().get())<r.min()?wc(e,r,"left-edge").fold(function(){return 0},function(n){return f(n)-t.left}):o>r.max()?wc(e,r,"right-edge").fold(function(){return t.width},function(n){return f(n)-t.left}):(r.value().get()-r.min())/s*t.width);return i.left-u.left+a},u=function(n){var e=i(n),t=o(n),r=Es(t.element())/2;xi(t.element(),"left",e-r+"px")},r=function(n,e){var t=c.value().get(),r=o(n);return t!==e||ki(r.element(),"left").isNone()?(c.value().set(e),u(n),c.onChange()(n,r,e),F.some(!0)):F.none()},a=Ds?[mr(L(),function(n,e){c.onDragStart()(n,o(n))}),mr(P(),function(n,e){c.onDragEnd()(n,o(n))})]:[mr($(),function(n,e){e.stop(),c.onDragStart()(n,o(n)),c.mouseIsDown().set(!0)}),mr(G(),function(n,e){c.onDragEnd()(n,o(n)),c.mouseIsDown().set(!1)})];return{uid:c.uid(),dom:c.dom(),components:n,behaviours:k(Lr(En([Ds?[]:[ja.config({mode:"special",focusIn:function(n){return wc(n,c,"spectrum").map(ja.focusIn).map(A(!0))}})],[Ss.config({store:{mode:"manual",getValue:function(n){return c.value().get()}}})]])),Ua(c.sliderBehaviours())),events:lr([mr(ns,function(n,e){r(n,e.event().value())}),br(function(n,e){c.value().set(c.getInitialValue()());var t=o(n);u(n),c.onInit()(n,t,c.value().get())})].concat(a)),apis:{resetToMin:function(n){r(n,c.min())},resetToMax:function(n){r(n,c.max())},refresh:u},domModification:{styles:{position:"relative"}}}},apis:{resetToMin:function(n,e){n.resetToMin(e)},resetToMax:function(n,e){n.resetToMax(e)},refresh:function(n,e){n.refresh(e)}}}),As=function(e,t,r){return Kc.forToolbar(t,function(){var n=r();e.setContextToolbar([{label:t+" group",items:n}])},{})},Ms=function(n){return[(o=n,i=function(n){return n<0?"black":360<n?"white":"hsl("+n+", 100%, 50%)"},Is.sketch({dom:Gc('<div class="${prefix}-slider ${prefix}-hue-slider-container"></div>'),components:[Is.parts()["left-edge"](_c('<div class="${prefix}-hue-slider-black"></div>')),Is.parts().spectrum({dom:Gc('<div class="${prefix}-slider-gradient-container"></div>'),components:[_c('<div class="${prefix}-slider-gradient"></div>')],behaviours:Lr([ci.config({toggleClass:mi.resolve("thumb-active")})])}),Is.parts()["right-edge"](_c('<div class="${prefix}-hue-slider-white"></div>')),Is.parts().thumb({dom:Gc('<div class="${prefix}-slider-thumb"></div>'),behaviours:Lr([ci.config({toggleClass:mi.resolve("thumb-active")})])})],onChange:function(n,e,t){var r=i(t);xi(e.element(),"background-color",r),o.onChange(n,e,r)},onDragStart:function(n,e){ci.on(e)},onDragEnd:function(n,e){ci.off(e)},onInit:function(n,e,t){var r=i(t);xi(e.element(),"background-color",r)},stepSize:10,min:0,max:360,getInitialValue:o.getInitialValue,sliderBehaviours:Lr([fi(Is.refresh)])}))];var o,i},Bs=function(n,r){var e={onChange:function(n,e,t){r.undoManager.transact(function(){r.formatter.apply("forecolor",{value:t}),r.nodeChanged()})},getInitialValue:function(){return-1}};return As(n,"color",function(){return Ms(e)})},Rs=Nt([Zt("getInitialValue"),Zt("onChange"),Zt("category"),Zt("sizes")]),Fs=function(n){var o=qt("SizeSlider",Rs,n);return Is.sketch({dom:{tag:"div",classes:[mi.resolve("slider-"+o.category+"-size-container"),mi.resolve("slider"),mi.resolve("slider-size-container")]},onChange:function(n,e,t){var r;0<=(r=t)&&r<o.sizes.length&&o.onChange(t)},onDragStart:function(n,e){ci.on(e)},onDragEnd:function(n,e){ci.off(e)},min:0,max:o.sizes.length-1,stepSize:1,getInitialValue:o.getInitialValue,snapToGrid:!0,sliderBehaviours:Lr([fi(Is.refresh)]),components:[Is.parts().spectrum({dom:Gc('<div class="${prefix}-slider-size-container"></div>'),components:[_c('<div class="${prefix}-slider-size-line"></div>')]}),Is.parts().thumb({dom:Gc('<div class="${prefix}-slider-thumb"></div>'),behaviours:Lr([ci.config({toggleClass:mi.resolve("thumb-active")})])})]})},Ns=["9px","10px","11px","12px","14px","16px","18px","20px","24px","32px","36px"],Vs=function(n){var e,t,r=n.selection.getStart(),o=se.fromDom(r),i=se.fromDom(n.getBody()),u=(e=function(n){return Ve(i,n)},(ve(t=o)?F.some(t):ze(t)).map(function(n){return go(n,function(n){return ki(n,"font-size").isSome()},e).bind(function(n){return ki(n,"font-size")}).getOrThunk(function(){return Ti(n,"font-size")})}).getOr(""));return Tn(Ns,function(n){return u===n}).getOr("medium")},Hs={candidates:A(Ns),get:function(n){var e,t=Vs(n);return(e=t,On(Ns,function(n){return n===e})).getOr(2)},apply:function(r,n){var e;(e=n,F.from(Ns[e])).each(function(n){var e,t;t=n,Vs(e=r)!==t&&e.execCommand("fontSize",!1,t)})}},zs=Hs.candidates(),js=function(n){return[_c('<span class="${prefix}-toolbar-button ${prefix}-icon-small-font ${prefix}-icon"></span>'),(e=n,Fs({onChange:e.onChange,sizes:zs,category:"font",getInitialValue:e.getInitialValue})),_c('<span class="${prefix}-toolbar-button ${prefix}-icon-large-font ${prefix}-icon"></span>')];var e},Ls=function(n){var e=n.uid!==undefined&&Tt(n,"uid")?n.uid:Fc("memento");return{get:function(n){return n.getSystem().getByUid(e).getOrDie()},getOpt:function(n){return n.getSystem().getByUid(e).fold(F.none,F.some)},asSpec:function(){return k(n,{uid:e})}}};function Us(n,e){return $s(document.createElement("canvas"),n,e)}function Ps(n){return n.getContext("2d")}function $s(n,e,t){return n.width=e,n.height=t,n}var Ws={create:Us,clone:function(n){var e;return Ps(e=Us(n.width,n.height)).drawImage(n,0,0),e},resize:$s,get2dContext:Ps,get3dContext:function(n){var e=null;try{e=n.getContext("webgl")||n.getContext("experimental-webgl")}catch(t){}return e||(e=null),e}},Gs={getWidth:function(n){return n.naturalWidth||n.width},getHeight:function(n){return n.naturalHeight||n.height}},_s=window.Promise?window.Promise:function(){var n=function(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],s(n,r(o,this),r(u,this))},e=n.immediateFn||"function"==typeof window.setImmediate&&window.setImmediate||function(n){setTimeout(n,1)};function r(n,e){return function(){n.apply(e,arguments)}}var t=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)};function i(r){var o=this;null!==this._state?e(function(){var n=o._state?r.onFulfilled:r.onRejected;if(null!==n){var e;try{e=n(o._value)}catch(t){return void r.reject(t)}r.resolve(e)}else(o._state?r.resolve:r.reject)(o._value)}):this._deferreds.push(r)}function o(n){try{if(n===this)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void s(r(e,n),r(o,this),r(u,this))}this._state=!0,this._value=n,a.call(this)}catch(t){u.call(this,t)}}function u(n){this._state=!1,this._value=n,a.call(this)}function a(){for(var n=0,e=this._deferreds.length;n<e;n++)i.call(this,this._deferreds[n]);this._deferreds=null}function c(n,e,t,r){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.resolve=t,this.reject=r}function s(n,e,t){var r=!1;try{n(function(n){r||(r=!0,e(n))},function(n){r||(r=!0,t(n))})}catch(o){if(r)return;r=!0,t(o)}}return n.prototype["catch"]=function(n){return this.then(null,n)},n.prototype.then=function(t,r){var o=this;return new n(function(n,e){i.call(o,new c(t,r,n,e))})},n.all=function(){var c=Array.prototype.slice.call(1===arguments.length&&t(arguments[0])?arguments[0]:arguments);return new n(function(o,i){if(0===c.length)return o([]);var u=c.length;function a(e,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void t.call(n,function(n){a(e,n)},i)}c[e]=n,0==--u&&o(c)}catch(r){i(r)}}for(var n=0;n<c.length;n++)a(n,c[n])})},n.resolve=function(e){return e&&"object"==typeof e&&e.constructor===n?e:new n(function(n){n(e)})},n.reject=function(t){return new n(function(n,e){e(t)})},n.race=function(o){return new n(function(n,e){for(var t=0,r=o.length;t<r;t++)o[t].then(n,e)})},n}();function qs(){return new(Ie.getOrDie("FileReader"))}var Ys={atob:function(n){return Ie.getOrDie("atob")(n)},requestAnimationFrame:function(n){Ie.getOrDie("requestAnimationFrame")(n)}};function Ks(a){return new _s(function(n,e){var t=URL.createObjectURL(a),r=new Image,o=function(){r.removeEventListener("load",i),r.removeEventListener("error",u)};function i(){o(),n(r)}function u(){o(),e("Unable to load data of type "+a.type+": "+t)}r.addEventListener("load",i),r.addEventListener("error",u),r.src=t,r.complete&&i()})}function Xs(r){return new _s(function(n,t){var e=new XMLHttpRequest;e.open("GET",r,!0),e.responseType="blob",e.onload=function(){200==this.status&&n(this.response)},e.onerror=function(){var n,e=this;t(0===this.status?((n=new Error("No access to download image")).code=18,n.name="SecurityError",n):new Error("Error "+e.status+" downloading image"))},e.send()})}function Js(n){var e=n.split(","),t=/data:([^;]+)/.exec(e[0]);if(!t)return F.none();for(var r,o,i,u=t[1],a=e[1],c=Ys.atob(a),s=c.length,f=Math.ceil(s/1024),l=new Array(f),d=0;d<f;++d){for(var m=1024*d,g=Math.min(m+1024,s),v=new Array(g-m),p=m,h=0;p<g;++h,++p)v[h]=c[p].charCodeAt(0);l[d]=(r=v,new(Ie.getOrDie("Uint8Array"))(r))}return F.some((o=l,i={type:u},new(Ie.getOrDie("Blob"))(o,i)))}function Qs(t){return new _s(function(n,e){Js(t).fold(function(){e("uri is not base64: "+t)},n)})}function Zs(t){return new _s(function(n){var e=qs();e.onloadend=function(){n(e.result)},e.readAsDataURL(t)})}var nf,ef,tf,rf,of,uf,af,cf,sf={blobToImage:Ks,imageToBlob:function(n){var e=n.src;return 0===e.indexOf("data:")?Qs(e):Xs(e)},blobToArrayBuffer:function(t){return new _s(function(n){var e=qs();e.onloadend=function(){n(e.result)},e.readAsArrayBuffer(t)})},blobToDataUri:Zs,blobToBase64:function(n){return Zs(n).then(function(n){return n.split(",")[1]})},dataUriToBlobSync:Js,canvasToBlob:function(n,t,r){return t=t||"image/png",HTMLCanvasElement.prototype.toBlob?new _s(function(e){n.toBlob(function(n){e(n)},t,r)}):Qs(n.toDataURL(t,r))},canvasToDataURL:function(n,e,t){return e=e||"image/png",n.then(function(n){return n.toDataURL(e,t)})},blobToCanvas:function(n){return Ks(n).then(function(n){var e,t;return e=n,URL.revokeObjectURL(e.src),t=Ws.create(Gs.getWidth(n),Gs.getHeight(n)),Ws.get2dContext(t).drawImage(n,0,0),t})},uriToBlob:function(n){return 0===n.indexOf("blob:")?Xs(n):0===n.indexOf("data:")?Qs(n):null}},ff=function(n){return sf.blobToBase64(n)},lf=function(u){var e=Ls({dom:{tag:"input",attributes:{accept:"image/*",type:"file",title:""},styles:{visibility:"hidden",position:"absolute"}},events:lr([hr(X()),mr(K(),function(n,e){var t,r,o;(t=e,r=t.event(),o=r.raw().target.files||r.raw().dataTransfer.files,F.from(o[0])).each(function(n){var o,i;o=u,ff(i=n).then(function(r){o.undoManager.transact(function(){var n=o.editorUpload.blobCache,e=n.create(Ja("mceu"),i,r);n.add(e);var t=o.dom.createHTML("img",{src:e.blobUri()});o.insertContent(t)})})})})])});return Pc.sketch({dom:Gc('<span class="${prefix}-toolbar-button ${prefix}-icon-image ${prefix}-icon"></span>'),components:[e.asSpec()],action:function(n){e.get(n).element().dom().click()}})},df=function(n){return n.dom().textContent},mf=function(n){return 0<n.length},gf=function(n){return n===undefined||null===n?"":n},vf=function(e,t,n){return n.text.filter(mf).fold(function(){return Yr(n=e,"href")===df(n)?F.some(t):F.none();var n},F.some)},pf=function(n){var e=se.fromDom(n.selection.getStart());return ji(e,"a")},hf={getInfo:function(n){return pf(n).fold(function(){return{url:"",text:n.selection.getContent({format:"text"}),title:"",target:"",link:F.none()}},function(n){return t=df(e=n),r=Yr(e,"href"),o=Yr(e,"title"),i=Yr(e,"target"),{url:gf(r),text:t!==r?gf(t):"",title:gf(o),target:gf(i),link:F.some(e)};var e,t,r,o,i})},applyInfo:function(o,i){i.url.filter(mf).fold(function(){var e;e=o,i.link.bind(h).each(function(n){e.execCommand("unlink")})},function(e){var n,t,r=(n=i,(t={}).href=e,n.title.filter(mf).each(function(n){t.title=n}),n.target.filter(mf).each(function(n){t.target=n}),t);i.link.bind(h).fold(function(){var n=i.text.filter(mf).getOr(e);o.insertContent(o.dom.createHTML("a",r,o.dom.encode(n)))},function(t){var n=vf(t,e,i);qr(t,r),n.each(function(n){var e;e=n,t.dom().textContent=e})})})},query:pf},bf=$n.detect(),yf=function(n,e){var t=e.selection.getRng();n(),e.selection.setRng(t)},wf=function(n,e){(bf.os.isAndroid()?yf:s)(e,n)},xf=function(n,e){var t,r;return{key:n,value:{config:{},me:(t=n,r=lr(e),Pr({fields:[Zt("enabled")],name:t,active:{events:A(r)}})),configAsRaw:A({}),initialConfig:{},state:zr}}},Sf=Object.freeze({getCurrent:function(n,e,t){return e.find()(n)}}),Tf=[Zt("find")],Of=Pr({fields:Tf,name:"composing",apis:Sf}),kf=Lc({name:"Container",factory:function(n){return{uid:n.uid(),dom:k({tag:"div",attributes:{role:"presentation"}},n.dom()),components:n.components(),behaviours:Ua(n.containerBehaviours()),events:n.events(),domModification:n.domModification(),eventOrder:n.eventOrder()}},configFields:[ur("components",[]),La("containerBehaviours",[]),ur("events",{}),ur("domModification",{}),ur("eventOrder",{})]}),Cf=Lc({name:"DataField",factory:function(t){return{uid:t.uid(),dom:t.dom(),behaviours:k(Lr([Ss.config({store:{mode:"memory",initialValue:t.getInitialValue()()}}),Of.config({find:F.some})]),Ua(t.dataBehaviours())),events:lr([br(function(n,e){Ss.setValue(n,t.getInitialValue()())})])}},configFields:[Zt("uid"),Zt("dom"),Zt("getInitialValue"),La("dataBehaviours",[Ss,Of])]}),Ef=function(n){return n.dom().value},Df=function(n,e){if(e===undefined)throw new Error("Value.set was undefined");n.dom().value=e},If=A([rr("data"),ur("inputAttributes",{}),ur("inputStyles",{}),ur("type","input"),ur("tag","input"),ur("inputClasses",[]),$o("onSetValue"),ur("styles",{}),rr("placeholder"),ur("eventOrder",{}),La("inputBehaviours",[Ss,bi]),ur("selectOnFocus",!0)]),Af=function(n){return k(Lr([Ss.config({store:{mode:"manual",initialValue:n.data().getOr(undefined),getValue:function(n){return Ef(n.element())},setValue:function(n,e){Ef(n.element())!==e&&Df(n.element(),e)}},onSetValue:n.onSetValue()})]),(e=n,Lr([bi.config({onFocus:!1===e.selectOnFocus()?I:function(n){var e=n.element(),t=Ef(e);e.dom().setSelectionRange(0,t.length)}})])),Ua(n.inputBehaviours()));var e},Mf=Lc({name:"Input",configFields:If(),factory:function(n,e){return{uid:n.uid(),dom:(t=n,{tag:t.tag(),attributes:k(xt([{key:"type",value:t.type()}].concat(t.placeholder().map(function(n){return{key:"placeholder",value:n}}).toArray())),t.inputAttributes()),styles:t.inputStyles(),classes:t.inputClasses()}),components:[],behaviours:Af(n),eventOrder:n.eventOrder()};var t}}),Bf=Object.freeze({exhibit:function(n,e){return Cr({attributes:xt([{key:e.tabAttr(),value:"true"}])})}}),Rf=[ur("tabAttr","data-alloy-tabstop")],Ff=Pr({fields:Rf,name:"tabstopping",active:Bf}),Nf=function(n,e){var t=Ls(Mf.sketch({placeholder:e,onSetValue:function(n,e){re(n,Y())},inputBehaviours:Lr([Of.config({find:F.some}),Ff.config({}),ja.config({mode:"execution"})]),selectOnFocus:!1})),r=Ls(Pc.sketch({dom:Gc('<button class="${prefix}-input-container-x ${prefix}-icon-cancel-circle ${prefix}-icon"></button>'),action:function(n){var e=t.get(n);Ss.setValue(e,"")}}));return{name:n,spec:kf.sketch({dom:Gc('<div class="${prefix}-input-container"></div>'),components:[t.asSpec(),r.asSpec()],containerBehaviours:Lr([ci.config({toggleClass:mi.resolve("input-container-empty")}),Of.config({find:function(n){return F.some(t.get(n))}}),xf("input-clearing",[mr(Y(),function(n){var e=t.get(n);(0<Ss.getValue(e).length?ci.off:ci.on)(n)})])])})}},Vf=["input","button","textarea"],Hf=function(n,e,t){e.disabled()&&$f(n,e,t)},zf=function(n){return hn(Vf,me(n.element()))},jf=function(n){_r(n.element(),"disabled","disabled")},Lf=function(n){Xr(n.element(),"disabled")},Uf=function(n){_r(n.element(),"aria-disabled","true")},Pf=function(n){_r(n.element(),"aria-disabled","false")},$f=function(e,n,t){n.disableClass().each(function(n){to(e.element(),n)}),(zf(e)?jf:Uf)(e)},Wf=function(n){return zf(n)?Kr(n.element(),"disabled"):"true"===Yr(n.element(),"aria-disabled")},Gf=Object.freeze({enable:function(e,n,t){n.disableClass().each(function(n){ro(e.element(),n)}),(zf(e)?Lf:Pf)(e)},disable:$f,isDisabled:Wf,onLoad:Hf}),_f=Object.freeze({exhibit:function(n,e,t){return Cr({classes:e.disabled()?e.disableClass().map(Bn).getOr([]):[]})},events:function(n,e){return lr([dr(Yn(),function(n,e){return Wf(n)}),Dr(n,e,Hf)])}}),qf=[ur("disabled",!1),rr("disableClass")],Yf=Pr({fields:qf,name:"disabling",active:_f,apis:Gf}),Kf=[La("formBehaviours",[Ss])],Xf=function(n){return"<alloy.field."+n+">"},Jf=function(o,n,e){return k({"debug.sketcher":{Form:e},uid:o.uid(),dom:o.dom(),components:n,behaviours:k(Lr([Ss.config({store:{mode:"manual",getValue:function(n){var e,t,r=(e=o,t=n.getSystem(),H(e.partUids(),function(n,e){return A(t.getByUid(n))}));return H(r,function(n,e){return n().bind(Of.getCurrent).map(Ss.getValue)})},setValue:function(t,n){V(n,function(e,n){wc(t,o,n).each(function(n){Of.getCurrent(n).each(function(n){Ss.setValue(n,e)})})})}}})]),Ua(o.formBehaviours())),apis:{getField:function(n,e){return wc(n,o,e).bind(Of.getCurrent)}}})},Qf=(Ec(function(n,e,t){return n.getField(e,t)}),function(n){var i,e=(i=[],{field:function(n,e){return i.push(n),t="form",r=Xf(n),o=e,{uiType:Ka(),owner:t,name:r,config:o,validated:{}};var t,r,o},record:function(){return i}}),t=n(e),r=e.record(),o=bn(r,function(n){return lc({name:n,pname:Xf(n)})});return Vc("form",Kf,o,Jf,t)}),Zf=function(){var e=fo(F.none()),t=function(){e.get().each(function(n){n.destroy()})};return{clear:function(){t(),e.set(F.none())},isSet:function(){return e.get().isSome()},set:function(n){t(),e.set(F.some(n))},run:function(n){e.get().each(n)}}},nl=function(){var e=fo(F.none());return{clear:function(){e.set(F.none())},set:function(n){e.set(F.some(n))},isSet:function(){return e.get().isSome()},on:function(n){e.get().each(n)}}},el=function(n){return{xValue:n,points:[]}},tl=function(n,e){if(e===n.xValue)return n;var t=0<e-n.xValue?1:-1,r={direction:t,xValue:e};return{xValue:e,points:(0===n.points.length?[]:n.points[n.points.length-1].direction===t?n.points.slice(0,n.points.length-1):n.points).concat([r])}},rl=function(n){if(0===n.points.length)return 0;var e=n.points[0].direction,t=n.points[n.points.length-1].direction;return-1===e&&-1===t?-1:1===e&&1===t?1:0},ol=function(n){var r="navigateEvent",e=Vt([Zt("fields"),ur("maxFieldIndex",n.fields.length-1),Zt("onExecute"),Zt("getInitialValue"),cr("state",function(){return{dialogSwipeState:nl(),currentScreen:fo(0)}})]),u=qt("SerialisedDialog",e,n),o=function(e,n,t){return Pc.sketch({dom:Gc('<span class="${prefix}-icon-'+n+' ${prefix}-icon"></span>'),action:function(n){oe(n,r,{direction:e})},buttonBehaviours:Lr([Yf.config({disableClass:mi.resolve("toolbar-navigation-disabled"),disabled:!t})])})},i=function(n,o){var i=Ni(n.element(),"."+mi.resolve("serialised-dialog-screen"));zi(n.element(),"."+mi.resolve("serialised-dialog-chain")).each(function(r){0<=u.state.currentScreen.get()+o&&u.state.currentScreen.get()+o<i.length&&(ki(r,"left").each(function(n){var e=parseInt(n,10),t=Es(i[0]);xi(r,"left",e-o*t+"px")}),u.state.currentScreen.set(u.state.currentScreen.get()+o))})},a=function(r){var n=Ni(r.element(),"input");F.from(n[u.state.currentScreen.get()]).each(function(n){r.getSystem().getByDom(n).each(function(n){var e,t;e=r,t=n.element(),e.getSystem().triggerFocus(t,e.element())})});var e=s.get(r);au.highlightAt(e,u.state.currentScreen.get())},c=Ls(Qf(function(t){return{dom:Gc('<div class="${prefix}-serialised-dialog"></div>'),components:[kf.sketch({dom:Gc('<div class="${prefix}-serialised-dialog-chain" style="left: 0px; position: absolute;"></div>'),components:bn(u.fields,function(n,e){return e<=u.maxFieldIndex?kf.sketch({dom:Gc('<div class="${prefix}-serialised-dialog-screen"></div>'),components:En([[o(-1,"previous",0<e)],[t.field(n.name,n.spec)],[o(1,"next",e<u.maxFieldIndex)]])}):t.field(n.name,n.spec)})})],formBehaviours:Lr([fi(function(n,e){var t;t=e,zi(n.element(),"."+mi.resolve("serialised-dialog-chain")).each(function(n){xi(n,"left",-u.state.currentScreen.get()*t.width+"px")})}),ja.config({mode:"special",focusIn:function(n){a(n)},onTab:function(n){return i(n,1),F.some(!0)},onShiftTab:function(n){return i(n,-1),F.some(!0)}}),xf("form-events",[br(function(e,n){u.state.currentScreen.set(0),u.state.dialogSwipeState.clear();var t=s.get(e);au.highlightFirst(t),u.getInitialValue(e).each(function(n){Ss.setValue(e,n)})}),xr(u.onExecute),mr(J(),function(n,e){"left"===e.event().raw().propertyName&&a(n)}),mr(r,function(n,e){var t=e.event().direction();i(n,t)})])])}})),s=Ls({dom:Gc('<div class="${prefix}-dot-container"></div>'),behaviours:Lr([au.config({highlightClass:mi.resolve("dot-active"),itemClass:mi.resolve("dot-item")})]),components:Dn(u.fields,function(n,e){return e<=u.maxFieldIndex?[_c('<div class="${prefix}-dot-item ${prefix}-icon-full-dot ${prefix}-icon"></div>')]:[]})});return{dom:Gc('<div class="${prefix}-serializer-wrapper"></div>'),components:[c.asSpec(),s.asSpec()],behaviours:Lr([ja.config({mode:"special",focusIn:function(n){var e=c.get(n);ja.focusIn(e)}}),xf("serializer-wrapper-events",[mr(L(),function(n,e){var t=e.event();u.state.dialogSwipeState.set(el(t.touches[0].clientX))}),mr(U(),function(n,e){var t=e.event();u.state.dialogSwipeState.on(function(n){e.event().prevent(),u.state.dialogSwipeState.set(tl(n,t.raw().touches[0].clientX))})}),mr(P(),function(r){u.state.dialogSwipeState.on(function(n){var e=c.get(r),t=-1*rl(n);i(e,t)})})])])}},il=Z(function(t,r){return[{label:"the link group",items:[ol({fields:[Nf("url","Type or paste URL"),Nf("text","Link text"),Nf("title","Link title"),Nf("target","Link target"),(n="link",{name:n,spec:Cf.sketch({dom:{tag:"span",styles:{display:"none"}},getInitialValue:function(){return F.none()}})})],maxFieldIndex:["url","text","title","target"].length-1,getInitialValue:function(){return F.some(hf.getInfo(r))},onExecute:function(n){var e=Ss.getValue(n);hf.applyInfo(r,e),t.restoreToolbar(),r.focus()}})]}];var n}),ul=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strikethrough",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Alignment",items:[{title:"Left",icon:"alignleft",format:"alignleft"},{title:"Center",icon:"aligncenter",format:"aligncenter"},{title:"Right",icon:"alignright",format:"alignright"},{title:"Justify",icon:"alignjustify",format:"alignjustify"}]}],al=lr([(nf=Gn(),ef=function(n,e){var t,r,o=e.event().originator(),i=e.event().target();return r=i,!(Ve(t=o,n.element())&&!Ve(t,r)&&(console.warn(Gn()+" did not get interpreted by the desired target. \nOriginator: "+Do(o)+"\nTarget: "+Do(i)+"\nCheck the "+Gn()+" event handlers"),1))},{key:nf,value:sr({can:ef})})]),cl=Object.freeze({events:al}),sl=h,fl=Hr(["debugInfo","triggerFocus","triggerEvent","triggerEscape","addToWorld","removeFromWorld","addToGui","removeFromGui","build","getByUid","getByDom","broadcast","broadcastOn","isConnected"]),ll=function(e){var n=function(n){return function(){throw new Error("The component must be in a context to send: "+n+"\n"+Do(e().element())+" is not in context.")}};return fl({debugInfo:A("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),build:n("build"),addToWorld:n("addToWorld"),removeFromWorld:n("removeFromWorld"),addToGui:n("addToGui"),removeFromGui:n("removeFromGui"),getByUid:n("getByUid"),getByDom:n("getByDom"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),isConnected:A(!1)})},dl=function(n,o){var i={};return V(n,function(n,r){V(n,function(n,e){var t=bt(e,[])(i);i[e]=t.concat([o(r,n)])})}),i},ml=function(n,e){return 1<n.length?nt.error('Multiple behaviours have tried to change DOM "'+e+'". The guilty behaviours are: '+Et(bn(n,function(n){return n.name()}))+". At this stage, this is not supported. Future releases might provide strategies for resolving this."):0===n.length?nt.value({}):nt.value(n[0].modification().fold(function(){return{}},function(n){return wt(e,n)}))},gl=function(u,a){return Sn(u,function(n,e){var t=e.modification().getOr({});return n.bind(function(i){var n=j(t,function(n,e){return i[e]!==undefined?(t=a,r=e,o=u,nt.error("Mulitple behaviours have tried to change the _"+r+'_ "'+t+'". The guilty behaviours are: '+Et(Dn(o,function(n){return n.modification().getOr({})[r]!==undefined?[n.name()]:[]}),null,2)+". This is not currently supported.")):nt.value(wt(e,n));var t,r,o});return St(n,i)})},nt.value({})).map(function(n){return wt(a,n)})},vl={classes:function(n,e){var t=Dn(n,function(n){return n.modification().getOr([])});return nt.value(wt(e,t))},attributes:gl,styles:gl,domChildren:ml,defChildren:ml,innerHtml:ml,value:ml},pl=function(n,e){return t=l.apply(undefined,[n.handler].concat(e)),r=n.purpose(),{cHandler:t,purpose:A(r)};var t,r},hl=function(n){return n.cHandler},bl=function(n,e){return{name:A(n),handler:A(e)}},yl=function(n,e,t){var r,o,i=k(t,(r=n,o={},yn(e,function(n){o[n.name()]=n.handlers(r)}),o));return dl(i,bl)},wl=function(n){var e,i=w(e=n)?{can:A(!0),abort:A(!1),run:e}:e;return function(n,e){for(var t=[],r=2;r<arguments.length;r++)t[r-2]=arguments[r];var o=[n,e].concat(t);i.abort.apply(undefined,o)?e.stop():i.can.apply(undefined,o)&&i.run.apply(undefined,o)}},xl=function(n,e,t){var r,o,i=e[t];return i?function(u,a,n,c){var e=n.slice(0);try{var t=e.sort(function(n,e){var t=n[a](),r=e[a](),o=c.indexOf(t),i=c.indexOf(r);if(-1===o)throw new Error("The ordering for "+u+" does not have an entry for "+t+".\nOrder specified: "+Et(c,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+r+".\nOrder specified: "+Et(c,null,2));return o<i?-1:i<o?1:0});return nt.value(t)}catch(r){return nt.error([r])}}("Event: "+t,"name",n,i).map(function(n){var e=bn(n,function(n){return n.handler()});return fr(e)}):(r=t,o=n,nt.error(["The event ("+r+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+Et(bn(o,function(n){return n.name()}),null,2)]))},Sl=function(n,i){var e=j(n,function(r,o){return(1===r.length?nt.value(r[0].handler()):xl(r,i,o)).map(function(n){var e=wl(n),t=1<r.length?wn(i,function(e){return hn(r,function(n){return n.name()===e})}).join(" > "):r[0].name();return wt(o,{handler:e,purpose:A(t)})})});return St(e,{})},Tl=function(n){return Gt("custom.definition",Nt([Lt("dom","dom",ot(),Nt([Zt("tag"),ur("styles",{}),ur("classes",[]),ur("attributes",{}),rr("value"),rr("innerHtml")])),Zt("components"),Zt("uid"),ur("events",{}),ur("apis",A({})),Lt("eventOrder","eventOrder",(e={"alloy.execute":["disabling","alloy.base.behaviour","toggling"],"alloy.focus":["alloy.base.behaviour","focusing","keying"],"alloy.system.init":["alloy.base.behaviour","disabling","toggling","representing"],input:["alloy.base.behaviour","representing","streaming","invalidating"],"alloy.system.detached":["alloy.base.behaviour","representing"]},tt.mergeWithThunk(A(e))),Jt()),rr("domModification"),Yo("originalSpec"),ur("debug.sketcher","unknown")]),n);var e},Ol=function(n){var e,t={tag:n.dom().tag(),classes:n.dom().classes(),attributes:k((e=n,wt(Ac(),e.uid())),n.dom().attributes()),styles:n.dom().styles(),domChildren:bn(n.components(),function(n){return n.element()})};return Or(k(t,n.dom().innerHtml().map(function(n){return wt("innerHtml",n)}).getOr({}),n.dom().value().map(function(n){return wt("value",n)}).getOr({})))},kl=function(e,n){yn(n,function(n){to(e,n)})},Cl=function(e,n){yn(n,function(n){ro(e,n)})},El=function(e){if(e.domChildren().isSome()&&e.defChildren().isSome())throw new Error("Cannot specify children and child specs! Must be one or the other.\nDef: "+(n=kr(e),Et(n,null,2)));return e.domChildren().fold(function(){var n=e.defChildren().getOr([]);return bn(n,Il)},function(n){return n});var n},Dl=function(n){var e=se.fromTag(n.tag());qr(e,n.attributes().getOr({})),kl(e,n.classes().getOr([])),Si(e,n.styles().getOr({})),ko(e,n.innerHtml().getOr(""));var t=El(n);return $e(e,t),n.value().each(function(n){Df(e,n)}),e},Il=function(n){var e=Or(n);return Dl(e)},Al=function(n,e){return t=n,o=bn(r=e,function(n){return or(n.name(),[Zt("config"),ur("state",zr)])}),i=Gt("component.behaviours",Vt(o),t.behaviours).fold(function(n){throw new Error(Kt(n)+"\nComplete spec:\n"+Et(t,null,2))},function(n){return n}),{list:r,data:H(i,function(n){var e=n().map(function(n){return{config:n.config(),state:n.state().init(n.config())}});return function(){return e}})};var t,r,o,i},Ml=function(n){var e,t,r=(e=yt(n,"behaviours").getOr({}),t=wn(N(e),function(n){return e[n]!==undefined}),bn(t,function(n){return e[n].me}));return Al(n,r)},Bl=Hr(["getSystem","config","hasConfigured","spec","connect","disconnect","element","syncComponents","readState","components","events"]),Rl=function(n,e,t){var r,o,i,u,a=Ol(n),c=function(e,n,t,r){var o=k({},n);yn(t,function(n){o[n.name()]=n.exhibit(e,r)});var i=dl(o,function(n,e){return{name:function(){return n},modification:e}}),u=H(i,function(n,e){return Dn(n,function(e){return e.modification().fold(function(){return[]},function(n){return[e]})})}),a=j(u,function(e,t){return yt(vl,t).fold(function(){return nt.error("Unknown field type: "+t)},function(n){return n(e,t)})});return St(a,{}).map(Cr)}(t,{"alloy.base.modification":(r=n,r.domModification().fold(function(){return Cr({})},Cr))},e,a).getOrDie();return i=c,u=k({tag:(o=a).tag(),classes:i.classes().getOr([]).concat(o.classes().getOr([])),attributes:C(o.attributes().getOr({}),i.attributes().getOr({})),styles:C(o.styles().getOr({}),i.styles().getOr({}))},i.innerHtml().or(o.innerHtml()).map(function(n){return wt("innerHtml",n)}).getOr({}),Er("domChildren",i.domChildren(),o.domChildren()),Er("defChildren",i.defChildren(),o.defChildren()),i.value().or(o.value()).map(function(n){return wt("value",n)}).getOr({})),Or(u)},Fl=function(n,e,t){var r,o,i,u,a,c,s={"alloy.base.behaviour":(r=n,r.events())};return(o=t,i=n.eventOrder(),u=e,a=s,c=yl(o,u,a),Sl(c,i)).getOrDie()},Nl=function(n){var e,t,r,o,i,u,a,c,s,f,l,d,m,g,v=sl(n),p=(e=v,t=bt("components",[])(e),bn(t,zl)),h=k(cl,v,wt("components",p));return nt.value((r=h,i=fo(ll(o=function(){return g})),u=_t(Tl(k(r,{behaviours:undefined}))),a=Ml(r),c=a.list,s=a.data,f=Rl(u,c,s),l=Dl(f),d=Fl(u,c,s),m=fo(u.components()),g=Bl({getSystem:i.get,config:function(n){if(n===Dc())return u.apis();if(b(n))throw new Error("Invalid input: only API constant is allowed");var e=s;return(w(e[n.name()])?e[n.name()]:function(){throw new Error("Could not find "+n.name()+" in "+Et(r,null,2))})()},hasConfigured:function(n){return w(s[n.name()])},spec:A(r),readState:function(n){return s[n]().map(function(n){return n.state.readState()}).getOr("not enabled")},connect:function(n){i.set(n)},disconnect:function(){i.set(ll(o))},element:A(l),syncComponents:function(){var n=je(l),e=Dn(n,function(n){return i.get().getByDom(n).fold(function(){return[]},function(n){return[n]})});m.set(e)},components:m.get,events:A(d)})))},Vl=function(n){var e=se.fromText(n);return Hl({element:e})},Hl=function(n){var t=Yt("external.component",Nt([Zt("element"),rr("uid")]),n),e=fo(ll());t.uid().each(function(n){var e;e=t.element(),_r(e,Bc,n)});var r=Bl({getSystem:e.get,config:F.none,hasConfigured:A(!1),connect:function(n){e.set(n)},disconnect:function(){e.set(ll(function(){return r}))},element:A(t.element()),spec:A(n),readState:A("No state"),syncComponents:I,components:A([]),events:A({})});return Cc(r)},zl=function(e){return(n=e,yt(n,Oc)).fold(function(){var n=k({uid:Fc("")},e);return Nl(n).getOrDie()},function(n){return n});var n},jl=Cc,Ll="alloy.item-hover",Ul="alloy.item-focus",Pl=function(n){(yo(n.element()).isNone()||bi.isFocused(n))&&(bi.isFocused(n)||bi.focus(n),oe(n,Ll,{item:n}))},$l=function(n){oe(n,Ul,{item:n})},Wl=A(Ll),Gl=A(Ul),_l=[Zt("data"),Zt("components"),Zt("dom"),rr("toggling"),ur("itemBehaviours",{}),ur("ignoreFocus",!1),ur("domModification",{}),qo("builder",function(n){return{dom:k(n.dom(),{attributes:{role:n.toggling().isSome()?"menuitemcheckbox":"menuitem"}}),behaviours:k(Lr([n.toggling().fold(ci.revoke,function(n){return ci.config(k({aria:{mode:"checked"}},n))}),bi.config({ignore:n.ignoreFocus(),onFocus:function(n){$l(n)}}),ja.config({mode:"execution"}),Ss.config({store:{mode:"memory",initialValue:n.data()}})]),n.itemBehaviours()),events:lr([(e=Jn(),r=ie,mr(e,function(e,t){var n=t.event();e.getSystem().getByDom(n.target()).each(function(n){r(e,n,t)})})),hr($()),mr(_(),Pl),mr(Kn(),bi.focus)]),components:n.components(),domModification:n.domModification(),eventOrder:n.eventOrder()};var e,r}),ur("eventOrder",{})],ql=[Zt("dom"),Zt("components"),qo("builder",function(n){return{dom:n.dom(),components:n.components(),events:lr([(e=Kn(),mr(e,function(n,e){e.stop()}))])};var e})],Yl=A([lc({name:"widget",overrides:function(e){return{behaviours:Lr([Ss.config({store:{mode:"manual",getValue:function(n){return e.data()},setValue:function(){}}})])}}})]),Kl=[Zt("uid"),Zt("data"),Zt("components"),Zt("dom"),ur("autofocus",!1),ur("domModification",{}),Tc(Yl()),qo("builder",function(t){var n=bc(0,t,Yl()),e=yc("item-widget",t,n.internals()),r=function(n){return wc(n,t,"widget").map(function(n){return ja.focusIn(n),n})},o=function(n,e){return mu(e.event().target())||t.autofocus()&&e.setSource(n.element()),F.none()};return k({dom:t.dom(),components:e,domModification:t.domModification(),events:lr([xr(function(n,e){r(n).each(function(n){e.stop()})}),mr(_(),Pl),mr(Kn(),function(n,e){t.autofocus()?r(n):bi.focus(n)})]),behaviours:Lr([Ss.config({store:{mode:"memory",initialValue:t.data()}}),bi.config({onFocus:function(n){$l(n)}}),ja.config({mode:"special",focusIn:t.autofocus()?function(n){r(n)}:Wr(),onLeft:o,onRight:o,onEscape:function(n,e){return bi.isFocused(n)||t.autofocus()?(t.autofocus()&&e.setSource(n.element()),F.none()):(bi.focus(n),F.some(!0))}})])})})],Xl=Xt("type",{widget:Kl,item:_l,separator:ql}),Jl=A([mc({factory:{sketch:function(n){var e=Yt("menu.spec item",Xl,n);return e.builder()(e)}},name:"items",unit:"item",defaults:function(n,e){var t=Fc("");return k({uid:t},e)},overrides:function(n,e){return{type:e.type,ignoreFocus:n.fakeFocus(),domModification:{classes:[n.markers().item()]}}}})]),Ql=A([Zt("value"),Zt("items"),Zt("dom"),Zt("components"),ur("eventOrder",{}),La("menuBehaviours",[au,Ss,Of,ja]),ar("movement",{mode:"menu",moveOnTab:!0},Xt("mode",{grid:[Ko(),qo("config",function(n,e){return{mode:"flatgrid",selector:"."+n.markers().item(),initSize:{numColumns:e.initSize().numColumns(),numRows:e.initSize().numRows()},focusManager:n.focusManager()}})],menu:[ur("moveOnTab",!0),qo("config",function(n,e){return{mode:"menu",selector:"."+n.markers().item(),moveOnTab:e.moveOnTab(),focusManager:n.focusManager()}})]})),nr("markers",Lo()),ur("fakeFocus",!1),ur("focusManager",cu()),$o("onHighlight")]),Zl=A("alloy.menu-focus"),nd=Uc({name:"Menu",configFields:Ql(),partFields:Jl(),factory:function(n,e,t,r){return k({dom:k(n.dom(),{attributes:{role:"menu"}}),uid:n.uid(),behaviours:k(Lr([au.config({highlightClass:n.markers().selectedItem(),itemClass:n.markers().item(),onHighlight:n.onHighlight()}),Ss.config({store:{mode:"memory",initialValue:n.value()}}),Of.config({find:F.some}),ja.config(n.movement().config()(n,n.movement()))]),Ua(n.menuBehaviours())),events:lr([mr(Gl(),function(e,t){var n=t.event();e.getSystem().getByDom(n.target()).each(function(n){au.highlight(e,n),t.stop(),oe(e,Zl(),{menu:e,item:n})})}),mr(Wl(),function(n,e){var t=e.event().item();au.highlight(n,t)})]),components:e,eventOrder:n.eventOrder()})}}),ed=function(n,e,t,r){var o=n.getSystem().build(r);Ke(n,o,t)},td=function(n,e){return n.components()},rd=Pr({fields:[],name:"replacing",apis:Object.freeze({append:function(n,e,t,r){ed(n,0,Pe,r)},prepend:function(n,e,t,r){ed(n,0,Ue,r)},remove:function(n,e,t,r){var o=td(n,e);Tn(o,function(n){return Ve(r.element(),n.element())}).each(Je)},set:function(e,n,t,r){var o,i,u,a,c,s;i=(o=e).components(),yn(i,Xe),We(o.element()),o.syncComponents(),u=function(){var n=bn(r,e.getSystem().build);yn(n,function(n){Ye(e,n)})},a=e.element(),c=He(a),s=bo(c).bind(function(e){var n=function(n){return Ve(e,n)};return n(a)?F.some(a):vo(a,n)}),u(a),s.each(function(e){bo(c).filter(function(n){return Ve(n,e)}).fold(function(){po(e)},I)})},contents:td})}),od=function(t,r,o,n){return yt(o,n).bind(function(n){return yt(t,n).bind(function(n){var e=od(t,r,o,n);return F.some([n].concat(e))})}).getOr([])},id=function(n,e){var t={};V(n,function(n,e){yn(n,function(n){t[n]=e})});var r=e,o=z(e,function(n,e){return{k:n,v:e}}),i=H(o,function(n,e){return[e].concat(od(t,r,o,e))});return H(t,function(n){return yt(i,n).getOr([n])})},ud=function(){var i=fo({}),u=fo({}),a=fo({}),c=fo(F.none()),s=fo({}),n=function(n){return yt(u.get(),n)};return{setContents:function(n,e,t,r){c.set(F.some(n)),i.set(t),u.set(e),s.set(r);var o=id(r,t);a.set(o)},expand:function(t){return yt(i.get(),t).map(function(n){var e=yt(a.get(),t).getOr([]);return[n].concat(e)})},refresh:function(n){return yt(a.get(),n)},collapse:function(n){return yt(a.get(),n).bind(function(n){return 1<n.length?F.some(n.slice(1)):F.none()})},lookupMenu:n,otherMenus:function(n){var e,t,r=s.get();return e=N(r),t=n,wn(e,function(n){return!hn(t,n)})},getPrimary:function(){return c.get().bind(n)},getMenus:function(){return u.get()},clear:function(){i.set({}),u.set({}),a.set({}),c.set(F.none())},isClear:function(){return c.get().isNone()}}},ad=A("collapse-item"),cd=Lc({name:"TieredMenu",configFields:[_o("onExecute"),_o("onEscape"),Go("onOpenMenu"),Go("onOpenSubmenu"),$o("onCollapseMenu"),ur("openImmediately",!0),tr("data",[Zt("primary"),Zt("menus"),Zt("expansions")]),ur("fakeFocus",!1),$o("onHighlight"),$o("onHover"),tr("markers",[Zt("backgroundMenu")].concat(zo()).concat(jo())),Zt("dom"),ur("navigateOnHover",!0),ur("stayInDom",!1),La("tmenuBehaviours",[ja,au,Of,rd]),ur("eventOrder",{})],apis:{collapseMenu:function(n,e){n.collapseMenu(e)}},factory:function(u,o){var i=function(r,n){return H(n,function(n,e){var t=nd.sketch(k(n,{value:e,items:n.items,markers:vt(o.markers,["item","selectedItem"]),fakeFocus:u.fakeFocus(),onHighlight:u.onHighlight(),focusManager:u.fakeFocus()?{get:function(n){return au.getHighlighted(n).map(function(n){return n.element()})},set:function(e,n){e.getSystem().getByDom(n).fold(I,function(n){au.highlight(e,n)})}}:cu()}));return r.getSystem().build(t)})},a=ud(),c=function(n){return Ss.getValue(n).value},s=function(n){return H(u.data().menus(),function(n,e){return Dn(n.items,function(n){return"separator"===n.type?[]:[n.data.value]})})},f=function(e,n){au.highlight(e,n),au.getHighlighted(n).orThunk(function(){return au.getFirst(n)}).each(function(n){ue(e,n.element(),Kn())})},l=function(n,e){return Ao(bn(e,n.lookupMenu))},d=function(r,o,i){return F.from(i[0]).bind(o.lookupMenu).map(function(n){var e=l(o,i.slice(1));yn(e,function(n){to(n.element(),u.markers().backgroundMenu())}),he(n.element())||rd.append(r,jl(n)),Cl(n.element(),[u.markers().backgroundMenu()]),f(r,n);var t=l(o,o.otherMenus(i));return yn(t,function(n){Cl(n.element(),[u.markers().backgroundMenu()]),u.stayInDom()||rd.remove(r,n)}),n})},m=function(e,t){var n=c(t);return a.expand(n).bind(function(n){return F.from(n[0]).bind(a.lookupMenu).each(function(n){he(n.element())||rd.append(e,jl(n)),u.onOpenSubmenu()(e,t,n),au.highlightFirst(n)}),d(e,a,n)})},r=function(e,t){var n=c(t);return a.collapse(n).bind(function(n){return d(e,a,n).map(function(n){return u.onCollapseMenu()(e,t,n),n})})},n=function(t){return function(e,n){return ji(n.getSource(),"."+u.markers().item()).bind(function(n){return e.getSystem().getByDom(n).toOption().bind(function(n){return t(e,n).map(function(){return!0})})})}},e=lr([mr(Zl(),function(n,e){var t=e.event().menu();au.highlight(n,t)}),xr(function(e,n){var t=n.event().target();e.getSystem().getByDom(t).each(function(n){0===c(n).indexOf("collapse-item")&&r(e,n),m(e,n).fold(function(){u.onExecute()(e,n)},function(){})})}),br(function(e,n){var t,r,o;(t=e,r=i(t,u.data().menus()),o=s(t),a.setContents(u.data().primary(),r,u.data().expansions(),o),a.getPrimary()).each(function(n){rd.append(e,jl(n)),u.openImmediately()&&(f(e,n),u.onOpenMenu()(e,n))})})].concat(u.navigateOnHover()?[mr(Wl(),function(n,e){var t,r,o=e.event().item();t=n,r=c(o),a.refresh(r).bind(function(n){return d(t,a,n)}),m(n,o),u.onHover()(n,o)})]:[]));return{uid:u.uid(),dom:u.dom(),behaviours:k(Lr([ja.config({mode:"special",onRight:n(function(n,e){return mu(e.element())?F.none():m(n,e)}),onLeft:n(function(n,e){return mu(e.element())?F.none():r(n,e)}),onEscape:n(function(n,e){return r(n,e).orThunk(function(){return u.onEscape()(n,e).map(function(){return n})})}),focusIn:function(e,n){a.getPrimary().each(function(n){ue(e,n.element(),Kn())})}}),au.config({highlightClass:u.markers().selectedMenu(),itemClass:u.markers().menu()}),Of.config({find:function(n){return au.getHighlighted(n)}}),rd.config({})]),Ua(u.tmenuBehaviours())),eventOrder:u.eventOrder(),apis:{collapseMenu:function(e){au.getHighlighted(e).each(function(n){au.getHighlighted(n).each(function(n){r(e,n)})})}},events:e}},extraApis:{tieredData:function(n,e,t){return{primary:n,menus:e,expansions:t}},singleData:function(n,e){return{primary:n,menus:wt(n,e),expansions:{}}},collapseItem:function(n){return{value:Ja(ad()),text:n}}}}),sd=function(n,e,t,r){return yt(e.routes(),r.start()).map(s).bind(function(n){return yt(n,r.destination()).map(s)})},fd=function(n,e,t,r){return sd(0,e,0,r).bind(function(e){return e.transition().map(function(n){return{transition:A(n),route:A(e)}})})},ld=function(t,r,n){var e,o,i;(e=t,o=r,i=n,dd(e,o,i).bind(function(n){return fd(e,o,i,n)})).each(function(n){var e=n.transition();ro(t.element(),e.transitionClass()),Xr(t.element(),r.destinationAttr())})},dd=function(n,e,t){var r=n.element();return Kr(r,e.destinationAttr())?F.some({start:A(Yr(n.element(),e.stateAttr())),destination:A(Yr(n.element(),e.destinationAttr()))}):F.none()},md=function(n,e,t,r){ld(n,e,t),Kr(n.element(),e.stateAttr())&&Yr(n.element(),e.stateAttr())!==r&&e.onFinish()(n,r),_r(n.element(),e.stateAttr(),r)},gd=Object.freeze({findRoute:sd,disableTransition:ld,getCurrentRoute:dd,jumpTo:md,progressTo:function(t,r,o,i){var n,e;e=r,Kr((n=t).element(),e.destinationAttr())&&(_r(n.element(),e.stateAttr(),Yr(n.element(),e.destinationAttr())),Xr(n.element(),e.destinationAttr()));var u,a,c=(u=r,a=i,{start:A(Yr(t.element(),u.stateAttr())),destination:A(a)});fd(t,r,o,c).fold(function(){md(t,r,o,i)},function(n){ld(t,r,o);var e=n.transition();to(t.element(),e.transitionClass()),_r(t.element(),r.destinationAttr(),i)})},getState:function(n,e,t){var r=n.element();return Kr(r,e.stateAttr())?F.some(Yr(r,e.stateAttr())):F.none()}}),vd=Object.freeze({events:function(o,i){return lr([mr(J(),function(t,n){var r=n.event().raw();dd(t,o,i).each(function(e){sd(0,o,0,e).each(function(n){n.transition().each(function(n){r.propertyName===n.property()&&(md(t,o,i,e.destination()),o.onTransition()(t,e))})})})}),br(function(n,e){md(n,o,i,o.initialState())})])}}),pd=[ur("destinationAttr","data-transitioning-destination"),ur("stateAttr","data-transitioning-state"),Zt("initialState"),$o("onTransition"),$o("onFinish"),nr("routes",Ht(nt.value,Ht(nt.value,Nt([ir("transition",[Zt("property"),Zt("transitionClass")])]))))],hd=Pr({fields:pd,name:"transitioning",active:vd,apis:gd,extra:{createRoutes:function(n){var r={};return V(n,function(n,e){var t=e.split("<->");r[t[0]]=wt(t[1],n),r[t[1]]=wt(t[0],n)}),r},createBistate:function(n,e,t){return xt([{key:n,value:wt(e,t)},{key:e,value:wt(n,t)}])},createTristate:function(n,e,t,r){return xt([{key:n,value:xt([{key:e,value:r},{key:t,value:r}])},{key:e,value:xt([{key:n,value:r},{key:t,value:r}])},{key:t,value:xt([{key:n,value:r},{key:e,value:r}])}])}}}),bd=mi.resolve("scrollable"),yd={register:function(n){to(n,bd)},deregister:function(n){ro(n,bd)},scrollable:A(bd)},wd=function(n){return yt(n,"format").getOr(n.title)},xd=function(n,e,t,r,o){return{data:{value:n,text:e},type:"item",dom:{tag:"div",classes:o?[mi.resolve("styles-item-is-menu")]:[]},toggling:{toggleOnExecute:!1,toggleClass:mi.resolve("format-matches"),selected:t},itemBehaviours:Lr(o?[]:[si(n,function(n,e){(e?ci.on:ci.off)(n)})]),components:[{dom:{tag:"div",attributes:{style:r},innerHtml:e}}]}},Sd=function(n,e,t,r){return{value:n,dom:{tag:"div"},components:[Pc.sketch({dom:{tag:"div",classes:[mi.resolve("styles-collapser")]},components:r?[{dom:{tag:"span",classes:[mi.resolve("styles-collapse-icon")]}},Vl(n)]:[Vl(n)],action:function(n){if(r){var e=t().get(n);cd.collapseMenu(e)}}}),{dom:{tag:"div",classes:[mi.resolve("styles-menu-items-container")]},components:[nd.parts().items({})],behaviours:Lr([xf("adhoc-scrollable-menu",[br(function(n,e){xi(n.element(),"overflow-y","auto"),xi(n.element(),"-webkit-overflow-scrolling","touch"),yd.register(n.element())}),yr(function(n){Ci(n.element(),"overflow-y"),Ci(n.element(),"-webkit-overflow-scrolling"),yd.deregister(n.element())})])])}],items:e,menuBehaviours:Lr([hd.config({initialState:"after",routes:hd.createTristate("before","current","after",{transition:{property:"transform",transitionClass:"transitioning"}})})])}},Td=function(r){var o,i,n,e,t,u=(o=r.formats,i=function(){return a},n=Sd("Styles",[].concat(bn(o.items,function(n){return xd(wd(n),n.title,n.isSelected(),n.getPreview(),Tt(o.expansions,wd(n)))})),i,!1),e=H(o.menus,function(n,e){var t=bn(n,function(n){return xd(wd(n),n.title,n.isSelected!==undefined&&n.isSelected(),n.getPreview!==undefined?n.getPreview():"",Tt(o.expansions,wd(n)))});return Sd(e,t,i,!0)}),t=k(e,wt("styles",n)),{tmenu:cd.tieredData("styles",t,o.expansions)}),a=Ls(cd.sketch({dom:{tag:"div",classes:[mi.resolve("styles-menu")]},components:[],fakeFocus:!0,stayInDom:!0,onExecute:function(n,e){var t=Ss.getValue(e);return r.handle(e,t.value),F.none()},onEscape:function(){return F.none()},onOpenMenu:function(n,e){var t=Es(n.element());Cs(e.element(),t),hd.jumpTo(e,"current")},onOpenSubmenu:function(n,e,t){var r=Es(n.element()),o=Hi(e.element(),'[role="menu"]').getOrDie("hacky"),i=n.getSystem().getByDom(o).getOrDie();Cs(t.element(),r),hd.progressTo(i,"before"),hd.jumpTo(t,"after"),hd.progressTo(t,"current")},onCollapseMenu:function(n,e,t){var r=Hi(e.element(),'[role="menu"]').getOrDie("hacky"),o=n.getSystem().getByDom(r).getOrDie();hd.progressTo(o,"after"),hd.progressTo(t,"current")},navigateOnHover:!1,openImmediately:!0,data:u.tmenu,markers:{backgroundMenu:mi.resolve("styles-background-menu"),menu:mi.resolve("styles-menu"),selectedMenu:mi.resolve("styles-selected-menu"),item:mi.resolve("styles-item"),selectedItem:mi.resolve("styles-selected-item")}}));return a.asSpec()},Od=function(n){return Tt(n,"items")?(t=k(pt(e=n,["items"]),{menu:!0}),r=kd(e.items),{item:t,menus:k(r.menus,wt(e.title,r.items)),expansions:k(r.expansions,wt(e.title,e.title))}):{item:n,menus:{},expansions:{}};var e,t,r},kd=function(n){return xn(n,function(n,e){var t=Od(e);return{menus:k(n.menus,t.menus),items:[t.item].concat(n.items),expansions:k(n.expansions,t.expansions)}},{menus:{},expansions:{},items:[]})},Cd={expand:kd},Ed=function(u,n){var a=function(n){return function(){return u.formatter.match(n)}},c=function(n){return function(){return u.formatter.getCssText(n)}},e=yt(n,"style_formats").getOr(ul),s=function(n){return bn(n,function(n){if(Tt(n,"items")){var e=s(n.items);return k(k(n,{isSelected:A(!1),getPreview:A("")}),{items:e})}return Tt(n,"format")?k(i=n,{isSelected:a(i.format),getPreview:c(i.format)}):(r=Ja((t=n).title),o=k(t,{format:r,isSelected:a(r),getPreview:c(r)}),u.formatter.register(r,o),o);var t,r,o,i})};return s(e)},Dd=function(t,n,r){var e,o,i,u=(e=t,i=(o=function(n){return Dn(n,function(n){return n.items!==undefined?0<o(n.items).length?[n]:[]:!Tt(n,"format")||e.formatter.canApply(n.format)?[n]:[]})})(n),Cd.expand(i));return Td({formats:u,handle:function(n,e){t.undoManager.transact(function(){ci.isOn(n)?t.formatter.remove(e):t.formatter.apply(e)}),r()}})},Id=["undo","bold","italic","link","image","bullist","styleselect"],Ad=function(n){var e=n.replace(/\|/g," ").trim();return 0<e.length?e.split(/\s+/):[]},Md=function(n){return Dn(n,function(n){return p(n)?Md(n):Ad(n)})},Bd=function(n){var e=n.toolbar!==undefined?n.toolbar:Id;return p(e)?Md(e):Ad(e)},Rd=function(r,o){var n=function(n){return function(){return Kc.forToolbarCommand(o,n)}},e=function(n){return function(){return Kc.forToolbarStateCommand(o,n)}},t=function(n,e,t){return function(){return Kc.forToolbarStateAction(o,n,e,t)}},i=n("undo"),u=n("redo"),a=e("bold"),c=e("italic"),s=e("underline"),f=n("removeformat"),l=t("unlink","link",function(){o.execCommand("unlink",null,!1)}),d=t("unordered-list","ul",function(){o.execCommand("InsertUnorderedList",null,!1)}),m=t("ordered-list","ol",function(){o.execCommand("InsertOrderedList",null,!1)}),g=Ed(o,o.settings),v=function(){return Dd(o,g,function(){o.fire("scrollIntoView")})},p=function(n,e){return{isSupported:function(){return n.forall(function(n){return Tt(o.buttons,n)})},sketch:e}};return{undo:p(F.none(),i),redo:p(F.none(),u),bold:p(F.none(),a),italic:p(F.none(),c),underline:p(F.none(),s),removeformat:p(F.none(),f),link:p(F.none(),function(){return e=r,t=o,Kc.forToolbarStateAction(t,"link","link",function(){var n=il(e,t);e.setContextToolbar(n),wf(t,function(){e.focusToolbar()}),hf.query(t).each(function(n){t.selection.select(n.dom())})});var e,t}),unlink:p(F.none(),l),image:p(F.none(),function(){return lf(o)}),bullist:p(F.some("bullist"),d),numlist:p(F.some("numlist"),m),fontsizeselect:p(F.none(),function(){return e=o,n={onChange:function(n){Hs.apply(e,n)},getInitialValue:function(){return Hs.get(e)}},As(r,"font-size",function(){return js(n)});var e,n}),forecolor:p(F.none(),function(){return Bs(r,o)}),styleselect:p(F.none(),function(){return Kc.forToolbar("style-formats",function(n){o.fire("toReading"),r.dropup().appear(v,ci.on,n)},Lr([ci.config({toggleClass:mi.resolve("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),Jo.config({channels:xt([li(To.orientationChanged(),ci.off),li(To.dropupDismissed(),ci.off)])})]))})}},Fd=function(n,t){var e=Bd(n),r={};return Dn(e,function(n){var e=!Tt(r,n)&&Tt(t,n)&&t[n].isSupported()?[t[n].sketch()]:[];return r[n]=!0,e})},Nd=function(m,g){return function(n){if(m(n)){var e,t,r,o,i,u,a,c=se.fromDom(n.target),s=function(){n.stopPropagation()},f=function(){n.preventDefault()},l=v(f,s),d=(e=c,t=n.clientX,r=n.clientY,o=s,i=f,u=l,a=n,{target:A(e),x:A(t),y:A(r),stop:o,prevent:i,kill:u,raw:A(a)});g(d)}}},Vd=function(n,e,t,r,o){var i=Nd(t,r);return n.dom().addEventListener(e,i,o),{unbind:l(Hd,n,e,i,o)}},Hd=function(n,e,t,r){n.dom().removeEventListener(e,t,r)},zd=A(!0),jd=function(n,e,t){return Vd(n,e,zd,t,!1)},Ld=function(n,e,t){return Vd(n,e,zd,t,!0)},Ud=function(n){var e=n.matchMedia("(orientation: portrait)").matches;return{isPortrait:A(e)}},Pd=Ud,$d=function(r,e){var n=se.fromDom(r),o=null,t=jd(n,"orientationchange",function(){clearInterval(o);var n=Ud(r);e.onChange(n),i(function(){e.onReady(n)})}),i=function(n){clearInterval(o);var e=r.innerHeight,t=0;o=setInterval(function(){e!==r.innerHeight?(clearInterval(o),n(F.some(r.innerHeight))):20<t&&(clearInterval(o),n(F.none())),t++},50)};return{onAdjustment:i,destroy:function(){t.unbind()}}},Wd=function(n){var e=$n.detect().os.isiOS(),t=Ud(n).isPortrait();return e&&!t?n.screen.height:n.screen.width},Gd=function(n){var e=n.raw();return e.touches===undefined||1!==e.touches.length?F.none():F.some(e.touches[0])},_d=function(t){var r,o,i,u=fo(F.none()),a=(r=function(n){u.set(F.none()),t.triggerEvent(Qn(),n)},o=400,i=null,{cancel:function(){null!==i&&(clearTimeout(i),i=null)},schedule:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];i=setTimeout(function(){r.apply(null,n),i=null},o)}}),c=xt([{key:L(),value:function(t){return Gd(t).each(function(n){a.cancel();var e={x:A(n.clientX),y:A(n.clientY),target:t.target};a.schedule(t),u.set(F.some(e))}),F.none()}},{key:U(),value:function(n){return a.cancel(),Gd(n).each(function(i){u.get().each(function(n){var e,t,r,o;e=i,t=n,r=Math.abs(e.clientX-t.x()),o=Math.abs(e.clientY-t.y()),(5<r||5<o)&&u.set(F.none())})}),F.none()}},{key:P(),value:function(e){return a.cancel(),u.get().filter(function(n){return Ve(n.target(),e.target())}).map(function(n){return t.triggerEvent(Xn(),e)})}}]);return{fireIfReady:function(e,n){return yt(c,n).bind(function(n){return n(e)})}}},qd=function(t){var e=_d({triggerEvent:function(n,e){t.onTapContent(e)}});return{fireTouchstart:function(n){e.fireIfReady(n,"touchstart")},onTouchend:function(){return jd(t.body(),"touchend",function(n){e.fireIfReady(n,"touchend")})},onTouchmove:function(){return jd(t.body(),"touchmove",function(n){e.fireIfReady(n,"touchmove")})}}},Yd=6<=$n.detect().os.version.major,Kd=function(r,e,t){var o=qd(r),i=He(e),u=function(n){return!Ve(n.start(),n.finish())||n.soffset()!==n.foffset()},n=function(){var n=r.doc().dom().hasFocus()&&r.getSelection().exists(u);t.getByDom(e).each(!0===(n||bo(i).filter(function(n){return"input"===me(n)}).exists(function(n){return n.dom().selectionStart!==n.dom().selectionEnd}))?ci.on:ci.off)},a=[jd(r.body(),"touchstart",function(n){r.onTouchContent(),o.fireTouchstart(n)}),o.onTouchmove(),o.onTouchend(),jd(e,"touchstart",function(n){r.onTouchToolstrip()}),r.onToReading(function(){ho(r.body())}),r.onToEditing(I),r.onScrollToCursor(function(n){n.preventDefault(),r.getCursorBox().each(function(n){var e=r.win(),t=n.top()>e.innerHeight||n.bottom()>e.innerHeight?n.bottom()-e.innerHeight+50:0;0!==t&&e.scrollTo(e.pageXOffset,e.pageYOffset+t)})})].concat(!0===Yd?[]:[jd(se.fromDom(r.win()),"blur",function(){t.getByDom(e).each(ci.off)}),jd(i,"select",n),jd(r.doc(),"selectionchange",n)]);return{destroy:function(){yn(a,function(n){n.unbind()})}}},Xd=function(n,e){var t=parseInt(Yr(n,e),10);return isNaN(t)?0:t},Jd=(tf=pe,rf="text",of=function(n){return tf(n)?F.from(n.dom().nodeValue):F.none()},uf=$n.detect().browser,{get:function(n){if(!tf(n))throw new Error("Can only get "+rf+" value of a "+rf+" node");return af(n).getOr("")},getOption:af=uf.isIE()&&10===uf.version.major?function(n){try{return of(n)}catch(e){return F.none()}}:of,set:function(n,e){if(!tf(n))throw new Error("Can only set raw "+rf+" value of a "+rf+" node");n.dom().nodeValue=e}}),Qd=function(n){return Jd.getOption(n)},Zd=et([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),nm={before:Zd.before,on:Zd.on,after:Zd.after,cata:function(n,e,t,r){return n.fold(e,t,r)},getStart:function(n){return n.fold(h,h,h)}},em=et([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),tm=we("start","soffset","finish","foffset"),rm=em.relative,om=em.exact,im=function(n,e,t,r){var o,i,u,a,c,s=(i=e,u=t,a=r,(c=He(o=n).dom().createRange()).setStart(o.dom(),i),c.setEnd(u.dom(),a),c),f=Ve(n,t)&&e===r;return s.collapsed&&!f},um=function(n,e,t){var r,o,i=n.document.createRange();return r=i,e.fold(function(n){r.setStartBefore(n.dom())},function(n,e){r.setStart(n.dom(),e)},function(n){r.setStartAfter(n.dom())}),o=i,t.fold(function(n){o.setEndBefore(n.dom())},function(n,e){o.setEnd(n.dom(),e)},function(n){o.setEndAfter(n.dom())}),i},am=function(n,e,t,r,o){var i=n.document.createRange();return i.setStart(e.dom(),t),i.setEnd(r.dom(),o),i},cm=function(n){return{left:A(n.left),top:A(n.top),right:A(n.right),bottom:A(n.bottom),width:A(n.width),height:A(n.height)}},sm=et([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),fm=function(n,e,t){return e(se.fromDom(t.startContainer),t.startOffset,se.fromDom(t.endContainer),t.endOffset)},lm=function(n,e){var o,t,r,i=(o=n,e.match({domRange:function(n){return{ltr:A(n),rtl:F.none}},relative:function(n,e){return{ltr:Z(function(){return um(o,n,e)}),rtl:Z(function(){return F.some(um(o,e,n))})}},exact:function(n,e,t,r){return{ltr:Z(function(){return am(o,n,e,t,r)}),rtl:Z(function(){return F.some(am(o,t,r,n,e))})}}}));return(r=(t=i).ltr()).collapsed?t.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return sm.rtl(se.fromDom(n.endContainer),n.endOffset,se.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return fm(0,sm.ltr,r)}):fm(0,sm.ltr,r)},dm=(document.caretPositionFromPoint||document.caretRangeFromPoint,function(n,e){var t=me(n);return"input"===t?nm.after(n):hn(["br","img"],t)?0===e?nm.before(n):nm.after(n):nm.on(n,e)}),mm=function(n,e,t,r,o){var i,u,a=am(n,e,t,r,o);i=n,u=a,F.from(i.getSelection()).each(function(n){n.removeAllRanges(),n.addRange(u)})},gm=function(n,e,t,r,o){var i,u,a,c,l,s=(i=r,u=o,a=dm(e,t),c=dm(i,u),rm(a,c));lm(l=n,s).match({ltr:function(n,e,t,r){mm(l,n,e,t,r)},rtl:function(n,e,t,r){var o,i,u,a,c,s=l.getSelection();if(s.setBaseAndExtent)s.setBaseAndExtent(n.dom(),e,t.dom(),r);else if(s.extend)try{i=n,u=e,a=t,c=r,(o=s).collapse(i.dom(),u),o.extend(a.dom(),c)}catch(f){mm(l,t,r,n,e)}else mm(l,t,r,n,e)}})},vm=function(n){var e=se.fromDom(n.anchorNode),t=se.fromDom(n.focusNode);return im(e,n.anchorOffset,t,n.focusOffset)?F.some(tm(se.fromDom(n.anchorNode),n.anchorOffset,se.fromDom(n.focusNode),n.focusOffset)):function(n){if(0<n.rangeCount){var e=n.getRangeAt(0),t=n.getRangeAt(n.rangeCount-1);return F.some(tm(se.fromDom(e.startContainer),e.startOffset,se.fromDom(t.endContainer),t.endOffset))}return F.none()}(n)},pm=function(n){return F.from(n.getSelection()).filter(function(n){return 0<n.rangeCount}).bind(vm)},hm=function(n,e){var i,t,r,o,u=lm(i=n,e).match({ltr:function(n,e,t,r){var o=i.document.createRange();return o.setStart(n.dom(),e),o.setEnd(t.dom(),r),o},rtl:function(n,e,t,r){var o=i.document.createRange();return o.setStart(t.dom(),r),o.setEnd(n.dom(),e),o}});return r=(t=u).getClientRects(),0<(o=0<r.length?r[0]:t.getBoundingClientRect()).width||0<o.height?F.some(o).map(cm):F.none()},bm=function(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom,width:A(2),height:n.height}},ym=function(n){return{left:A(n.left),top:A(n.top),right:A(n.right),bottom:A(n.bottom),width:A(n.width),height:A(n.height)}},wm=function(r){if(r.collapsed){var o=se.fromDom(r.startContainer);return ze(o).bind(function(n){var e,t=om(o,r.startOffset,n,"img"===me(e=n)?1:Qd(e).fold(function(){return je(e).length},function(n){return n.length}));return hm(r.startContainer.ownerDocument.defaultView,t).map(bm).map(Bn)}).getOr([])}return bn(r.getClientRects(),ym)},xm=function(n){var e=n.getSelection();return e!==undefined&&0<e.rangeCount?wm(e.getRangeAt(0)):[]},Sm=function(n){n.focus();var e=se.fromDom(n.document.body);(bo().exists(function(n){return hn(["input","textarea"],me(n))})?function(n){setTimeout(function(){n()},0)}:s)(function(){bo().each(ho),po(e)})},Tm="data-"+mi.resolve("last-outer-height"),Om=function(n,e){_r(n,Tm,e)},km=function(n){return{top:A(n.top()),bottom:A(n.top()+n.height())}},Cm=function(n,e){var t=Xd(e,Tm),r=n.innerHeight;return r<t?F.some(t-r):F.none()},Em=function(n,u){var e=se.fromDom(u.document.body),t=jd(se.fromDom(n),"resize",function(){Cm(n,e).each(function(i){var n,e;(n=u,e=xm(n),0<e.length?F.some(e[0]).map(km):F.none()).each(function(n){var e,t,r,o=(e=u,r=i,(t=n).top()>e.innerHeight||t.bottom()>e.innerHeight?Math.min(r,t.bottom()-e.innerHeight+50):0);0!==o&&u.scrollTo(u.pageXOffset,u.pageYOffset+o)})}),Om(e,n.innerHeight)});return Om(e,n.innerHeight),{toEditing:function(){Sm(u)},destroy:function(){t.unbind()}}},Dm=function(n){return F.some(se.fromDom(n.dom().contentWindow.document.body))},Im=function(n){return F.some(se.fromDom(n.dom().contentWindow.document))},Am=function(n){return F.from(n.dom().contentWindow)},Mm=function(n){return Am(n).bind(pm)},Bm=function(n){return n.getFrame()},Rm=function(n,t){return function(e){return e[n].getOrThunk(function(){var n=Bm(e);return function(){return t(n)}})()}},Fm=function(n,e,t,r){return n[t].getOrThunk(function(){return function(n){return jd(e,r,n)}})},Nm=function(n){return{left:A(n.left),top:A(n.top),right:A(n.right),bottom:A(n.bottom),width:A(n.width),height:A(n.height)}},Vm={getBody:Rm("getBody",Dm),getDoc:Rm("getDoc",Im),getWin:Rm("getWin",Am),getSelection:Rm("getSelection",Mm),getFrame:Bm,getActiveApi:function(a){var c=Bm(a);return Dm(c).bind(function(u){return Im(c).bind(function(i){return Am(c).map(function(o){var n=se.fromDom(i.dom().documentElement),e=a.getCursorBox.getOrThunk(function(){return function(){return(n=o,pm(n).map(function(n){return om(n.start(),n.soffset(),n.finish(),n.foffset())})).bind(function(n){return hm(o,n).orThunk(function(){return pm(o).filter(function(n){return Ve(n.start(),n.finish())&&n.soffset()===n.foffset()}).bind(function(n){var e=n.start().dom().getBoundingClientRect();return 0<e.width||0<e.height?F.some(e).map(Nm):F.none()})})});var n}}),t=a.setSelection.getOrThunk(function(){return function(n,e,t,r){gm(o,n,e,t,r)}}),r=a.clearSelection.getOrThunk(function(){return function(){o.getSelection().removeAllRanges()}});return{body:A(u),doc:A(i),win:A(o),html:A(n),getSelection:l(Mm,c),setSelection:t,clearSelection:r,frame:A(c),onKeyup:Fm(a,i,"onKeyup","keyup"),onNodeChanged:Fm(a,i,"onNodeChanged","selectionchange"),onDomChanged:a.onDomChanged,onScrollToCursor:a.onScrollToCursor,onScrollToElement:a.onScrollToElement,onToReading:a.onToReading,onToEditing:a.onToEditing,onToolbarScrollStart:a.onToolbarScrollStart,onTouchContent:a.onTouchContent,onTapContent:a.onTapContent,onTouchToolstrip:a.onTouchToolstrip,getCursorBox:e}})})})}},Hm="data-ephox-mobile-fullscreen-style",zm="position:absolute!important;",jm="top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;",Lm=$n.detect().os.isAndroid(),Um=function(n,e){var t,r,o,i=function(r){return function(n){var e=Yr(n,"style"),t=e===undefined?"no-styles":e.trim();t!==r&&(_r(n,Hm,t),_r(n,"style",r))}},u=(t="*",Ri(n,function(n){return Be(n,t)},r)),a=Dn(u,function(n){var e;return e="*",Fi(n,function(n){return Be(n,e)})}),c=(o=Ti(e,"background-color"))!==undefined&&""!==o?"background-color:"+o+"!important":"background-color:rgb(255,255,255)!important;";yn(a,i("display:none!important;")),yn(u,i(zm+jm+c)),i((!0===Lm?"":zm)+jm+c)(n)},Pm=function(){var n=Fe("["+Hm+"]");yn(n,function(n){var e=Yr(n,Hm);"no-styles"!==e?_r(n,"style",e):Xr(n,"style"),Xr(n,Hm)})},$m=function(){var e=Vi("head").getOrDie(),n=Vi('meta[name="viewport"]').getOrThunk(function(){var n=se.fromTag("meta");return _r(n,"name","viewport"),Pe(e,n),n}),t=Yr(n,"content");return{maximize:function(){_r(n,"content","width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0")},restore:function(){t!==undefined&&null!==t&&0<t.length?_r(n,"content",t):_r(n,"content","user-scalable=yes")}}},Wm=function(e,n){var t=$m(),r=Zf(),o=Zf();return{enter:function(){n.hide(),to(e.container,mi.resolve("fullscreen-maximized")),to(e.container,mi.resolve("android-maximized")),t.maximize(),to(e.body,mi.resolve("android-scroll-reload")),r.set(Em(e.win,Vm.getWin(e.editor).getOrDie("no"))),Vm.getActiveApi(e.editor).each(function(n){Um(e.container,n.body()),o.set(Kd(n,e.toolstrip,e.alloy))})},exit:function(){t.restore(),n.show(),ro(e.container,mi.resolve("fullscreen-maximized")),ro(e.container,mi.resolve("android-maximized")),Pm(),ro(e.body,mi.resolve("android-scroll-reload")),o.clear(),r.clear()}}},Gm=function(t,r){var o=null;return{cancel:function(){null!==o&&(clearTimeout(o),o=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null!==o&&clearTimeout(o),o=setTimeout(function(){t.apply(null,n),o=null},r)}}},_m=function(n,e){var t,r,o,i=Ls(kf.sketch({dom:Gc('<div aria-hidden="true" class="${prefix}-mask-tap-icon"></div>'),containerBehaviours:Lr([ci.config({toggleClass:mi.resolve("mask-tap-icon-selected"),toggleOnExecute:!1})])})),u=(t=n,r=200,o=null,{cancel:function(){null!==o&&(clearTimeout(o),o=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null===o&&(o=setTimeout(function(){t.apply(null,n),o=null},r))}});return kf.sketch({dom:Gc('<div class="${prefix}-disabled-mask"></div>'),components:[kf.sketch({dom:Gc('<div class="${prefix}-content-container"></div>'),components:[Pc.sketch({dom:Gc('<div class="${prefix}-content-tap-section"></div>'),components:[i.asSpec()],action:function(n){u.throttle()},buttonBehaviours:Lr([ci.config({toggleClass:mi.resolve("mask-tap-icon-selected")})])})]})]})},qm=Vt([tr("editor",[Zt("getFrame"),rr("getBody"),rr("getDoc"),rr("getWin"),rr("getSelection"),rr("setSelection"),rr("clearSelection"),rr("cursorSaver"),rr("onKeyup"),rr("onNodeChanged"),rr("getCursorBox"),Zt("onDomChanged"),ur("onTouchContent",I),ur("onTapContent",I),ur("onTouchToolstrip",I),ur("onScrollToCursor",A({unbind:I})),ur("onScrollToElement",A({unbind:I})),ur("onToEditing",A({unbind:I})),ur("onToReading",A({unbind:I})),ur("onToolbarScrollStart",h)]),Zt("socket"),Zt("toolstrip"),Zt("dropup"),Zt("toolbar"),Zt("container"),Zt("alloy"),cr("win",function(n){return He(n.socket).dom().defaultView}),cr("body",function(n){return se.fromDom(n.socket.dom().ownerDocument.body)}),ur("translate",h),ur("setReadOnly",I),ur("readOnlyOnInit",A(!0))]),Ym=function(n){var e=qt("Getting AndroidWebapp schema",qm,n);xi(e.toolstrip,"width","100%");var t=zl(_m(function(){e.setReadOnly(e.readOnlyOnInit()),o.enter()},e.translate));e.alloy.add(t);var r={show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}};Pe(e.container,t.element());var o=Wm(e,r);return{setReadOnly:e.setReadOnly,refreshStructure:I,enter:o.enter,exit:o.exit,destroy:I}},Km=A([ur("shell",!0),La("toolbarBehaviours",[rd])]),Xm=A([dc({name:"groups",overrides:function(n){return{behaviours:Lr([rd.config({})])}}})]),Jm=Uc({name:"Toolbar",configFields:Km(),partFields:Xm(),factory:function(e,n,t,r){var o=function(n){return e.shell()?F.some(n):wc(n,e,"groups")},i=e.shell()?{behaviours:[rd.config({})],components:[]}:{behaviours:[],components:n};return{uid:e.uid(),dom:e.dom(),components:i.components,behaviours:k(Lr(i.behaviours),Ua(e.toolbarBehaviours())),apis:{setGroups:function(n,e){o(n).fold(function(){throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(n){rd.set(n,e)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,e,t){n.setGroups(e,t)}}}),Qm=A([Zt("items"),(cf=["itemClass"],tr("markers",bn(cf,Zt))),La("tgroupBehaviours",[ja])]),Zm=A([mc({name:"items",unit:"item",overrides:function(n){return{domModification:{classes:[n.markers().itemClass()]}}}})]),ng=Uc({name:"ToolbarGroup",configFields:Qm(),partFields:Zm(),factory:function(n,e,t,r){return k({dom:{attributes:{role:"toolbar"}}},{uid:n.uid(),dom:n.dom(),components:e,behaviours:k(Lr([ja.config({mode:"flow",selector:"."+n.markers().itemClass()})]),Ua(n.tgroupBehaviours())),"debug.sketcher":t["debug.sketcher"]})}}),eg="data-"+mi.resolve("horizontal-scroll"),tg=function(n){return"true"===Yr(n,eg)?0<(t=n).dom().scrollLeft||function(n){n.dom().scrollLeft=1;var e=0!==n.dom().scrollLeft;return n.dom().scrollLeft=0,e}(t):0<(e=n).dom().scrollTop||function(n){n.dom().scrollTop=1;var e=0!==n.dom().scrollTop;return n.dom().scrollTop=0,e}(e);var e,t},rg={exclusive:function(n,e){return jd(n,"touchmove",function(n){ji(n.target(),e).filter(tg).fold(function(){n.raw().preventDefault()},I)})},markAsHorizontal:function(n){_r(n,eg,"true")}};function og(){var e=function(n){var e=!0===n.scrollable?"${prefix}-toolbar-scrollable-group":"";return{dom:Gc('<div aria-label="'+n.label+'" class="${prefix}-toolbar-group '+e+'"></div>'),tgroupBehaviours:Lr([xf("adhoc-scrollable-toolbar",!0===n.scrollable?[wr(function(n,e){xi(n.element(),"overflow-x","auto"),rg.markAsHorizontal(n.element()),yd.register(n.element())})]:[])]),components:[kf.sketch({components:[ng.parts().items({})]})],markers:{itemClass:mi.resolve("toolbar-group-item")},items:n.items}},t=zl(Jm.sketch({dom:Gc('<div class="${prefix}-toolbar"></div>'),components:[Jm.parts().groups({})],toolbarBehaviours:Lr([ci.config({toggleClass:mi.resolve("context-toolbar"),toggleOnExecute:!1,aria:{mode:"none"}}),ja.config({mode:"cyclic"})]),shell:!0})),n=zl(kf.sketch({dom:{classes:[mi.resolve("toolstrip")]},components:[jl(t)],containerBehaviours:Lr([ci.config({toggleClass:mi.resolve("android-selection-context-toolbar"),toggleOnExecute:!1})])})),r=function(){Jm.setGroups(t,o.get()),ci.off(t)},o=fo([]);return{wrapper:A(n),toolbar:A(t),createGroups:function(n){return bn(n,v(ng.sketch,e))},setGroups:function(n){o.set(n),r()},setContextToolbar:function(n){ci.on(t),Jm.setGroups(t,n)},restoreToolbar:function(){ci.isOn(t)&&r()},refresh:function(){},focus:function(){ja.focusIn(t)}}}var ig=function(n,e){rd.append(n,jl(e))},ug=function(n,e){rd.remove(n,e)},ag=function(n){return zl(Pc.sketch({dom:Gc('<div class="${prefix}-mask-edit-icon ${prefix}-icon"></div>'),action:function(){n.run(function(n){n.setReadOnly(!1)})}}))},cg=function(){return zl(kf.sketch({dom:Gc('<div class="${prefix}-editor-socket"></div>'),components:[],containerBehaviours:Lr([rd.config({})])}))},sg=function(n,e,t,r){(!0===t?so.toAlpha:so.toOmega)(r),(t?ig:ug)(n,e)},fg=function(e,n){return n.getAnimationRoot().fold(function(){return e.element()},function(n){return n(e)})},lg=function(n){return n.dimension().property()},dg=function(n,e){return n.dimension().getDimension()(e)},mg=function(n,e){var t=fg(n,e);Cl(t,[e.shrinkingClass(),e.growingClass()])},gg=function(n,e){ro(n.element(),e.openClass()),to(n.element(),e.closedClass()),xi(n.element(),lg(e),"0px"),Ei(n.element())},vg=function(n,e){ro(n.element(),e.closedClass()),to(n.element(),e.openClass()),Ci(n.element(),lg(e))},pg=function(n,e,t){t.setCollapsed(),xi(n.element(),lg(e),dg(e,n.element())),Ei(n.element());var r=fg(n,e);to(r,e.shrinkingClass()),gg(n,e),e.onStartShrink()(n)},hg=function(n,e,t){var r=function(n,e){vg(n,e);var t=dg(e,n.element());return gg(n,e),t}(n,e),o=fg(n,e);to(o,e.growingClass()),vg(n,e),xi(n.element(),lg(e),r),t.setExpanded(),e.onStartGrow()(n)},bg=function(n,e,t){var r=fg(n,e);return!0===io(r,e.growingClass())},yg=function(n,e,t){var r=fg(n,e);return!0===io(r,e.shrinkingClass())},wg=Object.freeze({grow:function(n,e,t){t.isExpanded()||hg(n,e,t)},shrink:function(n,e,t){t.isExpanded()&&pg(n,e,t)},immediateShrink:function(n,e,t){var r,o;t.isExpanded()&&(r=n,o=e,t.setCollapsed(),xi(r.element(),lg(o),dg(o,r.element())),Ei(r.element()),mg(r,o),gg(r,o),o.onStartShrink()(r),o.onShrunk()(r))},hasGrown:function(n,e,t){return t.isExpanded()},hasShrunk:function(n,e,t){return t.isCollapsed()},isGrowing:bg,isShrinking:yg,isTransitioning:function(n,e,t){return!0===bg(n,e)||!0===yg(n,e)},toggleGrow:function(n,e,t){(t.isExpanded()?pg:hg)(n,e,t)},disableTransitions:mg}),xg=Object.freeze({exhibit:function(n,e){var t=e.expanded();return Cr(t?{classes:[e.openClass()],styles:{}}:{classes:[e.closedClass()],styles:wt(e.dimension().property(),"0px")})},events:function(t,r){return lr([mr(J(),function(n,e){e.event().raw().propertyName===t.dimension().property()&&(mg(n,t),r.isExpanded()&&Ci(n.element(),t.dimension().property()),(r.isExpanded()?t.onGrown():t.onShrunk())(n))})])}}),Sg=[Zt("closedClass"),Zt("openClass"),Zt("shrinkingClass"),Zt("growingClass"),rr("getAnimationRoot"),$o("onShrunk"),$o("onStartShrink"),$o("onGrown"),$o("onStartGrow"),ur("expanded",!1),nr("dimension",Xt("property",{width:[qo("property","width"),qo("getDimension",function(n){return Es(n)+"px"})],height:[qo("property","height"),qo("getDimension",function(n){return Bi(n)+"px"})]}))],Tg=Pr({fields:Sg,name:"sliding",active:xg,apis:wg,state:Object.freeze({init:function(n){var e=fo(n.expanded());return jr({isExpanded:function(){return!0===e.get()},isCollapsed:function(){return!1===e.get()},setCollapsed:l(e.set,!1),setExpanded:l(e.set,!0),readState:function(){return"expanded: "+e.get()}})}})}),Og=function(e,t){var r=zl(kf.sketch({dom:{tag:"div",classes:[mi.resolve("dropup")]},components:[],containerBehaviours:Lr([rd.config({}),Tg.config({closedClass:mi.resolve("dropup-closed"),openClass:mi.resolve("dropup-open"),shrinkingClass:mi.resolve("dropup-shrinking"),growingClass:mi.resolve("dropup-growing"),dimension:{property:"height"},onShrunk:function(n){e(),t(),rd.set(n,[])},onGrown:function(n){e(),t()}}),fi(function(n,e){o(I)})])})),o=function(n){window.requestAnimationFrame(function(){n(),Tg.shrink(r)})};return{appear:function(n,e,t){!0===Tg.hasShrunk(r)&&!1===Tg.isTransitioning(r)&&window.requestAnimationFrame(function(){e(t),rd.set(r,[n()]),Tg.grow(r)})},disappear:o,component:A(r),element:r.element}},kg=$n.detect().browser.isFirefox(),Cg=Nt([er("triggerEvent"),er("broadcastEvent"),ur("stopBackspace",!0)]),Eg=function(e,n){var t,r,o,i,u,a=qt("Getting GUI events settings",Cg,n),c=$n.detect().deviceType.isTouch()?["touchstart","touchmove","touchend","gesturestart"]:["mousedown","mouseup","mouseover","mousemove","mouseout","click"],s=_d(a),f=bn(c.concat(["selectstart","input","contextmenu","change","transitionend","drag","dragstart","dragend","dragenter","dragleave","dragover","drop"]),function(n){return jd(e,n,function(e){s.fireIfReady(e,n).each(function(n){n&&e.kill()}),a.triggerEvent(n,e)&&e.kill()})}),l=jd(e,"keydown",function(n){var e;a.triggerEvent("keydown",n)?n.kill():!0!==a.stopBackspace||8!==(e=n).raw().which||hn(["input","textarea"],me(e.target()))||n.prevent()}),d=(t=e,r=function(n){a.triggerEvent("focusin",n)&&n.kill()},kg?Ld(t,"focus",r):jd(t,"focusin",r)),m=(o=e,i=function(n){a.triggerEvent("focusout",n)&&n.kill(),setTimeout(function(){a.triggerEvent(_n(),n)},0)},kg?Ld(o,"blur",i):jd(o,"focusout",i)),g=(u=e.dom().ownerDocument.defaultView,se.fromDom(u)),v=jd(g,"scroll",function(n){a.broadcastEvent(ne(),n)&&n.kill()});return{unbind:function(){yn(f,function(n){n.unbind()}),l.unbind(),d.unbind(),m.unbind(),v.unbind()}}},Dg=function(n,e){var t=yt(n,"target").map(function(n){return n()}).getOr(e);return fo(t)},Ig=et([{stopped:[]},{resume:["element"]},{complete:[]}]),Ag=function(n,r,e,t,o,i){var u,a,c,s,f=n(r,t),l=(u=e,a=o,c=fo(!1),s=fo(!1),{stop:function(){c.set(!0)},cut:function(){s.set(!0)},isStopped:c.get,isCut:s.get,event:A(u),setSource:a.set,getSource:a.get});return f.fold(function(){return i.logEventNoHandlers(r,t),Ig.complete()},function(e){var t=e.descHandler();return hl(t)(l),l.isStopped()?(i.logEventStopped(r,e.element(),t.purpose()),Ig.stopped()):l.isCut()?(i.logEventCut(r,e.element(),t.purpose()),Ig.complete()):ze(e.element()).fold(function(){return i.logNoParent(r,e.element(),t.purpose()),Ig.complete()},function(n){return i.logEventResponse(r,e.element(),t.purpose()),Ig.resume(n)})})},Mg=function(e,t,r,n,o,i){return Ag(e,t,r,n,o,i).fold(function(){return!0},function(n){return Mg(e,t,r,n,o,i)},function(){return!1})},Bg=function(n,e,t){var r,o,i=(r=e,o=fo(!1),{stop:function(){o.set(!0)},cut:I,isStopped:o.get,isCut:A(!1),event:A(r),setSource:c("Cannot set source of a broadcasted event"),getSource:c("Cannot get source of a broadcasted event")});return yn(n,function(n){var e=n.descHandler();hl(e)(i)}),i.isStopped()},Rg=function(n,e,t,r,o){var i=Dg(t,r);return Mg(n,e,t,r,i,o)},Fg=function(n,e,t){return go(n,function(n){return e(n).isSome()},t).bind(e)},Ng=we("element","descHandler"),Vg=function(n,e){return{id:A(n),descHandler:A(e)}};function Hg(){var i={};return{registerId:function(r,o,n){V(n,function(n,e){var t=i[e]!==undefined?i[e]:{};t[o]=pl(n,r),i[e]=t})},unregisterId:function(t){V(i,function(n,e){n.hasOwnProperty(t)&&delete n[t]})},filterByType:function(n){return yt(i,n).map(function(n){return j(n,function(n,e){return Vg(e,n)})}).getOr([])},find:function(n,e,t){var o=ht(e)(i);return Fg(t,function(n){return t=o,Rc(r=n).fold(function(){return F.none()},function(n){var e=ht(n);return t.bind(e).map(function(n){return Ng(r,n)})});var t,r},n)}}}function zg(){var r=Hg(),o={},i=function(r){var n=r.element();return Rc(n).fold(function(){return n="uid-",e=r.element(),t=Ja(Mc+n),_r(e,Bc,t),t;var n,e,t},function(n){return n})},u=function(n){Rc(n.element()).each(function(n){o[n]=undefined,r.unregisterId(n)})};return{find:function(n,e,t){return r.find(n,e,t)},filter:function(n){return r.filterByType(n)},register:function(n){var e=i(n);Tt(o,e)&&function(n,e){var t=o[e];if(t!==n)throw new Error('The tagId "'+e+'" is already used by: '+Do(t.element())+"\nCannot use it for: "+Do(n.element())+"\nThe conflicting element is"+(he(t.element())?" ":" not ")+"already in the DOM");u(n)}(n,e);var t=[n];r.registerId(t,e,n.events()),o[e]=n},unregister:u,getById:function(n){return ht(n)(o)}}}var jg=function(t){var r=function(e){return ze(t.element()).fold(function(){return!0},function(n){return Ve(e,n)})},o=zg(),s=function(n,e){return o.find(r,n,e)},n=Eg(t.element(),{triggerEvent:function(u,a){return Ho(u,a.target(),function(n){return e=s,t=u,o=n,i=(r=a).target(),Rg(e,t,r,i,o);var e,t,r,o,i})},broadcastEvent:function(n,e){var t=o.filter(n);return Bg(t,e)}}),i=fl({debugInfo:A("real"),triggerEvent:function(e,t,r){Ho(e,t,function(n){Rg(s,e,r,t,n)})},triggerFocus:function(a,c){Rc(a).fold(function(){po(a)},function(n){Ho(Gn(),a,function(n){var e,t,r,o,i,u;e=s,t=Gn(),r={originator:A(c),kill:I,prevent:I,target:A(a)},i=n,u=Dg(r,o=a),Ag(e,t,r,o,u,i)})})},triggerEscape:function(n,e){i.triggerEvent("keydown",n.element(),e.event())},getByUid:function(n){return m(n)},getByDom:function(n){return g(n)},build:zl,addToGui:function(n){a(n)},removeFromGui:function(n){c(n)},addToWorld:function(n){e(n)},removeFromWorld:function(n){u(n)},broadcast:function(n){l(n)},broadcastOn:function(n,e){d(n,e)},isConnected:A(!0)}),e=function(n){n.connect(i),pe(n.element())||(o.register(n),yn(n.components(),e),i.triggerEvent(Zn(),n.element(),{target:A(n.element())}))},u=function(n){pe(n.element())||(yn(n.components(),u),o.unregister(n)),n.disconnect()},a=function(n){Ye(t,n)},c=function(n){Je(n)},f=function(t){var n=o.filter(qn());yn(n,function(n){var e=n.descHandler();hl(e)(t)})},l=function(n){f({universal:A(!0),data:A(n)})},d=function(n,e){f({universal:A(!1),channels:A(n),data:A(e)})},m=function(n){return o.getById(n).fold(function(){return nt.error(new Error('Could not find component with uid: "'+n+'" in system.'))},nt.value)},g=function(n){var e=Rc(n).getOr("not found");return m(e)};return e(t),{root:A(t),element:t.element,destroy:function(){n.unbind(),Ge(t.element())},add:a,remove:c,getByUid:m,getByDom:g,addToWorld:e,removeFromWorld:u,broadcast:l,broadcastOn:d}},Lg=A(mi.resolve("readonly-mode")),Ug=A(mi.resolve("edit-mode"));function Pg(n){var e=zl(kf.sketch({dom:{classes:[mi.resolve("outer-container")].concat(n.classes)},containerBehaviours:Lr([so.config({alpha:Lg(),omega:Ug()})])}));return jg(e)}var $g=function(n,e){var t=se.fromTag("input");Si(t,{opacity:"0",position:"absolute",top:"-1000px",left:"-1000px"}),Pe(n,t),po(t),e(t),Ge(t)},Wg=function(n){var e=n.getSelection();if(0<e.rangeCount){var t=e.getRangeAt(0),r=n.document.createRange();r.setStart(t.startContainer,t.startOffset),r.setEnd(t.endContainer,t.endOffset),e.removeAllRanges(),e.addRange(r)}},Gg=function(n,e){bo().each(function(n){Ve(n,e)||ho(n)}),n.focus(),po(se.fromDom(n.document.body)),Wg(n)},_g={stubborn:function(n,e,t,r){var o=function(){Gg(e,r)},i=jd(t,"keydown",function(n){hn(["input","textarea"],me(n.target()))||o()});return{toReading:function(){$g(n,ho)},toEditing:o,onToolbarTouch:function(){},destroy:function(){i.unbind()}}},timid:function(n,e,t,r){var o=function(){ho(r)};return{toReading:function(){o()},toEditing:function(){Gg(e,r)},onToolbarTouch:function(){o()},destroy:I}}},qg=function(t,r,o,i,n){var u=function(){r.run(function(n){n.refreshSelection()})},e=function(n,e){var t=n-i.dom().scrollTop;r.run(function(n){n.scrollIntoView(t,t+e)})},a=function(){r.run(function(n){n.clearSelection()})},c=function(){t.getCursorBox().each(function(n){e(n.top(),n.height())}),r.run(function(n){n.syncHeight()})},s=qd(t),f=Gm(c,300),l=[t.onKeyup(function(){a(),f.throttle()}),t.onNodeChanged(u),t.onDomChanged(f.throttle),t.onDomChanged(u),t.onScrollToCursor(function(n){n.preventDefault(),f.throttle()}),t.onScrollToElement(function(n){n.element(),e(r,i)}),t.onToEditing(function(){r.run(function(n){n.toEditing()})}),t.onToReading(function(){r.run(function(n){n.toReading()})}),jd(t.doc(),"touchend",function(n){Ve(t.html(),n.target())||Ve(t.body(),n.target())}),jd(o,"transitionend",function(n){var e;"height"===n.raw().propertyName&&(e=Bi(o),r.run(function(n){n.setViewportOffset(e)}),u(),c())}),Ld(o,"touchstart",function(n){var e;r.run(function(n){n.highlightSelection()}),e=n,r.run(function(n){n.onToolbarTouch(e)}),t.onTouchToolstrip()}),jd(t.body(),"touchstart",function(n){a(),t.onTouchContent(),s.fireTouchstart(n)}),s.onTouchmove(),s.onTouchend(),jd(t.body(),"click",function(n){n.kill()}),jd(o,"touchmove",function(){t.onToolbarScrollStart()})];return{destroy:function(){yn(l,function(n){n.unbind()})}}},Yg=function(n){var t=F.none(),e=[],r=function(n){o()?u(n):e.push(n)},o=function(){return t.isSome()},i=function(n){yn(n,u)},u=function(e){t.each(function(n){setTimeout(function(){e(n)},0)})};return n(function(n){t=F.some(n),i(e),e=[]}),{get:r,map:function(t){return Yg(function(e){r(function(n){e(t(n))})})},isReady:o}},Kg={nu:Yg,pure:function(e){return Yg(function(n){n(e)})}},Xg=function(e){var n=function(n){var r;e((r=n,function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=this;setTimeout(function(){r.apply(t,n)},0)}))},t=function(){return Kg.nu(n)};return{map:function(r){return Xg(function(t){n(function(n){var e=r(n);t(e)})})},bind:function(t){return Xg(function(e){n(function(n){t(n).get(e)})})},anonBind:function(t){return Xg(function(e){n(function(n){t.get(e)})})},toLazy:t,toCached:function(){var e=null;return Xg(function(n){null===e&&(e=t()),e.get(n)})},get:n}},Jg={nu:Xg,pure:function(e){return Xg(function(n){n(e)})}},Qg=function(n,e,t){return Math.abs(n-e)<=t?F.none():n<e?F.some(n+t):F.some(n-t)},Zg=function(){var s=null;return{animate:function(r,o,n,i,e,t){var u=!1,a=function(n){u=!0,e(n)};clearInterval(s);var c=function(n){clearInterval(s),a(n)};s=setInterval(function(){var t=r();Qg(t,o,n).fold(function(){clearInterval(s),a(o)},function(n){if(i(n,c),!u){var e=r();(e!==n||Math.abs(e-o)>Math.abs(t-o))&&(clearInterval(s),a(o))}})},t)}}},nv=function(e,t){return Mo([{width:320,height:480,keyboard:{portrait:300,landscape:240}},{width:320,height:568,keyboard:{portrait:300,landscape:240}},{width:375,height:667,keyboard:{portrait:305,landscape:240}},{width:414,height:736,keyboard:{portrait:320,landscape:240}},{width:768,height:1024,keyboard:{portrait:320,landscape:400}},{width:1024,height:1366,keyboard:{portrait:380,landscape:460}}],function(n){return e<=n.width&&t<=n.height?F.some(n.keyboard):F.none()}).getOr({portrait:t/5,landscape:e/4})},ev=function(n){var e,t=Pd(n).isPortrait(),r=nv((e=n).screen.width,e.screen.height),o=t?r.portrait:r.landscape;return(t?n.screen.height:n.screen.width)-n.innerHeight>o?0:o},tv=function(n,e){var t=He(n).dom().defaultView;return Bi(n)+Bi(e)-ev(t)},rv=tv,ov=function(n,e,t){var r=tv(e,t),o=Bi(e)+Bi(t)-r;xi(n,"padding-bottom",o+"px")},iv=et([{fixed:["element","property","offsetY"]},{scroller:["element","offsetY"]}]),uv="data-"+mi.resolve("position-y-fixed"),av="data-"+mi.resolve("y-property"),cv="data-"+mi.resolve("scrolling"),sv="data-"+mi.resolve("last-window-height"),fv=function(n){return Xd(n,uv)},lv=function(n,e){var t=Yr(n,av);return iv.fixed(n,t,e)},dv=function(n,e){return iv.scroller(n,e)},mv=function(n){var e=fv(n);return("true"===Yr(n,cv)?dv:lv)(n,e)},gv=function(n,e,t){var r=He(n).dom().defaultView.innerHeight;return _r(n,sv,r+"px"),r-e-t},vv=function(n){var e=Ni(n,"["+uv+"]");return bn(e,mv)},pv=function(r,o,i,u){var n,e,t,a,c,s,f,l,d=He(r).dom().defaultView,m=(l=Yr(f=i,"style"),Si(f,{position:"absolute",top:"0px"}),_r(f,uv,"0px"),_r(f,av,"top"),{restore:function(){_r(f,"style",l||""),Xr(f,uv),Xr(f,av)}}),g=Bi(i),v=Bi(u),p=gv(r,g,v),h=(t=g,a=p,s=Yr(c=r,"style"),yd.register(c),Si(c,{position:"absolute",height:a+"px",width:"100%",top:t+"px"}),_r(c,uv,t+"px"),_r(c,cv,"true"),_r(c,av,"top"),{restore:function(){yd.deregister(c),_r(c,"style",s||""),Xr(c,uv),Xr(c,cv),Xr(c,av)}}),b=(e=Yr(n=u,"style"),Si(n,{position:"absolute",bottom:"0px"}),_r(n,uv,"0px"),_r(n,av,"bottom"),{restore:function(){_r(n,"style",e||""),Xr(n,uv),Xr(n,av)}}),y=!0,w=function(){var n=d.innerHeight;return Xd(r,sv)<n},x=function(){if(y){var n=Bi(i),e=Bi(u),t=gv(r,n,e);_r(r,uv,n+"px"),xi(r,"height",t+"px"),xi(u,"bottom",-(n+t+e)+"px"),ov(o,r,u)}};return ov(o,r,u),{setViewportOffset:function(n){_r(r,uv,n+"px"),x()},isExpanding:w,isShrinking:S(w),refresh:x,restore:function(){y=!1,m.restore(),h.restore(),b.restore()}}},hv=fv,bv=Zg(),yv="data-"+mi.resolve("last-scroll-top"),wv=function(n){var e=ki(n,"top").getOr("0");return parseInt(e,10)},xv=function(n){return parseInt(n.dom().scrollTop,10)},Sv=function(n,e){var t=e+hv(n)+"px";xi(n,"top",t)},Tv=function(t,r,o){return Jg.nu(function(n){var e=l(xv,t);bv.animate(e,r,15,function(n){t.dom().scrollTop=n,xi(t,"top",wv(t)+15+"px")},function(){t.dom().scrollTop=r,xi(t,"top",o+"px"),n(r)},10)})},Ov=function(o,i){return Jg.nu(function(n){var e=l(xv,o);_r(o,yv,e());var t=Math.abs(i-e()),r=Math.ceil(t/10);bv.animate(e,i,r,function(n,e){Xd(o,yv)!==o.dom().scrollTop?e(o.dom().scrollTop):(o.dom().scrollTop=n,_r(o,yv,n))},function(){o.dom().scrollTop=i,_r(o,yv,i),n(i)},10)})},kv=function(i,u){return Jg.nu(function(n){var e=l(wv,i),t=function(n){xi(i,"top",n+"px")},r=Math.abs(u-e()),o=Math.ceil(r/10);bv.animate(e,u,o,t,function(){t(u),n(u)},10)})},Cv=function(e,t,r){var o=He(e).dom().defaultView;return Jg.nu(function(n){Sv(e,r),Sv(t,r),o.scrollTo(0,r),n(r)})},Ev=function(n,e,t,r,o){var i=rv(e,t),u=l(Wg,n);i<r||i<o?Ov(e,e.dom().scrollTop-i+o).get(u):r<0&&Ov(e,e.dom().scrollTop+r).get(u)},Dv=function(u,n){return n(function(r){var o=[],i=0;0===u.length?r([]):yn(u,function(n,e){var t;n.get((t=e,function(n){o[t]=n,++i>=u.length&&r(o)}))})})},Iv=function(n,c){return n.fold(function(n,e,t){return xi(n,e,c+(r=t)+"px"),Jg.pure(r);var r},function(n,e){return o=c+(r=e),i=ki(t=n,"top").getOr(r),u=o-parseInt(i,10),a=t.dom().scrollTop+u,Tv(t,a,o);var t,r,o,i,u,a})},Av=function(n,e){var t=vv(n),r=bn(t,function(n){return Iv(n,e)});return Dv(r,Jg.nu)},Mv=function(e,t,n,r,o,i){var u,a,c=(u=function(n){return Cv(e,t,n)},a=fo(Kg.pure({})),{start:function(e){var n=Kg.nu(function(n){return u(e).get(n)});a.set(n)},idle:function(n){a.get().get(function(){n()})}}),s=Gm(function(){c.idle(function(){Av(n,r.pageYOffset).get(function(){var n;(n=xm(i),F.from(n[0]).bind(function(n){var e=n.top()-t.dom().scrollTop;return e>r.innerHeight+5||e<-5?F.some({top:A(e),bottom:A(e+n.height())}):F.none()})).each(function(n){t.dom().scrollTop=t.dom().scrollTop+n.top()}),c.start(0),o.refresh()})})},1e3),f=jd(se.fromDom(r),"scroll",function(){r.pageYOffset<0||s.throttle()});return Av(n,r.pageYOffset).get(h),{unbind:f.unbind}},Bv=function(n){var t=n.cWin(),e=n.ceBody(),r=n.socket(),o=n.toolstrip(),i=n.toolbar(),u=n.contentElement(),a=n.keyboardType(),c=n.outerWindow(),s=n.dropup(),f=pv(r,e,o,s),l=a(n.outerBody(),t,be(),u,o,i),d=$d(c,{onChange:I,onReady:f.refresh});d.onAdjustment(function(){f.refresh()});var m=jd(se.fromDom(c),"resize",function(){f.isExpanding()&&f.refresh()}),g=Mv(o,r,n.outerBody(),c,f,t),v=function(t,e){var n=t.document,r=se.fromTag("div");to(r,mi.resolve("unfocused-selections")),Pe(se.fromDom(n.documentElement),r);var o=jd(r,"touchstart",function(n){n.prevent(),Gg(t,e),u()}),i=function(n){var e=se.fromTag("span");return kl(e,[mi.resolve("layer-editor"),mi.resolve("unfocused-selection")]),Si(e,{left:n.left()+"px",top:n.top()+"px",width:n.width()+"px",height:n.height()+"px"}),e},u=function(){We(r)};return{update:function(){u();var n=xm(t),e=bn(n,i);$e(r,e)},isActive:function(){return 0<je(r).length},destroy:function(){o.unbind(),Ge(r)},clear:u}}(t,u),p=function(){v.clear()};return{toEditing:function(){l.toEditing(),p()},toReading:function(){l.toReading()},onToolbarTouch:function(n){l.onToolbarTouch(n)},refreshSelection:function(){v.isActive()&&v.update()},clearSelection:p,highlightSelection:function(){v.update()},scrollIntoView:function(n,e){Ev(t,r,s,n,e)},updateToolbarPadding:I,setViewportOffset:function(n){f.setViewportOffset(n),kv(r,n).get(h)},syncHeight:function(){xi(u,"height",u.dom().contentWindow.document.body.scrollHeight+"px")},refreshStructure:f.refresh,destroy:function(){f.restore(),d.destroy(),g.unbind(),m.unbind(),l.destroy(),v.destroy(),$g(be(),ho)}}},Rv=function(r,n){var o=$m(),i=nl(),u=nl(),a=Zf(),c=Zf();return{enter:function(){n.hide();var t=se.fromDom(document);Vm.getActiveApi(r.editor).each(function(n){i.set({socketHeight:ki(r.socket,"height"),iframeHeight:ki(n.frame(),"height"),outerScroll:document.body.scrollTop}),u.set({exclusives:rg.exclusive(t,"."+yd.scrollable())}),to(r.container,mi.resolve("fullscreen-maximized")),Um(r.container,n.body()),o.maximize(),xi(r.socket,"overflow","scroll"),xi(r.socket,"-webkit-overflow-scrolling","touch"),po(n.body());var e=Ce(["cWin","ceBody","socket","toolstrip","toolbar","dropup","contentElement","cursor","keyboardType","isScrolling","outerWindow","outerBody"],[]);a.set(Bv(e({cWin:n.win(),ceBody:n.body(),socket:r.socket,toolstrip:r.toolstrip,toolbar:r.toolbar,dropup:r.dropup.element(),contentElement:n.frame(),cursor:I,outerBody:r.body,outerWindow:r.win,keyboardType:_g.stubborn,isScrolling:function(){return u.get().exists(function(n){return n.socket.isScrolling()})}}))),a.run(function(n){n.syncHeight()}),c.set(qg(n,a,r.toolstrip,r.socket,r.dropup))})},refreshStructure:function(){a.run(function(n){n.refreshStructure()})},exit:function(){o.restore(),c.clear(),a.clear(),n.show(),i.on(function(n){n.socketHeight.each(function(n){xi(r.socket,"height",n)}),n.iframeHeight.each(function(n){xi(r.editor.getFrame(),"height",n)}),document.body.scrollTop=n.scrollTop}),i.clear(),u.on(function(n){n.exclusives.unbind()}),u.clear(),ro(r.container,mi.resolve("fullscreen-maximized")),Pm(),yd.deregister(r.toolbar),Ci(r.socket,"overflow"),Ci(r.socket,"-webkit-overflow-scrolling"),ho(r.editor.getFrame()),Vm.getActiveApi(r.editor).each(function(n){n.clearSelection()})}}},Fv=function(n){var e=qt("Getting IosWebapp schema",qm,n);xi(e.toolstrip,"width","100%"),xi(e.container,"position","relative");var t=zl(_m(function(){e.setReadOnly(e.readOnlyOnInit()),r.enter()},e.translate));e.alloy.add(t);var r=Rv(e,{show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}});return{setReadOnly:e.setReadOnly,refreshStructure:r.refreshStructure,enter:r.enter,exit:r.exit,destroy:I}},Nv=tinymce.util.Tools.resolve("tinymce.EditorManager"),Vv=function(n){var e=yt(n.settings,"skin_url").fold(function(){return Nv.baseURL+"/skins/lightgray"},function(n){return n});return{content:e+"/content.mobile.min.css",ui:e+"/skin.mobile.min.css"}},Hv=function(n,e,t){n.system().broadcastOn([To.formatChanged()],{command:e,state:t})},zv=function(r,n){var e=N(n.formatter.get());yn(e,function(e){n.formatter.formatChanged(e,function(n){Hv(r,e,n)})}),yn(["ul","ol"],function(t){n.selection.selectorChanged(t,function(n,e){Hv(r,t,n)})})},jv=(A(["x-small","small","medium","large","x-large"]),function(n){var e=function(){n._skinLoaded=!0,n.fire("SkinLoaded")};return function(){n.initialized?e():n.on("init",e)}}),Lv=A("toReading"),Uv=A("toEditing");xo.add("mobile",function(D){return{getNotificationManagerImpl:function(){return{open:A({progressBar:{value:I},close:I}),close:I,reposition:I,getArgs:h}},renderUI:function(n){var e=Vv(D);0==(!1===D.settings.skin)?(D.contentCSS.push(e.content),wo.DOM.styleSheetLoader.load(e.ui,jv(D))):jv(D)();var t,r,o,i,u,a,c,s,f,l,d,m,g,v,p,h,b,y=function(){D.fire("scrollIntoView")},w=se.fromTag("div"),x=$n.detect().os.isAndroid()?(s=y,f=Pg({classes:[mi.resolve("android-container")]}),l=og(),d=Zf(),m=ag(d),g=cg(),v=Og(I,s),f.add(l.wrapper()),f.add(g),f.add(v.component()),{system:A(f),element:f.element,init:function(n){d.set(Ym(n))},exit:function(){d.run(function(n){n.exit(),rd.remove(g,m)})},setToolbarGroups:function(n){var e=l.createGroups(n);l.setGroups(e)},setContextToolbar:function(n){var e=l.createGroups(n);l.setContextToolbar(e)},focusToolbar:function(){l.focus()},restoreToolbar:function(){l.restoreToolbar()},updateMode:function(n){sg(g,m,n,f.root())},socket:A(g),dropup:A(v)}):(t=y,r=Pg({classes:[mi.resolve("ios-container")]}),o=og(),i=Zf(),u=ag(i),a=cg(),c=Og(function(){i.run(function(n){n.refreshStructure()})},t),r.add(o.wrapper()),r.add(a),r.add(c.component()),{system:A(r),element:r.element,init:function(n){i.set(Fv(n))},exit:function(){i.run(function(n){rd.remove(a,u),n.exit()})},setToolbarGroups:function(n){var e=o.createGroups(n);o.setGroups(e)},setContextToolbar:function(n){var e=o.createGroups(n);o.setContextToolbar(e)},focusToolbar:function(){o.focus()},restoreToolbar:function(){o.restoreToolbar()},updateMode:function(n){sg(a,u,n,r.root())},socket:A(a),dropup:A(c)}),S=se.fromDom(n.targetNode);we("element","offset"),h=w,(b=(p=S).dom(),F.from(b.nextSibling).map(se.fromDom)).fold(function(){ze(p).each(function(n){Pe(n,h)})},function(n){var e,t;t=h,ze(e=n).each(function(n){n.dom().insertBefore(t.dom(),e.dom())})}),function(n,e){Pe(n,e.element());var t=je(e.element());yn(t,function(n){e.getByDom(n).each(qe)})}(w,x.system());var T=n.targetNode.ownerDocument.defaultView,O=$d(T,{onChange:function(){x.system().broadcastOn([To.orientationChanged()],{width:Wd(T)})},onReady:I}),k=function(n,e,t,r){!1===r&&D.selection.collapse();var o=C(n,e,t);x.setToolbarGroups(!0===r?o.readOnly:o.main),D.setMode(!0===r?"readonly":"design"),D.fire(!0===r?Lv():Uv()),x.updateMode(r)},C=function(n,e,t){var r=n.get();return{readOnly:r.backToMask.concat(e.get()),main:r.backToMask.concat(t.get())}},E=function(n,e){return D.on(n,e),{unbind:function(){D.off(n)}}};return D.on("init",function(){x.init({editor:{getFrame:function(){return se.fromDom(D.contentAreaContainer.querySelector("iframe"))},onDomChanged:function(){return{unbind:I}},onToReading:function(n){return E(Lv(),n)},onToEditing:function(n){return E(Uv(),n)},onScrollToCursor:function(e){return D.on("scrollIntoView",function(n){e(n)}),{unbind:function(){D.off("scrollIntoView"),O.destroy()}}},onTouchToolstrip:function(){t()},onTouchContent:function(){var n,e=se.fromDom(D.editorContainer.querySelector("."+mi.resolve("toolbar")));(n=e,yo(n).bind(function(n){return x.system().getByDom(n).toOption()})).each(ie),x.restoreToolbar(),t()},onTapContent:function(n){var e=n.target();"img"===me(e)?(D.selection.select(e.dom()),n.kill()):"a"===me(e)&&x.system().getByDom(se.fromDom(D.editorContainer)).each(function(n){so.isAlpha(n)&&So(e.dom())})}},container:se.fromDom(D.editorContainer),socket:se.fromDom(D.contentAreaContainer),toolstrip:se.fromDom(D.editorContainer.querySelector("."+mi.resolve("toolstrip"))),toolbar:se.fromDom(D.editorContainer.querySelector("."+mi.resolve("toolbar"))),dropup:x.dropup(),alloy:x.system(),translate:I,setReadOnly:function(n){k(c,a,u,n)},readOnlyOnInit:function(){return!1}});var t=function(){x.dropup().disappear(function(){x.system().broadcastOn([To.dropupDismissed()],{})})},n={label:"The first group",scrollable:!1,items:[Kc.forToolbar("back",function(){D.selection.collapse(),x.exit()},{})]},e={label:"Back to read only",scrollable:!1,items:[Kc.forToolbar("readonly-back",function(){k(c,a,u,!0)},{})]},r=Rd(x,D),o=Fd(D.settings,r),i={label:"The extra group",scrollable:!1,items:[]},u=fo([{label:"the action group",scrollable:!0,items:o},i]),a=fo([{label:"The read only mode group",scrollable:!0,items:[]},i]),c=fo({backToMask:[n],backToReadOnly:[e]});zv(x,D)}),{iframeContainer:x.socket().element().dom(),editorContainer:x.element().dom()}}}})}();