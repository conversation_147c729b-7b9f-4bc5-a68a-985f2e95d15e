tinymce.addI18n('vi_VN',{
"Redo": "Ho\u00e0n t\u00e1t",
"Undo": "Hu\u1ef7 thao t\u00e1c",
"Cut": "C\u1eaft",
"Copy": "Ch\u00e9p",
"Paste": "D\u00e1n",
"Select all": "Ch\u1ecdn t\u1ea5t c\u1ea3",
"New document": "T\u1ea1o t\u00e0i li\u1ec7u m\u1edbi",
"Ok": "OK",
"Cancel": "Hu\u1ef7",
"Visual aids": "Hi\u1ec7n khung so\u1ea1n th\u1ea3o",
"Bold": "T\u00f4 \u0111\u1eadm",
"Italic": "In nghi\u00eang",
"Underline": "G\u1ea1ch d\u01b0\u1edbi",
"Strikethrough": "G\u1ea1ch ngang",
"Superscript": "Tr\u00ean d\u00f2ng",
"Subscript": "D\u01b0\u1edbi d\u00f2ng",
"Clear formatting": "Xo\u00e1 \u0111\u1ecbnh d\u1ea1ng",
"Align left": "Canh tr\u00e1i",
"Align center": "Canh gi\u1eefa",
"Align right": "Canh ph\u1ea3i",
"Justify": "Canh \u0111\u1ec1u hai b\u00ean",
"Bullet list": "D\u1ea5u \u0111\u1ea7u d\u00f2ng",
"Numbered list": "Danh s\u00e1ch s\u1ed1",
"Decrease indent": "L\u00f9i ra",
"Increase indent": "L\u00f9i v\u00e0o",
"Close": "\u0110\u00f3ng",
"Formats": "\u0110\u1ecbnh d\u1ea1ng",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "Tr\u00ecnh duy\u1ec7t c\u1ee7a b\u1ea1n kh\u00f4ng h\u1ed7 tr\u1ee3 truy c\u1eadp clipboard, vui l\u00f2ng s\u1eed d\u1ee5ng c\u00e1c t\u1ed5 h\u1ee3p Ctrl + X, C, V.",
"Headers": "\u0110\u1ea7u trang",
"Header 1": "Ti\u00eau \u0111\u1ec1 1",
"Header 2": "Ti\u00eau \u0111\u1ec1 2",
"Header 3": "Ti\u00eau \u0111\u1ec1 3",
"Header 4": "Ti\u00eau \u0111\u1ec1 4",
"Header 5": "Ti\u00eau \u0111\u1ec1 5",
"Header 6": "Ti\u00eau \u0111\u1ec1 6",
"Headings": "Ti\u00eau \u0111\u1ec1",
"Heading 1": "Ti\u00eau \u0111\u1ec1 1",
"Heading 2": "Ti\u00eau \u0111\u1ec1 2",
"Heading 3": "Ti\u00eau \u0111\u1ec1 3",
"Heading 4": "Ti\u00eau \u0111\u1ec1 4",
"Heading 5": "Ti\u00eau \u0111\u1ec1 5",
"Heading 6": "Ti\u00eau \u0111\u1ec1 6",
"Div": "Khung",
"Pre": "\u0110\u1ecbnh d\u1ea1ng",
"Code": "M\u00e3",
"Paragraph": "\u0110o\u1ea1n v\u0103n",
"Blockquote": "Tr\u00edch",
"Inline": "C\u00f9ng d\u00f2ng",
"Blocks": "Bao",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "D\u00e1n b\u00e2y gi\u1edd l\u00e0 \u1edf ch\u1ebf \u0111\u1ed9 v\u0103n b\u1ea3n \u0111\u01a1n gi\u1ea3n. N\u1ed9i dung s\u1ebd \u0111\u01b0\u1ee3c d\u00e1n nh\u01b0 \u0111\u1ed3ng b\u1eb1ng v\u0103n b\u1ea3n cho \u0111\u1ebfn khi b\u1ea1n chuy\u1ec3n \u0111\u1ed5i t\u00f9y ch\u1ecdn n\u00e0y.",
"Font Family": "Ph\u00f4ng",
"Font Sizes": "K\u00edch th\u01b0\u1edbc ph\u00f4ng",
"Class": "L\u1edbp",
"Browse for an image": "Duy\u1ec7t \u1ea3nh",
"OR": "HO\u1eb6C",
"Drop an image here": "Th\u1ea3 h\u00ecnh \u1ea3nh \u1edf \u0111\u00e2y",
"Upload": "T\u1ea3i l\u00ean",
"Block": "Kh\u1ed1i",
"Align": "C\u0103n ch\u1ec9nh",
"Default": "Ng\u1ea7m \u0111\u1ecbnh",
"Circle": "H\u00ecnh tr\u00f2n",
"Disc": "H\u00ecnh tr\u00f2n m\u1ecfng",
"Square": "\u00d4 vu\u00f4ng",
"Lower Alpha": "K\u00fd t\u1ef1 th\u01b0\u1eddng",
"Lower Greek": "S\u1ed1 hy l\u1ea1p th\u01b0\u1eddng",
"Lower Roman": "S\u1ed1 la m\u00e3 th\u01b0\u1eddng",
"Upper Alpha": "K\u00fd t\u1ef1 hoa",
"Upper Roman": "S\u1ed1 la m\u00e3 hoa",
"Anchor": "Neo",
"Name": "T\u00ean",
"Id": "Id",
"Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.": "Id ph\u1ea3i b\u1eaft \u0111\u1ea7u b\u1eb1ng m\u1ed9t ch\u1eef c\u00e1i, ch\u1ec9 theo sau b\u1edfi c\u00e1c ch\u1eef c\u00e1i, s\u1ed1, d\u1ea5u g\u1ea1ch ngang, d\u1ea5u ch\u1ea5m, d\u1ea5u hai ch\u1ea5m ho\u1eb7c d\u1ea5u g\u1ea1ch d\u01b0\u1edbi.",
"You have unsaved changes are you sure you want to navigate away?": "B\u1ea1n ch\u01b0a l\u01b0u c\u00e1c thay \u0111\u1ed5i, b\u1ea1n c\u00f3 th\u1eadt s\u1ef1 mu\u1ed1n \u0111\u00f3ng ?",
"Restore last draft": "Ph\u1ee5c h\u1ed3i b\u1ea3n l\u01b0u g\u1ea7n nh\u1ea5t",
"Special character": "K\u00fd t\u1ef1 \u0111\u1eb7c bi\u1ec7t",
"Source code": "M\u00e3 ngu\u1ed3n",
"Insert\/Edit code sample": "Ch\u00e8n\/Ch\u1ec9nh s\u1eeda m\u1eabu",
"Language": "Ng\u00f4n ng\u1eef",
"Code sample": "\u0110o\u1ea1n m\u00e3 m\u1eabu",
"Color": "M\u00e0u",
"R": "R",
"G": "G",
"B": "B",
"Left to right": "Tr\u00e1i sang ph\u1ea3i",
"Right to left": "Ph\u1ea3i sang tr\u00e1i",
"Emoticons": "Bi\u1ec3u t\u01b0\u1ee3ng c\u1ea3m x\u00fac",
"Document properties": "Thu\u1ed9c t\u00ednh t\u00e0i li\u1ec7u",
"Title": "Ti\u00eau \u0111\u1ec1",
"Keywords": "T\u1eeb kho\u00e1",
"Description": "Mi\u00eau t\u1ea3",
"Robots": "Robots",
"Author": "Neo",
"Encoding": "M\u00e3 ho\u00e1",
"Fullscreen": "\u0110\u1ea7y m\u00e0n h\u00ecnh",
"Action": "H\u00e0nh \u0111\u1ed9ng",
"Shortcut": "L\u1ed1i t\u1eaft",
"Help": "Tr\u1ee3 gi\u00fap",
"Address": "\u0110\u1ecba ch\u1ec9",
"Focus to menubar": "G\u1eafn l\u00ean thanh tr\u00ecnh \u0111\u01a1n",
"Focus to toolbar": "G\u1eafn l\u00ean thanh c\u00f4ng c\u1ee5",
"Focus to element path": "G\u1eafn v\u00e0o \u0111\u01b0\u1eddng d\u1eabn",
"Focus to contextual toolbar": "G\u1eafn v\u00e0o thanh c\u00f4ng c\u1ee5 ng\u1eef c\u1ea3nh",
"Insert link (if link plugin activated)": "Ch\u00e8n li\u00ean k\u1ebft (n\u1ebfu plugin li\u00ean k\u1ebft \u0111\u1ea3 k\u00edch ho\u1ea1t)",
"Save (if save plugin activated)": "L\u01b0u (n\u1ebfu plugin l\u01b0u \u0111\u1ea3 k\u00edch ho\u1ea1t)",
"Find (if searchreplace plugin activated)": "T\u00ecm (n\u1ebfu plugin t\u00ecm v\u00e0 thay th\u1ebf \u0111\u1ea3 k\u00edch ho\u1ea1t)",
"Plugins installed ({0}):": "Plugin \u0111\u00e3 c\u00e0i \u0111\u1eb7t ({0}):",
"Premium plugins:": "C\u00e1c Plugin tr\u1ea3 ph\u00ed:",
"Learn more...": "T\u00ecm hi\u1ec3u th\u00eam...",
"You are using {0}": "B\u1ea1n \u0111ang s\u1eed d\u1ee5ng {0}",
"Plugins": "Plugins",
"Handy Shortcuts": "Ph\u00edm t\u1eaft ti\u1ec7n d\u1ee5ng",
"Horizontal line": "G\u1ea1ch ngang",
"Insert\/edit image": "Th\u00eam \/ s\u1eeda h\u00ecnh \u1ea3nh",
"Image description": "Mi\u00eau t\u1ea3 h\u00ecnh \u1ea3nh",
"Source": "Ngu\u1ed3n",
"Dimensions": "K\u00edch th\u01b0\u1edbc",
"Constrain proportions": "H\u1ea1n ch\u1ebf t\u1ef7 l\u1ec7",
"General": "T\u1ed5ng h\u1ee3p",
"Advanced": "N\u00e2ng cao",
"Style": "Ki\u1ec3u",
"Vertical space": "Kho\u1ea3ng c\u00e1ch d\u1ecdc",
"Horizontal space": "Kho\u1ea3ng c\u00e1ch ngang",
"Border": "\u0110\u01b0\u1eddng vi\u1ec1n",
"Insert image": "Ch\u00e8n \u1ea3nh",
"Image": "H\u00ecnh \u1ea3nh",
"Image list": "Danh s\u00e1ch \u1ea3nh",
"Rotate counterclockwise": "Xoay ng\u01b0\u1ee3c chi\u1ec1u kim \u0111\u1ed3ng",
"Rotate clockwise": "Xoay theo chi\u1ec1u kim \u0111\u1ed3ng h\u1ed3",
"Flip vertically": "L\u1eadt d\u1ecdc",
"Flip horizontally": "L\u1eadt ngang",
"Edit image": "S\u1eeda \u1ea3nh",
"Image options": "T\u00f9y ch\u1ecdn h\u00ecnh \u1ea3nh",
"Zoom in": "Ph\u00f3ng to",
"Zoom out": "Thu nh\u1ecf",
"Crop": "X\u00e9n",
"Resize": "Thay \u0111\u1ed5i k\u00edch th\u01b0\u1edbc",
"Orientation": "\u0110\u1ecbnh h\u01b0\u1edbng",
"Brightness": "\u0110\u1ed9 s\u00e1ng",
"Sharpen": "\u0110\u1ed9 s\u1eafc n\u00e9t",
"Contrast": "\u0110\u1ed9 t\u01b0\u01a1ng ph\u1ea3n",
"Color levels": "M\u1ee9c \u0111\u1ed9 m\u00e0u s\u1eafc",
"Gamma": "M\u00e0u Gamma",
"Invert": "\u0110\u1ea3o ng\u01b0\u1ee3c",
"Apply": "\u00c1p d\u1ee5ng",
"Back": "Tr\u1edf l\u1ea1i",
"Insert date\/time": "Th\u00eam ng\u00e0y \/ gi\u1edd",
"Date\/time": "Ng\u00e0y\/gi\u1edd",
"Insert link": "Th\u00eam li\u00ean k\u1ebft",
"Insert\/edit link": "Th\u00eam \/ s\u1eeda li\u00ean k\u1ebft",
"Text to display": "Ch\u1eef hi\u1ec3n th\u1ecb",
"Url": "Li\u00ean k\u1ebft",
"Target": "M\u1ee5c ti\u00eau",
"None": "Kh\u00f4ng",
"New window": "C\u1eeda s\u1ed5 m\u1edbi",
"Remove link": "Xo\u00e1 li\u00ean k\u1ebft",
"Anchors": "Ghim",
"Link": "Li\u00ean k\u1ebft",
"Paste or type a link": "D\u00e1n ho\u1eb7c nh\u1eadp li\u00ean k\u1ebft",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "URL b\u1ea1n nh\u1eadp v\u00e0o c\u00f3 v\u1ebb l\u00e0 m\u1ed9t \u0111\u1ecba ch\u1ec9 email. B\u1ea1n c\u00f3 mu\u1ed1n th\u00eam c\u00e1c y\u00eau c\u1ea7u mailto: ti\u1ec1n t\u1ed1?",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "URL b\u1ea1n nh\u1eadp v\u00e0o c\u00f3 v\u1ebb l\u00e0 m\u1ed9t li\u00ean k\u1ebft b\u00ean ngo\u00e0i. B\u1ea1n c\u00f3 mu\u1ed1n th\u00eam ti\u1ec1n t\u1ed1 http:\/\/ c\u1ea7n thi\u1ebft?",
"Link list": "Danh s\u00e1ch li\u00ean k\u1ebft",
"Insert video": "Th\u00eam video",
"Insert\/edit video": "Th\u00eam \/ s\u1eeda video",
"Insert\/edit media": "Ch\u00e8n\/ch\u1ec9nh s\u1eeda ph\u01b0\u01a1ng ti\u1ec7n truy\u1ec1n th\u00f4ng",
"Alternative source": "Ngu\u1ed3n thay th\u1ebf",
"Poster": "Ng\u01b0\u1eddi \u0111\u0103ng",
"Paste your embed code below:": "D\u00e1n m\u00e3 embed v\u00e0o:",
"Embed": "Embed",
"Media": "Ph\u01b0\u01a1ng ti\u1ec7n truy\u1ec1n th\u00f4ng",
"Nonbreaking space": "Kh\u00f4ng ng\u1eaft kho\u1ea3ng",
"Page break": "Ng\u1eaft trang",
"Paste as text": "D\u00e1n nh\u01b0 v\u0103n b\u1ea3n",
"Preview": "Xem tr\u01b0\u1edbc",
"Print": "In",
"Save": "L\u01b0u",
"Find": "T\u00ecm",
"Replace with": "Thay th\u1ebf b\u1eb1ng",
"Replace": "Thay th\u1ebf",
"Replace all": "Thay th\u1ebf t\u1ea5t c\u1ea3",
"Prev": "Tr\u01b0\u1edbc",
"Next": "Sau",
"Find and replace": "T\u00ecm v\u00e0 thay th\u1ebf",
"Could not find the specified string.": "Kh\u00f4ng t\u00ecm th\u1ea5y chu\u1ed7i y\u00eau c\u1ea7u",
"Match case": "Ph\u00e2n bi\u1ec7t hoa th\u01b0\u1eddng",
"Whole words": "T\u1ea5t c\u1ea3 \u0111o\u1ea1n",
"Spellcheck": "Ki\u1ec3m tra ch\u00ednh t\u1ea3",
"Ignore": "L\u1edd qua",
"Ignore all": "L\u1edd t\u1ea5t c\u1ea3",
"Finish": "Ho\u00e0n t\u1ea5t",
"Add to Dictionary": "Th\u00eam v\u00e0o t\u1eeb \u0111i\u1ec3n",
"Insert table": "Th\u00eam b\u1ea3ng",
"Table properties": "Thu\u1ed9c t\u00ednh b\u1ea3ng",
"Delete table": "Xo\u00e1 b\u1ea3ng",
"Cell": "\u00d4",
"Row": "D\u00f2ng",
"Column": "C\u1ed9t",
"Cell properties": "Thu\u1ed9c t\u00ednh \u00f4",
"Merge cells": "N\u1ed1i \u00f4",
"Split cell": "Chia \u00f4",
"Insert row before": "Th\u00eam d\u00f2ng ph\u00eda tr\u00ean",
"Insert row after": "Th\u00eam d\u00f2ng ph\u00eda d\u01b0\u1edbi",
"Delete row": "Xo\u00e1 d\u00f2ng",
"Row properties": "Thu\u1ed9c t\u00ednh d\u00f2ng",
"Cut row": "C\u1eaft d\u00f2ng",
"Copy row": "Ch\u00e9p d\u00f2ng",
"Paste row before": "D\u00e1n v\u00e0o ph\u00eda tr\u01b0\u1edbc, tr\u00ean",
"Paste row after": "D\u00e1n v\u00e0o ph\u00eda sau, d\u01b0\u1edbi",
"Insert column before": "Th\u00eam c\u1ed9t b\u00ean tr\u00e1i",
"Insert column after": "Th\u00eam c\u1ed9t b\u00ean ph\u1ea3i",
"Delete column": "Xo\u00e1 c\u1ed9t",
"Cols": "C\u1ed9t",
"Rows": "D\u00f2ng",
"Width": "R\u1ed9ng",
"Height": "Cao",
"Cell spacing": "Kho\u1ea3ng c\u00e1ch \u00f4",
"Cell padding": "Kho\u1ea3ng c\u00e1ch trong \u00f4",
"Caption": "Ti\u00eau \u0111\u1ec1",
"Left": "Tr\u00e1i",
"Center": "Gi\u1eefa",
"Right": "Ph\u1ea3i",
"Cell type": "Lo\u1ea1i \u00f4",
"Scope": "Quy\u1ec1n",
"Alignment": "Canh ch\u1ec9nh",
"H Align": "X\u1ebfp ngang",
"V Align": "X\u1ebfp d\u1ecdc",
"Top": "\u0110\u1ec9nh",
"Middle": "Gi\u1eefa",
"Bottom": "\u0110\u00e1y",
"Header cell": "Ti\u00eau \u0111\u1ec1 \u00f4",
"Row group": "Nh\u00f3m d\u00f2ng",
"Column group": "Nh\u00f3m c\u1ed9t",
"Row type": "Lo\u1ea1i d\u00f2ng",
"Header": "Ti\u00eau \u0111\u1ec1",
"Body": "N\u1ed9i dung",
"Footer": "Ch\u00e2n",
"Border color": "M\u00e0u vi\u1ec1n",
"Insert template": "Th\u00eam m\u1eabu",
"Templates": "M\u1eabu",
"Template": "B\u1ea3n m\u1eabu",
"Text color": "M\u00e0u ch\u1eef",
"Background color": "M\u00e0u n\u1ec1n",
"Custom...": "T\u00f9y ch\u1ecdn...",
"Custom color": "M\u00e0u t\u00f9y ch\u1ecdn",
"No color": "Kh\u00f4ng m\u00e0u",
"Table of Contents": "M\u1ee5c l\u1ee5c",
"Show blocks": "Hi\u1ec3n th\u1ecb kh\u1ed1i",
"Show invisible characters": "Hi\u1ec3n th\u1ecb c\u00e1c k\u00fd t\u1ef1 \u1ea9n",
"Words: {0}": "T\u1eeb: {0}",
"{0} words": "{0} t\u1eeb",
"File": "T\u1eadp tin",
"Edit": "S\u1eeda",
"Insert": "Th\u00eam",
"View": "Xem",
"Format": "\u0110\u1ecbnh d\u1ea1ng",
"Table": "B\u1ea3ng",
"Tools": "C\u00f4ng c\u1ee5",
"Powered by {0}": "\u0110\u01b0\u1ee3c cung c\u1ea5p b\u1edfi {0}",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "Khu v\u1ef1c so\u1ea1n th\u1ea3o. Nh\u1ea5n ALT-F9 \u0111\u1ec3 hi\u1ec7n menu, ALT-F10 \u0111\u1ec3 hi\u1ec7n thanh c\u00f4ng c\u1ee5. C\u1ea7n tr\u1ee3 gi\u00fap nh\u1ea5n ALT-0"
});