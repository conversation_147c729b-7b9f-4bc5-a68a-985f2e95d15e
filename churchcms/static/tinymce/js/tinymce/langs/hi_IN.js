tinymce.addI18n('hi_IN',{
"Cut": "\u0915\u093e\u091f\u0947\u0902",
"Heading 5": "\u0936\u0940\u0930\u094d\u0937\u0915 5",
"Header 2": "\u0936\u0940\u0930\u094d\u0937\u0915 2",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "\u0906\u092a\u0915\u093e \u091c\u093e\u0932 \u0935\u093f\u091a\u093e\u0930\u0915 \u0938\u0940\u0927\u0947 \u0938\u092e\u0930\u094d\u0925\u0928 \u0928\u0939\u0940\u0902 \u0915\u0930 \u0930\u0939\u093e \u0939\u0948\u0964 \u0915\u0943\u092a\u092f\u093e \u0915\u0941\u0902\u091c\u0940\u092a\u091f\u0932 \u0915\u0947 \u0926\u094d\u0935\u093e\u0930\u093e Ctrl+X\/C\/V \u0915\u093e \u0909\u092a\u092f\u094b\u0917 \u0915\u0930\u0947\u0902\u0964",
"Heading 4": "\u0936\u0940\u0930\u094d\u0937\u0915 4",
"Div": "\u0921\u093f\u0935",
"Heading 2": "\u0936\u0940\u0930\u094d\u0937\u0915 2",
"Paste": "\u091a\u093f\u092a\u0915\u093e\u090f\u0901",
"Close": "\u092c\u0902\u0926",
"Font Family": "\u092b\u093c\u0949\u0928\u094d\u091f \u092a\u0930\u093f\u0935\u093e\u0930",
"Pre": "\u092a\u0942\u0930\u094d\u0935",
"Align right": "\u0926\u093e\u090f\u0901 \u0938\u0902\u0930\u0947\u0916\u0923",
"New document": "\u0928\u092f\u093e \u0926\u0938\u094d\u0924\u093e\u0935\u0947\u091c\u093c",
"Blockquote": "\u0916\u0902\u0921-\u0909\u0926\u094d\u0927\u0930\u0923",
"Numbered list": "\u0915\u094d\u0930\u092e\u093e\u0902\u0915\u093f\u0924 \u0938\u0942\u091a\u0940",
"Heading 1": "\u0936\u0940\u0930\u094d\u0937\u0915 1",
"Headings": "\u0936\u0940\u0930\u094d\u0937\u0915",
"Increase indent": "\u0916\u0930\u094b\u091c \u092c\u095d\u093e\u090f\u0901",
"Formats": "\u092a\u094d\u0930\u093e\u0930\u0942\u092a",
"Headers": "\u0936\u0940\u0930\u094d\u0937\u0915",
"Select all": "\u0938\u092d\u0940 \u091a\u0941\u0928\u0947\u0902",
"Header 3": "\u0936\u0940\u0930\u094d\u0937\u0915 3",
"Blocks": "\u0916\u0902\u0921",
"Undo": "\u092a\u0940\u091b\u0947",
"Strikethrough": "\u092e\u0927\u094d\u092f \u0938\u0947 \u0915\u093e\u091f\u0947\u0902",
"Bullet list": "\u0917\u094b\u0932\u0940 \u0938\u0942\u091a\u0940",
"Header 1": "\u0936\u0940\u0930\u094d\u0937\u0915 1",
"Superscript": "\u0909\u092a\u0930\u093f\u0932\u093f\u0916\u093f\u0924",
"Clear formatting": "\u092a\u094d\u0930\u093e\u0930\u0942\u092a\u0923 \u0939\u091f\u093e\u090f\u0901",
"Font Sizes": "\u092b\u093c\u0949\u0928\u094d\u091f \u0906\u0915\u093e\u0930",
"Subscript": "\u0928\u093f\u091a\u0932\u0940\u0932\u093f\u0916\u093f\u0924",
"Header 6": "\u0936\u0940\u0930\u094d\u0937\u0915 6",
"Redo": "\u0906\u0917\u0947",
"Paragraph": "\u0905\u0928\u0941\u091a\u094d\u091b\u0947\u0926",
"Ok": "\u0920\u0940\u0915 \u0939\u0948",
"Bold": "\u092e\u094b\u091f\u093e",
"Code": "\u0938\u0902\u0915\u0947\u0924\u0932\u093f\u092a\u093f",
"Italic": "\u091f\u0947\u095c\u093e",
"Align center": "\u092e\u0927\u094d\u092f \u0938\u0902\u0930\u0947\u0916\u0923",
"Header 5": "\u0936\u0940\u0930\u094d\u0937\u0915 5",
"Heading 6": "\u0936\u0940\u0930\u094d\u0937\u0915 6",
"Heading 3": "\u0936\u0940\u0930\u094d\u0937\u0915 3",
"Decrease indent": "\u0916\u0930\u094b\u091c \u0915\u092e \u0915\u0930\u0947\u0902",
"Header 4": "\u0936\u0940\u0930\u094d\u0937\u0915 4",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "\u091a\u093f\u092a\u0915\u093e\u0928\u0947 \u0915\u093e \u092a\u094d\u0930\u0915\u093e\u0930 \u0905\u092d\u0940 \u0938\u093e\u0926\u093e \u092a\u093e\u0920\u094d\u092f \u0939\u0948\u0964 \u0938\u093e\u092e\u0917\u094d\u0930\u0940 \u091a\u093f\u092a\u0915\u093e\u0928\u0947 \u092a\u0930 \u0935\u0939 \u0938\u093e\u0926\u0947 \u092a\u093e\u0920\u094d\u092f \u092e\u0947\u0902 \u0930\u0939\u0917\u0940 \u091c\u092c \u0924\u0915 \u0906\u092a \u0907\u0938 \u0935\u093f\u0915\u0932\u094d\u092a \u0915\u094b \u092c\u0902\u0926 \u0928\u0939\u0940\u0902 \u0915\u0930 \u0926\u0947\u0924\u0947\u0964",
"Underline": "\u0905\u0927\u094b\u0930\u0947\u0916\u093e\u0902\u0915\u0928",
"Cancel": "\u0930\u0926\u094d\u0926",
"Justify": "\u0938\u092e\u0915\u0930\u0923",
"Inline": "\u0930\u0947\u0916\u093e \u092e\u0947\u0902",
"Copy": "\u092a\u094d\u0930\u0924\u093f \u0915\u0930\u0947\u0902",
"Align left": "\u092c\u093e\u090f\u0901 \u0938\u0902\u0930\u0947\u0916\u0923",
"Visual aids": "\u0926\u0943\u0936\u094d\u092f \u0938\u093e\u0927\u0928",
"Lower Greek": "\u0928\u093f\u092e\u094d\u0928 \u0917\u094d\u0930\u0940\u0915",
"Square": "\u0935\u0930\u094d\u0917",
"Default": "\u092a\u0939\u0932\u0947 \u0938\u0947 \u091a\u0941\u0928\u093e \u0939\u0941\u0906",
"Lower Alpha": "\u0928\u093f\u092e\u094d\u0928 \u0905\u0932\u094d\u092b\u093e",
"Circle": "\u0935\u0943\u0924\u094d\u0924",
"Disc": "\u092c\u093f\u0902\u092c",
"Upper Alpha": "\u0909\u091a\u094d\u091a \u0905\u0932\u094d\u092b\u093e",
"Upper Roman": "\u0909\u091a\u094d\u091a \u0930\u094b\u092e\u0928",
"Lower Roman": "\u0928\u093f\u092e\u094d\u0928 \u0930\u094b\u092e\u0928",
"Name": "\u0928\u093e\u092e",
"Anchor": "\u0932\u0902\u0917\u0930",
"You have unsaved changes are you sure you want to navigate away?": "\u0906\u092a\u0915\u0947 \u0915\u0941\u091b \u0905\u0938\u0939\u0947\u091c\u0947 \u092c\u0926\u0932\u093e\u0935 \u0939\u0948\u0902, \u0915\u094d\u092f\u093e \u0906\u092a \u0928\u093f\u0936\u094d\u091a\u093f\u0924 \u0930\u0942\u092a \u0938\u0947 \u092f\u0939\u093e\u0901 \u0938\u0947 \u091c\u093e\u0928\u093e \u091a\u093e\u0939\u0924\u0947 \u0939\u094b?",
"Restore last draft": "\u0906\u0916\u093f\u0930\u0940 \u092e\u0938\u094c\u0926\u093e \u092a\u0941\u0928\u0930\u094d\u0938\u094d\u0925\u093e\u092a\u093f\u0924 \u0915\u0930\u0947\u0902",
"Special character": "\u0935\u093f\u0936\u0947\u0937 \u0935\u0930\u094d\u0923",
"Source code": "\u0938\u094d\u0924\u094d\u0930\u094b\u0924 \u0938\u0902\u0915\u0947\u0924\u0932\u093f\u092a\u093f",
"B": "\u092c\u0940",
"R": "\u0906\u0930",
"G": "\u091c\u0940",
"Color": "\u0930\u0902\u0917",
"Right to left": "\u0926\u093e\u090f\u0901 \u0938\u0947 \u092c\u093e\u090f\u0901",
"Left to right": "\u092c\u093e\u090f\u0901 \u0938\u0947 \u0926\u093e\u090f\u0901",
"Emoticons": "\u092d\u093e\u0935\u0928\u093e-\u092a\u094d\u0930\u0924\u0940\u0915",
"Robots": "\u092f\u0902\u0924\u094d\u0930\u092e\u093e\u0928\u0935",
"Document properties": "\u0926\u0938\u094d\u0924\u093e\u0935\u0947\u091c\u093c \u0917\u0941\u0923",
"Title": "\u0936\u0940\u0930\u094d\u0937\u0915",
"Keywords": "\u0915\u0941\u0902\u091c\u0940\u0936\u092c\u094d\u0926",
"Encoding": "\u0938\u0902\u0915\u0947\u0924\u0940\u0915\u0930\u0923",
"Description": "\u0935\u093f\u0935\u0930\u0923",
"Author": "\u0932\u0947\u0916\u0915",
"Fullscreen": "\u092a\u0942\u0930\u094d\u0923 \u0938\u094d\u0915\u094d\u0930\u0940\u0928",
"Horizontal line": "\u0915\u094d\u0937\u0948\u0924\u093f\u091c \u0930\u0947\u0916\u093e",
"Horizontal space": "\u0915\u094d\u0937\u0948\u0924\u093f\u091c \u0938\u094d\u0925\u093e\u0928",
"Insert\/edit image": "\u091b\u0935\u093f \u0921\u093e\u0932\u0947\u0902\/\u0938\u092e\u094d\u092a\u093e\u0926\u0928 \u0915\u0930\u0947\u0902",
"General": "\u0938\u093e\u092e\u093e\u0928\u094d\u092f",
"Advanced": "\u0909\u0928\u094d\u0928\u0924",
"Source": "\u0938\u094d\u0924\u094d\u0930\u094b\u0924",
"Border": "\u0938\u0940\u092e\u093e",
"Constrain proportions": "\u0905\u0928\u0941\u092a\u093e\u0924 \u0935\u093f\u0935\u0936",
"Vertical space": "\u090a\u0930\u094d\u0927\u094d\u0935\u093e\u0927\u0930 \u0938\u094d\u0925\u093e\u0928",
"Image description": "\u091b\u0935\u093f \u0935\u093f\u0935\u0930\u0923",
"Style": "\u0936\u0948\u0932\u0940",
"Dimensions": "\u0906\u092f\u093e\u092e",
"Insert image": "\u091b\u0935\u093f \u0921\u093e\u0932\u0947\u0902",
"Zoom in": "\u0926\u0942\u0930\u0940 \u0915\u092e \u0915\u0930\u0947\u0902",
"Contrast": "\u0935\u093f\u0937\u092e\u0924\u093e",
"Back": "\u092a\u0940\u091b\u0947",
"Gamma": "\u0917\u093e\u092e\u093e",
"Flip horizontally": "\u0915\u094d\u0937\u0948\u0924\u093f\u091c \u0915\u0930\u0947\u0902",
"Resize": "\u0906\u0915\u093e\u0930 \u092c\u0926\u0932\u0947\u0902",
"Sharpen": "\u0928\u0941\u0915\u0940\u0932\u093e\u092a\u0928",
"Zoom out": "\u0926\u0942\u0930\u0940 \u092c\u095d\u093e\u090f\u0901",
"Image options": "\u091b\u0935\u093f \u0915\u0947 \u0935\u093f\u0915\u0932\u094d\u092a",
"Apply": "\u0932\u093e\u0917\u0942 \u0915\u0930\u0947\u0902",
"Brightness": "\u091a\u092e\u0915\u0940\u0932\u093e\u092a\u0928",
"Rotate clockwise": "\u0918\u095c\u0940 \u0915\u0940 \u0926\u093f\u0936\u093e \u092e\u0947\u0902 \u0918\u0941\u092e\u093e\u0913",
"Rotate counterclockwise": "\u0918\u095c\u0940 \u0915\u0947 \u0935\u093f\u092a\u0930\u0940\u0924 \u0918\u0941\u092e\u093e\u0913",
"Edit image": "\u091b\u0935\u093f \u0938\u092e\u094d\u092a\u093e\u0926\u0928",
"Color levels": "\u0930\u0902\u0917 \u0938\u094d\u0924\u0930",
"Crop": "\u0915\u093e\u091f\u0947\u0902",
"Orientation": "\u0928\u093f\u0930\u094d\u0926\u0947\u0936\u0915 \u0930\u0947\u0916\u093e",
"Flip vertically": "\u0916\u095c\u093e \u0915\u0930\u0947\u0902",
"Invert": "\u0909\u0932\u091f\u0947\u0902",
"Insert date\/time": "\u0926\u093f\u0928\u093e\u0902\u0915\/\u0938\u092e\u092f \u0921\u093e\u0932\u0947\u0902",
"Remove link": "\u0915\u095c\u0940 \u0939\u091f\u093e\u090f\u0901",
"Url": "\u091c\u093e\u0932\u0938\u094d\u0925\u0932 \u092a\u0924\u093e",
"Text to display": "\u0926\u093f\u0916\u093e\u0928\u0947 \u0939\u0947\u0924\u0941 \u092a\u093e\u0920\u094d\u092f",
"Anchors": "\u0932\u0902\u0917\u0930",
"Insert link": "\u0915\u095c\u0940 \u0921\u093e\u0932\u0947\u0902",
"New window": "\u0928\u0908 \u0916\u093f\u095c\u0915\u0940",
"None": "\u0915\u094b\u0908 \u0928\u0939\u0940\u0902",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "\u0906\u092a\u0928\u0947 \u091c\u094b \u0915\u095c\u0940 \u0921\u093e\u0932\u0940 \u0939\u0948 \u0935\u0939 \u092c\u093e\u0939\u0930\u0940 \u0915\u095c\u0940 \u0915\u0947 \u091c\u0948\u0938\u0947 \u0926\u093f\u0916 \u0930\u0939\u093e \u0939\u0948\u0964 \u0915\u094d\u092f\u093e \u0906\u092a http:\/\/ \u092a\u0939\u0932\u0947 \u091c\u094b\u095c\u0928\u093e \u091a\u093e\u0939\u0924\u0947 \u0939\u0948?",
"Target": "\u0932\u0915\u094d\u0937\u094d\u092f",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "\u0906\u092a\u0928\u0947 \u091c\u094b \u0915\u095c\u0940 \u0921\u093e\u0932\u0940 \u0939\u0948 \u0935\u0939 \u0908\u092e\u0947\u0932 \u092a\u0924\u093e \u0915\u0947 \u091c\u0948\u0938\u0947 \u0926\u093f\u0916 \u0930\u0939\u093e \u0939\u0948\u0964 \u0915\u094d\u092f\u093e \u0906\u092a mailto: \u092a\u0939\u0932\u0947 \u091c\u094b\u095c\u0928\u093e \u091a\u093e\u0939\u0924\u0947 \u0939\u0948?",
"Insert\/edit link": "\u0915\u095c\u0940 \u0921\u093e\u0932\u0947\u0902\/\u0938\u0902\u092a\u093e\u0926\u093f\u0924 \u0915\u0930\u0947\u0902",
"Insert\/edit video": "\u091a\u0932\u091a\u093f\u0924\u094d\u0930 \u0921\u093e\u0932\u0947\u0902\/\u0938\u092e\u094d\u092a\u093e\u0926\u0928 \u0915\u0930\u0947\u0902",
"Poster": "\u092a\u094b\u0938\u094d\u091f\u0930",
"Alternative source": "\u0935\u0948\u0915\u0932\u094d\u092a\u093f\u0915 \u0938\u094d\u0930\u094b\u0924",
"Paste your embed code below:": "\u0926\u093f\u0916\u093e\u0928\u0947 \u0935\u093e\u0932\u0947 \u0938\u0902\u0915\u0947\u0924 \u0915\u094b \u0928\u0940\u091a\u0947 \u0921\u093e\u0932\u0947\u0902:",
"Insert video": "\u091a\u0932\u091a\u093f\u0924\u094d\u0930 \u0921\u093e\u0932\u0947\u0902",
"Embed": "\u0926\u093f\u0916\u093e\u0928\u093e",
"Nonbreaking space": "\u0905\u0935\u093f\u0930\u093e\u092e\u093f\u0924 \u091c\u0917\u0939",
"Page break": "\u092a\u0943\u0937\u094d\u0920 \u0935\u093f\u0930\u093e\u092e",
"Paste as text": "\u092a\u093e\u0920\u094d\u092f \u0915\u0947 \u0930\u0942\u092a \u092e\u0947\u0902 \u091a\u093f\u092a\u0915\u093e\u090f\u0901",
"Preview": "\u092a\u0942\u0930\u094d\u0935\u093e\u0935\u0932\u094b\u0915\u0928",
"Print": "\u092e\u0941\u0926\u094d\u0930\u0923",
"Save": "\u0938\u0939\u0947\u091c\u0947\u0902",
"Could not find the specified string.": "\u0928\u093f\u0930\u094d\u0926\u093f\u0937\u094d\u091f \u092a\u0902\u0915\u094d\u0924\u093f \u0928\u0939\u0940\u0902 \u092e\u093f\u0932 \u0938\u0915\u093e\u0964",
"Replace": "\u092a\u094d\u0930\u0924\u093f\u0938\u094d\u0925\u093e\u092a\u0928",
"Next": "\u0905\u0917\u0932\u093e",
"Whole words": "\u0938\u0902\u092a\u0942\u0930\u094d\u0923 \u0936\u092c\u094d\u0926",
"Find and replace": "\u0922\u0942\u0901\u0922\u0947\u0902 \u0914\u0930 \u092c\u0926\u0932\u0947\u0902",
"Replace with": "\u092a\u094d\u0930\u0924\u093f\u0938\u094d\u0925\u093e\u092a\u093f\u0924 \u0915\u0930\u0947\u0902",
"Find": "\u0916\u094b\u091c",
"Replace all": "\u0938\u092d\u0940 \u092a\u094d\u0930\u0924\u093f\u0938\u094d\u0925\u093e\u092a\u093f\u0924 \u0915\u0930\u0947\u0902",
"Match case": "\u092e\u093e\u092e\u0932\u0947 \u092e\u093f\u0932\u093e\u090f\u0901",
"Prev": "\u092a\u093f\u091b\u0932\u093e",
"Spellcheck": "\u0935\u0930\u094d\u0924\u0928\u0940\u0936\u094b\u0927\u0915",
"Finish": "\u0938\u092e\u093e\u092a\u094d\u0924",
"Ignore all": "\u0938\u092d\u0940 \u0915\u0940 \u0909\u092a\u0947\u0915\u094d\u0937\u093e",
"Ignore": "\u0909\u092a\u0947\u0915\u094d\u0937\u093e",
"Add to Dictionary": "\u0936\u092c\u094d\u0926\u0915\u094b\u0936 \u092e\u0947\u0902 \u091c\u094b\u095c\u0947\u0902",
"Insert row before": "\u092a\u0939\u0932\u0947 \u092a\u0902\u0915\u094d\u0924\u093f \u0921\u093e\u0932\u0947\u0902",
"Rows": "\u092a\u0902\u0915\u094d\u0924\u093f\u092f\u093e\u0901",
"Height": "\u090a\u0901\u091a\u093e\u0908",
"Paste row after": "\u092a\u0902\u0915\u094d\u0924\u093f \u0915\u0947 \u092c\u093e\u0926 \u091a\u093f\u092a\u0915\u093e\u090f\u0901",
"Alignment": "\u0938\u0902\u0930\u0947\u0916\u0923",
"Border color": "\u0938\u0940\u092e\u093e \u0930\u0902\u0917",
"Column group": "\u0938\u094d\u0924\u0902\u092d \u0938\u092e\u0942\u0939",
"Row": "\u092a\u0902\u0915\u094d\u0924\u093f",
"Insert column before": "\u092a\u0939\u0932\u0947 \u0938\u094d\u0924\u0902\u092d \u0921\u093e\u0932\u0947\u0902",
"Split cell": "\u0916\u093e\u0928\u0947\u0902 \u0935\u093f\u092d\u093e\u091c\u093f\u0924 \u0915\u0930\u0947\u0902",
"Cell padding": "\u0916\u093e\u0928\u094b\u0902 \u092e\u0947\u0902 \u0926\u0942\u0930\u0940",
"Cell spacing": "\u0916\u093e\u0928\u094b\u0902 \u092e\u0947\u0902 \u0930\u093f\u0915\u094d\u0924\u093f",
"Row type": "\u092a\u0902\u0915\u094d\u0924\u093f \u092a\u094d\u0930\u0915\u093e\u0930",
"Insert table": "\u0924\u093e\u0932\u093f\u0915\u093e \u0921\u093e\u0932\u0947\u0902",
"Body": "\u0936\u0930\u0940\u0930",
"Caption": "\u0905\u0928\u0941\u0936\u0940\u0930\u094d\u0937\u0915",
"Footer": "\u092a\u093e\u0926 \u0932\u0947\u0916",
"Delete row": "\u092a\u0902\u0915\u094d\u0924\u093f \u0939\u091f\u093e\u090f\u0902",
"Paste row before": "\u092a\u0902\u0915\u094d\u0924\u093f \u0938\u0947 \u092a\u0939\u0932\u0947 \u091a\u093f\u092a\u0915\u093e\u090f\u0901",
"Scope": "\u0915\u094d\u0937\u0947\u0924\u094d\u0930",
"Delete table": "\u0924\u093e\u0932\u093f\u0915\u093e \u0939\u091f\u093e\u090f\u0901",
"H Align": "\u0915\u094d\u0937\u0947\u0924\u093f\u091c \u0938\u0902\u0930\u0947\u0916\u093f\u0924",
"Top": "\u0936\u0940\u0930\u094d\u0937",
"Header cell": "\u0936\u0940\u0930\u094d\u0937 \u0916\u093e\u0928\u093e",
"Column": "\u0938\u094d\u0924\u0902\u092d",
"Row group": "\u092a\u0902\u0915\u094d\u0924\u093f \u0938\u092e\u0942\u0939",
"Cell": "\u0915\u094b\u0936\u093f\u0915\u093e",
"Middle": "\u092e\u0927\u094d\u092f",
"Cell type": "\u0916\u093e\u0928\u0947 \u0915\u093e \u092a\u094d\u0930\u0915\u093e\u0930",
"Copy row": "\u092a\u0902\u0915\u094d\u0924\u093f \u0915\u0940 \u092a\u094d\u0930\u0924\u093f\u0932\u093f\u092a\u093f \u0932\u0947\u0902",
"Row properties": "\u092a\u0902\u0915\u094d\u0924\u093f \u0915\u0947 \u0917\u0941\u0923",
"Table properties": "\u0924\u093e\u0932\u093f\u0915\u093e \u0915\u0947 \u0917\u0941\u0923",
"Bottom": "\u0928\u0940\u091a\u0947",
"V Align": "\u090a\u0930\u094d\u0927\u094d\u0935\u093e\u0927\u0930 \u0938\u0902\u0930\u0947\u0916\u093f\u0924",
"Header": "\u0936\u0940\u0930\u094d\u0937\u0915",
"Right": "\u0926\u093e\u092f\u093e\u0901",
"Insert column after": "\u092c\u093e\u0926 \u0938\u094d\u0924\u0902\u092d \u0921\u093e\u0932\u0947\u0902",
"Cols": "\u0938\u094d\u0924\u0902\u092d",
"Insert row after": "\u092c\u093e\u0926 \u092a\u0902\u0915\u094d\u0924\u093f \u0921\u093e\u0932\u0947\u0902",
"Width": "\u091a\u094c\u0921\u093c\u093e\u0908",
"Cell properties": "\u0915\u094b\u0936\u093f\u0915\u093e \u0917\u0941\u0923",
"Left": "\u092c\u093e\u092f\u093e\u0901",
"Cut row": "\u092a\u0902\u0915\u094d\u0924\u093f \u0915\u093e\u091f\u0947\u0902",
"Delete column": "\u0938\u094d\u0924\u0902\u092d \u0939\u091f\u093e\u090f\u0901",
"Center": "\u092e\u0927\u094d\u092f",
"Merge cells": "\u0916\u093e\u0928\u094b\u0902 \u0915\u094b \u092e\u093f\u0932\u093e\u090f\u0902",
"Insert template": "\u0938\u093e\u0901\u091a\u093e \u0921\u093e\u0932\u0947\u0902",
"Templates": "\u0938\u093e\u0901\u091a\u0947",
"Background color": "\u092a\u0943\u0937\u094d\u0920\u092d\u0942\u092e\u093f \u0915\u093e \u0930\u0902\u0917",
"Custom...": "\u0905\u0928\u0941\u0915\u0942\u0932\u093f\u0924...",
"Custom color": "\u0905\u0928\u0941\u0915\u0942\u0932\u093f\u0924 \u0930\u0902\u0917",
"No color": "\u0930\u0902\u0917\u0939\u0940\u0928",
"Text color": "\u092a\u093e\u0920\u094d\u092f \u0930\u0902\u0917",
"Show blocks": "\u0921\u092c\u094d\u092c\u0947 \u0926\u093f\u0916\u093e\u090f\u0901",
"Show invisible characters": "\u0905\u0926\u0943\u0936\u094d\u092f \u0905\u0915\u094d\u0937\u0930\u094b\u0902 \u0915\u094b \u0926\u093f\u0916\u093e\u090f\u0901",
"Words: {0}": "\u0936\u092c\u094d\u0926: {0}",
"Insert": "\u0921\u093e\u0932\u0947\u0902",
"File": "\u0928\u0924\u094d\u0925\u0940",
"Edit": "\u0938\u092e\u094d\u092a\u093e\u0926\u0928",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "\u0938\u0902\u092a\u0928\u094d\u0928 \u092a\u093e\u0920 \u0915\u094d\u0937\u0947\u0924\u094d\u0930\u0964 \u092e\u0947\u0928\u0942 \u0915\u0947 \u0932\u093f\u090f ALT-F9 \u0926\u092c\u093e\u090f\u0901\u0964 \u0909\u092a\u0915\u0930\u0923 \u092a\u091f\u094d\u091f\u0940 \u0915\u0947 \u0932\u093f\u090f  ALT-F10 \u0926\u092c\u093e\u090f\u0901\u0964 \u0938\u0939\u093e\u092f\u0924\u093e \u0915\u0947 \u0932\u093f\u090f ALT-0 \u0926\u092c\u093e\u090f\u0901\u0964",
"Tools": "\u0909\u092a\u0915\u0930\u0923",
"View": "\u0926\u0947\u0916\u0947\u0902",
"Table": "\u0924\u093e\u0932\u093f\u0915\u093e",
"Format": "\u092a\u094d\u0930\u093e\u0930\u0942\u092a"
});