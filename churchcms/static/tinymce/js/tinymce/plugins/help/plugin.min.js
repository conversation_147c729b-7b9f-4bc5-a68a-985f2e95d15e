!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),t=function(e){return function(){return e}};function c(r){for(var o=[],e=1;e<arguments.length;e++)o[e-1]=arguments[e];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=o.concat(e);return r.apply(null,n)}}var n,r,o,a,i,l,u=t(!1),s=t(!0),m=u,f=s,d=function(){return p},p=(a={fold:function(e,t){return e()},is:m,isSome:m,isNone:f,getOr:o=function(e){return e},getOrThunk:r=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:function(){return null},getOrUndefined:function(){return undefined},or:o,orThunk:r,map:d,ap:d,each:function(){},bind:d,flatten:d,exists:m,forall:f,filter:d,equals:n=function(e){return e.isNone()},equals_:n,toArray:function(){return[]},toString:t("none()")},Object.freeze&&Object.freeze(a),a),y=function(n){var e=function(){return n},t=function(){return o},r=function(e){return e(n)},o={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:f,isNone:m,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return y(e(n))},ap:function(e){return e.fold(d,function(e){return y(e(n))})},each:function(e){e(n)},bind:r,flatten:e,exists:r,forall:r,filter:function(e){return e(n)?o:p},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(m,function(e){return t(n,e)})},toArray:function(){return[n]},toString:function(){return"some("+n+")"}};return o},h={some:y,none:d,from:function(e){return null===e||e===undefined?p:y(e)}},g=(i="function",function(e){return function(e){if(null===e)return"null";var t=typeof e;return"object"===t&&Array.prototype.isPrototypeOf(e)?"array":"object"===t&&String.prototype.isPrototypeOf(e)?"string":t}(e)===i}),k=(l=Array.prototype.indexOf)===undefined?function(e,t){return x(e,t)}:function(e,t){return l.call(e,t)},v=function(e,t){return-1<k(e,t)},b=function(e,t){for(var n=e.length,r=new Array(n),o=0;o<n;o++){var a=e[o];r[o]=t(a,o,e)}return r},x=function(e,t){for(var n=0,r=e.length;n<r;++n)if(e[n]===t)return n;return-1},C=(Array.prototype.slice,g(Array.from)&&Array.from,tinymce.util.Tools.resolve("tinymce.util.I18n")),w=tinymce.util.Tools.resolve("tinymce.Env"),A=w.mac?"\u2318":"Ctrl",S=w.mac?"Ctrl + Alt":"Shift + Alt",O={shortcuts:[{shortcut:A+" + B",action:"Bold"},{shortcut:A+" + I",action:"Italic"},{shortcut:A+" + U",action:"Underline"},{shortcut:A+" + A",action:"Select all"},{shortcut:A+" + Y or "+A+" + Shift + Z",action:"Redo"},{shortcut:A+" + Z",action:"Undo"},{shortcut:S+" + 1",action:"Header 1"},{shortcut:S+" + 2",action:"Header 2"},{shortcut:S+" + 3",action:"Header 3"},{shortcut:S+" + 4",action:"Header 4"},{shortcut:S+" + 5",action:"Header 5"},{shortcut:S+" + 6",action:"Header 6"},{shortcut:S+" + 7",action:"Paragraph"},{shortcut:S+" + 8",action:"Div"},{shortcut:S+" + 9",action:"Address"},{shortcut:"Alt + F9",action:"Focus to menubar"},{shortcut:"Alt + F10",action:"Focus to toolbar"},{shortcut:"Alt + F11",action:"Focus to element path"},{shortcut:"Ctrl + F9",action:"Focus to contextual toolbar"},{shortcut:A+" + K",action:"Insert link (if link plugin activated)"},{shortcut:A+" + S",action:"Save (if save plugin activated)"},{shortcut:A+" + F",action:"Find (if searchreplace plugin activated)"}]},T=function(){var e=b(O.shortcuts,function(e){return'<tr data-mce-tabstop="1" tabindex="-1" aria-label="Action: '+(t=e).action+", Shortcut: "+t.shortcut.replace(/Ctrl/g,"Control")+'"><td>'+C.translate(e.action)+"</td><td>"+e.shortcut+"</td></tr>";var t}).join("");return{title:"Handy Shortcuts",type:"container",style:"overflow-y: auto; overflow-x: hidden; max-height: 250px",items:[{type:"container",html:'<div><table class="mce-table-striped"><thead><th>'+C.translate("Action")+"</th><th>"+C.translate("Shortcut")+"</th></thead>"+e+"</table></div>"}]}},P=Object.keys,_=[{key:"advlist",name:"Advanced List"},{key:"anchor",name:"Anchor"},{key:"autolink",name:"Autolink"},{key:"autoresize",name:"Autoresize"},{key:"autosave",name:"Autosave"},{key:"bbcode",name:"BBCode"},{key:"charmap",name:"Character Map"},{key:"code",name:"Code"},{key:"codesample",name:"Code Sample"},{key:"colorpicker",name:"Color Picker"},{key:"compat3x",name:"3.x Compatibility"},{key:"contextmenu",name:"Context Menu"},{key:"directionality",name:"Directionality"},{key:"emoticons",name:"Emoticons"},{key:"fullpage",name:"Full Page"},{key:"fullscreen",name:"Full Screen"},{key:"help",name:"Help"},{key:"hr",name:"Horizontal Rule"},{key:"image",name:"Image"},{key:"imagetools",name:"Image Tools"},{key:"importcss",name:"Import CSS"},{key:"insertdatetime",name:"Insert Date/Time"},{key:"legacyoutput",name:"Legacy Output"},{key:"link",name:"Link"},{key:"lists",name:"Lists"},{key:"media",name:"Media"},{key:"nonbreaking",name:"Nonbreaking"},{key:"noneditable",name:"Noneditable"},{key:"pagebreak",name:"Page Break"},{key:"paste",name:"Paste"},{key:"preview",name:"Preview"},{key:"print",name:"Print"},{key:"save",name:"Save"},{key:"searchreplace",name:"Search and Replace"},{key:"spellchecker",name:"Spell Checker"},{key:"tabfocus",name:"Tab Focus"},{key:"table",name:"Table"},{key:"template",name:"Template"},{key:"textcolor",name:"Text Color"},{key:"textpattern",name:"Text Pattern"},{key:"toc",name:"Table of Contents"},{key:"visualblocks",name:"Visual Blocks"},{key:"visualchars",name:"Visual Characters"},{key:"wordcount",name:"Word Count"}],H=c(function(e,o){return e.replace(/\$\{([^{}]*)\}/g,function(e,t){var n,r=o[t];return"string"==(n=typeof r)||"number"===n?r.toString():e})},'<a href="${url}" target="_blank" rel="noopener">${name}</a>'),F=function(t,n){return function(e,t){for(var n=0,r=e.length;n<r;n++){var o=e[n];if(t(o,n,e))return h.some(o)}return h.none()}(_,function(e){return e.key===n}).fold(function(){var e=t.plugins[n].getMetadata;return"function"==typeof e?H(e()):n},function(e){return H({name:e.name,url:"https://www.tinymce.com/docs/plugins/"+e.key})})},M=function(t){var e,n,r,o=(r=P((e=t).plugins),e.settings.forced_plugins===undefined?r:function(e,t){for(var n=[],r=0,o=e.length;r<o;r++){var a=e[r];t(a,r,e)&&n.push(a)}return n}(r,(n=c(v,e.settings.forced_plugins),function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return!n.apply(null,e)}))),a=b(o,function(e){return"<li>"+F(t,e)+"</li>"}),i=a.length,l=a.join("");return"<p><b>"+C.translate(["Plugins installed ({0}):",i])+"</b></p><ul>"+l+"</ul>"},E=function(e){return{title:"Plugins",type:"container",style:"overflow-y: auto; overflow-x: hidden;",layout:"flex",padding:10,spacing:10,items:[(t=e,{type:"container",html:'<div style="overflow-y: auto; overflow-x: hidden; max-height: 230px; height: 230px;" data-mce-tabstop="1" tabindex="-1">'+M(t)+"</div>",flex:1}),{type:"container",html:'<div style="padding: 10px; background: #e3e7f4; height: 100%;" data-mce-tabstop="1" tabindex="-1"><p><b>'+C.translate("Premium plugins:")+'</b></p><ul><li>PowerPaste</li><li>Spell Checker Pro</li><li>Accessibility Checker</li><li>Advanced Code Editor</li><li>Enhanced Media Embed</li><li>Link Checker</li></ul><br /><p style="float: right;"><a href="https://www.tinymce.com/pricing/?utm_campaign=editor_referral&utm_medium=help_dialog&utm_source=tinymce" target="_blank">'+C.translate("Learn more...")+"</a></p></div>",flex:1}]};var t},I=tinymce.util.Tools.resolve("tinymce.EditorManager"),j=function(){var e,t,n='<a href="https://www.tinymce.com/docs/changelog/?utm_campaign=editor_referral&utm_medium=help_dialog&utm_source=tinymce" target="_blank">TinyMCE '+(e=I.majorVersion,t=I.minorVersion,0===e.indexOf("@")?"X.X.X":e+"."+t)+"</a>";return[{type:"label",html:C.translate(["You are using {0}",n])},{type:"spacer",flex:1},{text:"Close",onclick:function(){this.parent().parent().close()}}]},L=function(e,t){return function(){e.windowManager.open({title:"Help",bodyType:"tabpanel",layout:"flex",body:[T(),E(e)],buttons:j(),onPostRender:function(){this.getEl("title").innerHTML='<img src="'+t+'/img/logo.png" alt="TinyMCE Logo" style="display: inline-block; width: 200px; height: 50px">'}})}},B=function(e,t){e.addCommand("mceHelp",L(e,t))},N=function(e,t){e.addButton("help",{icon:"help",onclick:L(e,t)}),e.addMenuItem("help",{text:"Help",icon:"help",context:"help",onclick:L(e,t)})};e.add("help",function(e,t){N(e,t),B(e,t),e.shortcuts.add("Alt+0","Open help dialog","mceHelp")})}();