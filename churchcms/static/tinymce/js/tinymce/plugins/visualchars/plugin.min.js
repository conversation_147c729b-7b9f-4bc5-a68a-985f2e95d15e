!function(){"use strict";var n,e,t,r,o,u,i=function(n){var e=n,t=function(){return e};return{get:t,set:function(n){e=n},clone:function(){return i(t())}}},c=tinymce.util.Tools.resolve("tinymce.PluginManager"),a=function(n){return{isEnabled:function(){return n.get()}}},f=function(n,e){return n.fire("VisualChars",{state:e})},l={"\xa0":"nbsp","\xad":"shy"},d=function(n,e){var t,r="";for(t in n)r+=t;return new RegExp("["+r+"]",e?"g":"")},s=function(n){var e,t="";for(e in n)t&&(t+=","),t+="span.mce-"+n[e];return t},m={charMap:l,regExp:d(l),regExpGlobal:d(l,!0),selector:s(l),charMapToRegExp:d,charMapToSelector:s},N=function(n){return function(){return n}},E=N(!1),g=N(!0),h=E,v=g,T=function(){return p},p=(r={fold:function(n,e){return n()},is:h,isSome:h,isNone:v,getOr:t=function(n){return n},getOrThunk:e=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:function(){return null},getOrUndefined:function(){return undefined},or:t,orThunk:e,map:T,ap:T,each:function(){},bind:T,flatten:T,exists:h,forall:v,filter:T,equals:n=function(n){return n.isNone()},equals_:n,toArray:function(){return[]},toString:N("none()")},Object.freeze&&Object.freeze(r),r),O=function(t){var n=function(){return t},e=function(){return o},r=function(n){return n(t)},o={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:v,isNone:h,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:e,orThunk:e,map:function(n){return O(n(t))},ap:function(n){return n.fold(T,function(n){return O(n(t))})},each:function(n){n(t)},bind:r,flatten:n,exists:r,forall:r,filter:function(n){return n(t)?o:p},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(h,function(n){return e(t,n)})},toArray:function(){return[t]},toString:function(){return"some("+t+")"}};return o},y=function(n){return null===n||n===undefined?p:O(n)},D=(o="function",function(n){return function(n){if(null===n)return"null";var e=typeof n;return"object"===e&&Array.prototype.isPrototypeOf(n)?"array":"object"===e&&String.prototype.isPrototypeOf(n)?"string":e}(n)===o}),_=function(n,e){for(var t=0,r=n.length;t<r;t++)e(n[t],t,n)},C=(Array.prototype.slice,D(Array.from)&&Array.from,function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:N(n)}}),M={fromHtml:function(n,e){var t=(e||document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||1<t.childNodes.length)throw console.error("HTML does not have a single root node",n),"HTML must have a single root node";return C(t.childNodes[0])},fromTag:function(n,e){var t=(e||document).createElement(n);return C(t)},fromText:function(n,e){var t=(e||document).createTextNode(n);return C(t)},fromDom:C,fromPoint:function(n,e,t){var r=n.dom();return y(r.elementFromPoint(e,t)).map(C)}},b=(Node.ATTRIBUTE_NODE,Node.CDATA_SECTION_NODE,Node.COMMENT_NODE,Node.DOCUMENT_NODE,Node.DOCUMENT_TYPE_NODE,Node.DOCUMENT_FRAGMENT_NODE,Node.ELEMENT_NODE,Node.TEXT_NODE),k=(Node.PROCESSING_INSTRUCTION_NODE,Node.ENTITY_REFERENCE_NODE,Node.ENTITY_NODE,Node.NOTATION_NODE,function(n){return n.dom().nodeValue}),S=(u=b,function(n){return n.dom().nodeType===u}),x=function(n){return'<span data-mce-bogus="1" class="mce-'+m.charMap[n]+'">'+n+"</span>"},A=function(n,e){var t=[],r=function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var u=n[o];r[o]=e(u,o,n)}return r}(n.dom().childNodes,M.fromDom);return _(r,function(n){e(n)&&(t=t.concat([n])),t=t.concat(A(n,e))}),t},w={isMatch:function(n){return S(n)&&k(n)!==undefined&&m.regExp.test(k(n))},filterDescendants:A,findParentElm:function(n,e){for(;n.parentNode;){if(n.parentNode===e)return n;n=n.parentNode}},replaceWithSpans:function(n){return n.replace(m.regExpGlobal,x)}},P=function(t,n){var r,o,e=w.filterDescendants(M.fromDom(n),w.isMatch);_(e,function(n){var e=w.replaceWithSpans(k(n));for(o=t.dom.create("div",null,e);r=o.lastChild;)t.dom.insertAfter(r,n.dom());t.dom.remove(n.dom())})},R=function(e,n){var t=e.dom.select(m.selector,n);_(t,function(n){e.dom.remove(n,1)})},I=P,B=R,U=function(n){var e=n.getBody(),t=n.selection.getBookmark(),r=w.findParentElm(n.selection.getNode(),e);r=r!==undefined?r:e,R(n,r),P(n,r),n.selection.moveToBookmark(t)},V=function(n,e){var t,r=n.getBody(),o=n.selection;e.set(!e.get()),f(n,e.get()),t=o.getBookmark(),!0===e.get()?I(n,r):B(n,r),o.moveToBookmark(t)},j=function(n,e){n.addCommand("mceVisualChars",function(){V(n,e)})},q=tinymce.util.Tools.resolve("tinymce.util.Delay"),G=function(e,t){var r=q.debounce(function(){U(e)},300);!1!==e.settings.forced_root_block&&e.on("keydown",function(n){!0===t.get()&&(13===n.keyCode?U(e):r())})},H=function(t){return function(n){var e=n.control;t.on("VisualChars",function(n){e.active(n.state)})}};c.add("visualchars",function(n){var e,t=i(!1);return j(n,t),(e=n).addButton("visualchars",{active:!1,title:"Show invisible characters",cmd:"mceVisualChars",onPostRender:H(e)}),e.addMenuItem("visualchars",{text:"Show invisible characters",cmd:"mceVisualChars",onPostRender:H(e),selectable:!0,context:"view",prependToContext:!0}),G(n,t),a(t)})}();