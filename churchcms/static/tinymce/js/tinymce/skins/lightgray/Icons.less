/* Icons */

@font-face {
	font-family: 'tinymce';
	src:url('fonts/tinymce.eot');
	src:url('fonts/tinymce.eot?#iefix') format('embedded-opentype'),
		url('fonts/tinymce.woff') format('woff'),
		url('fonts/tinymce.ttf') format('truetype'),
		url('fonts/tinymce.svg#tinymce') format('svg');
	font-weight: normal;
	font-style: normal;
}

@font-face {
	font-family: 'tinymce-small';
	src:url('fonts/tinymce-small.eot');
	src:url('fonts/tinymce-small.eot?#iefix') format('embedded-opentype'),
		url('fonts/tinymce-small.woff') format('woff'),
		url('fonts/tinymce-small.ttf') format('truetype'),
		url('fonts/tinymce-small.svg#tinymce') format('svg');
	font-weight: normal;
	font-style: normal;
}

@iconSize: 16px;

.@{prefix}-ico {
	font-family: 'tinymce', Arial;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	font-size: @iconSize;
	line-height: 16px;
	speak: none;
	vertical-align: text-top;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;

	display: inline-block;
	background: transparent center center;
	background-size: cover;
	width: 16px;
	height: 16px;
	color: @btn-text;
}

.@{prefix}-btn-small .@{prefix}-ico {
	font-family: 'tinymce-small', Arial;
}

.@{prefix}-i-save:before                 { content: "\e000"; }
.@{prefix}-i-newdocument:before          { content: "\e001"; }
.@{prefix}-i-fullpage:before             { content: "\e002"; }
.@{prefix}-i-alignleft:before            { content: "\e003"; }
.@{prefix}-i-aligncenter:before          { content: "\e004"; }
.@{prefix}-i-alignright:before           { content: "\e005"; }
.@{prefix}-i-alignjustify:before         { content: "\e006"; }
.@{prefix}-i-alignnone:before            { content: "\e003"; }
.@{prefix}-i-cut:before                  { content: "\e007"; }
.@{prefix}-i-paste:before                { content: "\e008"; }
.@{prefix}-i-searchreplace:before        { content: "\e009"; }
.@{prefix}-i-bullist:before              { content: "\e00a"; }
.@{prefix}-i-numlist:before              { content: "\e00b"; }
.@{prefix}-i-indent:before               { content: "\e00c"; }
.@{prefix}-i-outdent:before              { content: "\e00d"; }
.@{prefix}-i-blockquote:before           { content: "\e00e"; }
.@{prefix}-i-undo:before                 { content: "\e00f"; }
.@{prefix}-i-redo:before                 { content: "\e010"; }
.@{prefix}-i-link:before                 { content: "\e011"; }
.@{prefix}-i-unlink:before               { content: "\e012"; }
.@{prefix}-i-anchor:before               { content: "\e013"; }
.@{prefix}-i-image:before                { content: "\e014"; }
.@{prefix}-i-media:before                { content: "\e015"; }
.@{prefix}-i-help:before                 { content: "\e016"; }
.@{prefix}-i-code:before                 { content: "\e017"; }
.@{prefix}-i-insertdatetime:before       { content: "\e018"; }
.@{prefix}-i-preview:before              { content: "\e019"; }
.@{prefix}-i-forecolor:before            { content: "\e01a"; }
.@{prefix}-i-backcolor:before            { content: "\e01a"; }
.@{prefix}-i-table:before                { content: "\e01b"; }
.@{prefix}-i-hr:before                   { content: "\e01c"; }
.@{prefix}-i-removeformat:before         { content: "\e01d"; }
.@{prefix}-i-subscript:before            { content: "\e01e"; }
.@{prefix}-i-superscript:before          { content: "\e01f"; }
.@{prefix}-i-charmap:before              { content: "\e020"; }
.@{prefix}-i-emoticons:before            { content: "\e021"; }
.@{prefix}-i-print:before                { content: "\e022"; }
.@{prefix}-i-fullscreen:before           { content: "\e023"; }
.@{prefix}-i-spellchecker:before         { content: "\e024"; }
.@{prefix}-i-nonbreaking:before          { content: "\e025"; }
.@{prefix}-i-template:before             { content: "\e026"; }
.@{prefix}-i-pagebreak:before            { content: "\e027"; }
.@{prefix}-i-restoredraft:before         { content: "\e028"; }
.@{prefix}-i-bold:before                 { content: "\e02a"; }
.@{prefix}-i-italic:before               { content: "\e02b"; }
.@{prefix}-i-underline:before            { content: "\e02c"; }
.@{prefix}-i-strikethrough:before        { content: "\e02d"; }
.@{prefix}-i-visualchars:before          { content: "\e02e"; }
.@{prefix}-i-visualblocks:before         { content: "\e02e"; }
.@{prefix}-i-ltr:before                  { content: "\e02f"; }
.@{prefix}-i-rtl:before                  { content: "\e030"; }
.@{prefix}-i-copy:before                 { content: "\e031"; }
.@{prefix}-i-resize:before               { content: "\e032"; }
.@{prefix}-i-browse:before               { content: "\e034"; }
.@{prefix}-i-pastetext:before            { content: "\e035"; }
.@{prefix}-i-rotateleft:before           { content: "\eaa8"; }
.@{prefix}-i-rotateright:before          { content: "\eaa9"; }
.@{prefix}-i-crop:before                 { content: "\ee78"; }
.@{prefix}-i-editimage:before            { content: "\e915"; }
.@{prefix}-i-options:before              { content: "\ec6a"; }
.@{prefix}-i-flipv:before                { content: "\eaaa"; }
.@{prefix}-i-fliph:before                { content: "\eaac"; }
.@{prefix}-i-zoomin:before               { content: "\eb35"; }
.@{prefix}-i-zoomout:before              { content: "\eb36"; }
.@{prefix}-i-sun:before                  { content: "\eccc"; }
.@{prefix}-i-moon:before                 { content: "\eccd"; }
.@{prefix}-i-arrowleft:before            { content: "\edc0"; }
.@{prefix}-i-arrowright:before           { content: "\e93c"; }
.@{prefix}-i-drop:before                 { content: "\e935"; }
.@{prefix}-i-contrast:before             { content: "\ecd4"; }
.@{prefix}-i-sharpen:before              { content: "\eba7"; }
.@{prefix}-i-resize2:before              { content: "\edf9"; }
.@{prefix}-i-orientation:before          { content: "\e601"; }
.@{prefix}-i-invert:before               { content: "\e602"; }
.@{prefix}-i-gamma:before                { content: "\e600"; }
.@{prefix}-i-remove:before               { content: "\ed6a"; }
.@{prefix}-i-tablerowprops:before        { content: "\e604"; }
.@{prefix}-i-tablecellprops:before       { content: "\e605"; }
.@{prefix}-i-table2:before               { content: "\e606"; }
.@{prefix}-i-tablemergecells:before      { content: "\e607"; }
.@{prefix}-i-tableinsertcolbefore:before { content: "\e608"; }
.@{prefix}-i-tableinsertcolafter:before  { content: "\e609"; }
.@{prefix}-i-tableinsertrowbefore:before { content: "\e60a"; }
.@{prefix}-i-tableinsertrowafter:before  { content: "\e60b"; }
.@{prefix}-i-tablesplitcells:before      { content: "\e60d"; }
.@{prefix}-i-tabledelete:before          { content: "\e60e"; }
.@{prefix}-i-tableleftheader:before      { content: "\e62a"; }
.@{prefix}-i-tabletopheader:before       { content: "\e62b"; }
.@{prefix}-i-tabledeleterow:before       { content: "\e800"; }
.@{prefix}-i-tabledeletecol:before       { content: "\e801"; }
.@{prefix}-i-codesample:before           { content: "\e603"; }
.@{prefix}-i-fill:before                 { content: "\e902"; }
.@{prefix}-i-borderwidth:before          { content: "\e903"; }
.@{prefix}-i-line:before                 { content: "\e904"; }
.@{prefix}-i-count:before                { content: "\e905"; }
.@{prefix}-i-translate:before            { content: "\e907"; }
.@{prefix}-i-drag:before                 { content: "\e908"; }
.@{prefix}-i-home:before                 { content: "\e90b"; }
.@{prefix}-i-upload:before               { content: "\e914"; }
.@{prefix}-i-bubble:before               { content: "\e91c"; }
.@{prefix}-i-user:before                 { content: "\e91d"; }
.@{prefix}-i-lock:before                 { content: "\e926"; }
.@{prefix}-i-unlock:before               { content: "\e927"; }
.@{prefix}-i-settings:before             { content: "\e928"; }
.@{prefix}-i-remove2:before              { content: "\e92a"; }
.@{prefix}-i-menu:before                 { content: "\e92d"; }
.@{prefix}-i-warning:before              { content: "\e930"; }
.@{prefix}-i-question:before             { content: "\e931"; }
.@{prefix}-i-pluscircle:before           { content: "\e932"; }
.@{prefix}-i-info:before                 { content: "\e933"; }
.@{prefix}-i-notice:before               { content: "\e934"; }
.@{prefix}-i-arrowup:before              { content: "\e93b"; }
.@{prefix}-i-arrowdown:before            { content: "\e93d"; }
.@{prefix}-i-arrowup2:before             { content: "\e93f"; }
.@{prefix}-i-arrowdown2:before           { content: "\e940"; }
.@{prefix}-i-menu2:before                { content: "\e941"; }
.@{prefix}-i-newtab:before               { content: "\e961"; }
.@{prefix}-i-a11y:before                 { content: "\e900"; }
.@{prefix}-i-plus:before                 { content: "\e93a"; }
.@{prefix}-i-insert:before               { content: "\e93a"; }
.@{prefix}-i-minus:before                { content: "\e939"; }
.@{prefix}-i-books:before                { content: "\e911"; }
.@{prefix}-i-reload:before               { content: "\e906"; }
.@{prefix}-i-toc:before                  { content: "\e901"; }
.@{prefix}-i-checkmark:before            { content: "\e033"; }
.@{prefix}-i-checkbox:before, .@{prefix}-i-selected:before {
	content: "\e033";
}

.@{prefix}-i-insert                { font-size: 14px; }
.@{prefix}-i-selected              { visibility: hidden; }
i.@{prefix}-i-backcolor            { text-shadow: none; background: @colorbtn-backcolor-bg; }
