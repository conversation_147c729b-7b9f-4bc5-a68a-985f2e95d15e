/* Content.less */

@font-family: Verdana, Arial, Helvetica, sans-serif;
@font-size: 14px;

body {
	background-color: #FFFFFF;
	color: #000000;
	font-family: @font-family;
	font-size: @font-size;
	scrollbar-3dlight-color: #F0F0EE;
	scrollbar-arrow-color: #676662;
	scrollbar-base-color: #F0F0EE;
	scrollbar-darkshadow-color: #DDDDDD;
	scrollbar-face-color: #E0E0DD;
	scrollbar-highlight-color: #F0F0EE;
	scrollbar-shadow-color: #F0F0EE;
	scrollbar-track-color: #F5F5F5;
}

td, th {
	font-family: @font-family;
	font-size: @font-size;
}

@import "Mixins.less";
@import "Content.Objects.less";
