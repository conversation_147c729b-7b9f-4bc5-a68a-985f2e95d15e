// ComboBox

.@{prefix}-combobox {
	position: relative;
	.inline-block();
	.border-radius(3px);
	.box-shadow(@textbox-box-shadow);
	*height: 32px;
}

.@{prefix}-combobox input {
	border: 1px solid @textbox-border;
	border-right-color: @combobox-border;
	height: 28px;
}

.@{prefix}-combobox.@{prefix}-disabled input {
	color: mix(@text, @textbox-bg, 40%);
}

.@{prefix}-combobox.@{prefix}-has-open input {
	.border-radius(4px 0 0 4px);
}

.@{prefix}-combobox .@{prefix}-btn {
	border: 1px solid @textbox-border;
	border-left: 0;
	.border-radius(0 4px 4px 0);
	margin: 0;
}

.@{prefix}-combobox button {
	padding-right: 8px;
	padding-left: 8px;
}

.@{prefix}-combobox.@{prefix}-disabled .@{prefix}-btn button {
	cursor: default;
	.box-shadow(none);
	.opacity(@btn-box-disabled-opacity);
}

.@{prefix}-combobox .@{prefix}-status {
	position: absolute;
	right: 2px;
	top: 50%;
	line-height: 16px;
  margin-top: -8px;
	font-size: 12px;
	width: 15px;
	height: 15px;
	text-align: center;
	cursor: pointer;
}

.@{prefix}-combobox.@{prefix}-has-status input {
	padding-right: 20px;
}

.@{prefix}-combobox.@{prefix}-has-open .@{prefix}-status {
	right: 37px;
}

.@{prefix}-combobox .@{prefix}-status.@{prefix}-i-warning {
	color: @combobox-warning-text;
}

.@{prefix}-combobox .@{prefix}-status.@{prefix}-i-checkmark {
	color: @combobox-success-text;
}

.@{prefix}-menu.@{prefix}-combobox-menu {
	border-top: 0;
	margin-top: 0;
	max-height: 200px;

	.@{prefix}-menu-item {
		padding: 4px 6px 4px 4px;
		font-size: 11px;
	}

	.@{prefix}-menu-item-sep {
		padding: 0;
	}

	.@{prefix}-text {
		font-size: 11px;
	}

	.@{prefix}-menu-item-link, .@{prefix}-menu-item-link b {
		font-size: 11px;
	}

	.@{prefix}-text b {
		font-size: 11px;
	}
}
