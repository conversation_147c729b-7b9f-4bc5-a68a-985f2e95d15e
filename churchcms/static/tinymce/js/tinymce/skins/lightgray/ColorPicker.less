// ColorPicker

.@{prefix}-colorpicker {
	position: relative;
	width: 250px;
	height: 220px;
}

.@{prefix}-colorpicker-sv {
	position: absolute;
	top: 0; left: 0;
	width: 90%;
	height: 100%;
	border: 1px solid @colorpicker-border;
	cursor: crosshair;
	overflow: hidden;
}

.@{prefix}-colorpicker-h-chunk {
	width: 100%;
}

.@{prefix}-colorpicker-overlay1, .@{prefix}-colorpicker-overlay2 {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
}

.@{prefix}-colorpicker-overlay1 {
	filter: progid:DXImageTransform.Microsoft.gradient(GradientType=1,startColorstr='#ffffff', endColorstr='#00ffffff');
	-ms-filter: "progid:DXImageTransform.Microsoft.gradient(GradientType=1,startColorstr='#ffffff', endColorstr='#00ffffff')";
	background: linear-gradient(to right, rgba(255,255,255,1), rgba(255,255,255,0));
}

.@{prefix}-colorpicker-overlay2 {
	filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0,startColorstr='#00000000', endColorstr='#000000');
	-ms-filter: "progid:DXImageTransform.Microsoft.gradient(GradientType=0,startColorstr='#00000000', endColorstr='#000000')";
	background: linear-gradient(to bottom, rgba(0,0,0,0), rgba(0,0,0,1));
}

.@{prefix}-colorpicker-selector1 {
	background: none;
	position: absolute;
	width: 12px;
	height: 12px;
	margin: -8px 0 0 -8px;
	border: 1px solid black;
	border-radius: 50%;
}

.@{prefix}-colorpicker-selector2 {
	position: absolute;
	width: 10px;
	height: 10px;
	border: 1px solid white;
	border-radius: 50%;
}

.@{prefix}-colorpicker-h {
	position: absolute;
	top: 0; right: 0;
	width: 6.5%;
	height: 100%;
	border: 1px solid @colorpicker-border;
	cursor: crosshair;
}

.@{prefix}-colorpicker-h-marker {
	margin-top: -4px;
	position: absolute;
	top: 0;
	left: -1px;
	width: 100%;
	border: 1px solid @colorpicker-hue-border;
	background: @colorpicker-hue-bg;
	height: 4px;
	z-index: 100;
}
