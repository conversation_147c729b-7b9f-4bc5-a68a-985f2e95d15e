// TextBox

.@{prefix}-textbox {
	background: @textbox-bg;
	border: 1px solid @textbox-border;
	.border-radius(3px);
	.box-shadow(@textbox-box-shadow);
	display: inline-block;
	.transition(~"border linear .2s, box-shadow linear .2s");
	height: 28px;
	resize: none;
	padding: 0 4px 0 4px;
	white-space: pre-wrap;
	*white-space: pre;
	color: @text;
}

.@{prefix}-textbox:focus, .@{prefix}-textbox.@{prefix}-focus {
	border-color: @textbox-border-focus;
	.box-shadow(inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px fadeout(@textbox-border-focus, 15%));
}

.@{prefix}-placeholder .@{prefix}-textbox {
	color: @textbox-text-placeholder;
}

.@{prefix}-textbox.@{prefix}-multiline {
	padding: 4px;
	height: auto;
}

.@{prefix}-textbox.@{prefix}-disabled {
	color: mix(@text, @textbox-bg, 40%);
}

// RTL

.@{prefix}-rtl .@{prefix}-textbox {
	text-align: right;
	direction: rtl;
}
