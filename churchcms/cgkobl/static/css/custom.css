/* Modern Church CMS - Custom Styles 2025 */

/* ===== MODERN COLOR PALETTE ===== */
:root {
    /* Primary Colors - Warmer, modern blue */
    --primary-color: #2563eb;
    --primary-light: #3b82f6;
    --primary-dark: #1d4ed8;

    /* Secondary Colors - Warm accent */
    --secondary-color: #f59e0b;
    --secondary-light: #fbbf24;
    --secondary-dark: #d97706;

    /* Neutral Colors - Modern grays */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-light: #94a3b8;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-accent: #f1f5f9;

    /* Modern Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-secondary: 'Playfair Display', Georgia, serif;
}

/* ===== MODERN TYPOGRAPHY ===== */
body {
    font-family: var(--font-primary);
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-secondary);
    font-weight: 600;
    line-height: 1.3;
    color: var(--text-primary);
}

/* ===== MODERN HEADER STYLES ===== */
#header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--gray-200);
    transition: all 0.3s ease;
}

#header.header-transparent {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sticky-header-active #header {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    box-shadow: var(--shadow-md);
}

#header .header-nav-features {
    padding-left: 0;
}

html:not(.sticky-header-active) .header-logo-sticky,
html:is(.sticky-header-active) .header-logo-non-sticky {
    display: none;
}

.sticky-header-active .header-logo-sticky {
    display: block;
}

#header .header-nav-features .header-nav-features-user .header-nav-features-toggle {
    font-size: 16px;
    color: #FFF;
}

/* ===== MODERN NAVIGATION ===== */
.header-nav-main nav > ul > li > a {
    font-family: var(--font-primary);
    font-weight: 500;
    color: var(--text-primary);
    transition: all 0.3s ease;
    position: relative;
}

.header-nav-main nav > ul > li > a:hover {
    color: var(--primary-color);
}

.header-nav-main nav > ul > li > a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.header-nav-main nav > ul > li > a:hover::after,
.header-nav-main nav > ul > li.current-menu-item > a::after {
    width: 100%;
}

/* ===== MODERN BUTTONS ===== */
.btn {
    font-family: var(--font-primary);
    font-weight: 500;
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
    border: none;
    text-transform: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: white;
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--text-primary);
    border: 1px solid var(--gray-200);
}

.btn-secondary:hover {
    background: var(--gray-200);
    color: var(--text-primary);
    transform: translateY(-1px);
}

/* ===== MODERN HERO SECTION ===== */
#home {
    height: 80vh;
    min-height: 600px;
    position: relative;
    overflow: hidden;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.8), rgba(59, 130, 246, 0.6));
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-family: var(--font-secondary);
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: 1.5rem;
}

.hero-subtitle {
    font-size: clamp(1.1rem, 2vw, 1.25rem);
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-cta {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 1rem 2rem;
    font-weight: 600;
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
}

.hero-cta:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    color: white;
}

/* ===== MODERN CARDS & SECTIONS ===== */
.modern-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    transition: all 0.3s ease;
    border: 1px solid var(--gray-100);
}

.modern-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.section {
    padding: 5rem 0;
}

.section-secondary {
    background: var(--bg-secondary);
}

.section-accent {
    background: var(--bg-accent);
}

/* ===== MODERN SERVICE CARDS ===== */
.custom-post-event {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    border: 1px solid var(--gray-100);
}

.custom-post-event:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.post-event-date {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-md);
    padding: 1rem;
    text-align: center;
    color: white;
    min-width: 80px;
}

.post-event-date .month {
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.post-event-date .day {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
}

.post-event-date .year {
    font-size: 0.875rem;
    opacity: 0.9;
}

.custom-event-infos ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.custom-event-infos li {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.custom-event-infos i {
    margin-right: 0.5rem;
    color: var(--primary-color);
    width: 16px;
}

/* ===== MODERN NEWS CARDS ===== */
.custom-post-blog {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    height: 100%;
}

.custom-post-blog:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.custom-thumb-info-2 .thumb-info-wrapper img {
    transition: transform 0.3s ease;
}

.custom-post-blog:hover .thumb-info-wrapper img {
    transform: scale(1.05);
}

.thumb-info-caption {
    padding: 1.5rem;
}

.custom-thumb-info-post-infos {
    border-top: 1px solid var(--gray-100);
    padding-top: 1rem;
    margin-top: 1rem;
}

/* ===== MODERN TESTIMONIAL SECTION ===== */
.testimonial-style-2 {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: 3rem;
    box-shadow: var(--shadow-lg);
    position: relative;
}

.testimonial-quote {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
}

.dailyverse-verse {
    font-size: 1.25rem;
    font-style: italic;
    color: var(--text-primary);
    line-height: 1.6;
}

.dailyverse-book {
    color: var(--primary-color);
    font-weight: 600;
}

/* ===== MODERN FOOTER ===== */
#footer {
    background: var(--gray-900);
    color: var(--gray-300);
    margin-top: 0;
}

#footer h5 {
    color: white;
    font-weight: 600;
}

#footer .footer-social-icons a {
    background: var(--gray-800);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

#footer .footer-social-icons a:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

/* ===== MODERN UTILITY CLASSES ===== */
.modern-gradient-bg {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.modern-text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.modern-border {
    border: 1px solid var(--gray-200);
}

.modern-shadow {
    box-shadow: var(--shadow-md);
}

.modern-shadow-lg {
    box-shadow: var(--shadow-lg);
}

/* ===== LEGACY COMPATIBILITY ===== */
.sticky-header-active #header .header-nav.header-nav-links.header-nav-light-text nav > ul > li > a,
#header .header-nav.header-nav-line.header-nav-light-text nav > ul > li > a {
    color: var(--text-primary);
}

.slider-container {
    background: var(--bg-accent);
}

.page-header {
    margin: 0;
}

#wpadminbar {
    display: none;
}

.custom-testimonial-style .testimonial-quote {
    margin: 12px 0 20px;
}

html .text-color-secondary, html .text-secondary {
    color: var(--primary-color) !important;
}

/* ===== MODERN ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
    animation: fadeInDown 0.6s ease-out;
}

.blink {
    animation: blinker 2s linear infinite;
}

@keyframes blinker {
    50% { opacity: 0; }
}

/* ===== MODERN RESPONSIVE DESIGN ===== */
@media (min-width: 320px) {
    .hero-title {
        font-size: 2rem;
    }

    .header-logo-non-sticky, .header-logo-sticky {
        height: 48px;
    }
    .header-logo-non-sticky {
        max-width: 220px;
    }
    .header-logo-sticky {
        max-width: 220px;
    }
    #home {
        height: 70vh;
        min-height: 400px;
    }

    .section {
        padding: 3rem 0;
    }

    .modern-card {
        padding: 1.5rem;
    }
}

@media (min-width: 375px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .header-logo .header-logo-sticky-change {
        height: 40px;
    }
    .header-logo-non-sticky {
        max-width: 280px;
    }
    .header-logo-sticky {
        max-width: 280px;
    }
    .footer-logo {
        max-width: 325px;
    }
    #home {
        height: 70vh;
        min-height: 450px;
    }
}

@media (min-width: 414px) {
    .header-logo .header-logo-sticky-change {
        height: 40px;
    }
    .header-logo-non-sticky {
        max-width: 300px;
    }
    .header-logo-sticky {
        max-width: 300px;
    }
    .footer-logo {
        max-width: 325px;
    }
    #home {
        height: 75vh;
        min-height: 500px;
    }
}

@media (min-width: 768px) {
    .header-logo-non-sticky, .header-logo-sticky {
        height: 48px;
    }
    .header-logo-non-sticky {
        max-width: 280px;
    }
    .header-logo-sticky {
        max-width: 280px;
    }
    #home {
        height: 80vh;
        min-height: 600px;
    }

    .section {
        padding: 5rem 0;
    }

    .modern-card {
        padding: 2rem;
    }
}

@media (min-width: 992px) {
    #header .header-nav-main nav > ul > li > a {
        font-size: 16px;
        font-weight: 500;
    }

    #header .header-nav.header-nav-links nav > ul > li.dropdown.open > .dropdown-menu,
    #header .header-nav.header-nav-links nav > ul > li.dropdown:hover > .dropdown-menu,
    #header .header-nav.header-nav-line nav > ul > li.dropdown.open > .dropdown-menu,
    #header .header-nav.header-nav-line nav > ul > li.dropdown:hover > .dropdown-menu {
        margin-top: -25px;
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--gray-200);
    }

    .sticky-header-active #header .header-nav.header-nav-links nav > ul > li.dropdown.open > .dropdown-menu,
    .sticky-header-active #header .header-nav.header-nav-links nav > ul > li.dropdown:hover > .dropdown-menu,
    .sticky-header-active #header .header-nav.header-nav-line nav > ul > li.dropdown.open > .dropdown-menu,
    .sticky-header-active #header .header-nav.header-nav-line nav > ul > li.dropdown:hover > .dropdown-menu {
        margin-top: 0;
    }

    .header-logo-non-sticky {
        max-width: 320px;
    }
    .header-logo-sticky {
        max-width: 320px;
    }
}

/* ===== MODERN UTILITY CLASSES ===== */
.card-2-lines {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.modern-spacing-sm {
    margin-bottom: 1rem;
}

.modern-spacing-md {
    margin-bottom: 2rem;
}

.modern-spacing-lg {
    margin-bottom: 3rem;
}

.modern-text-center {
    text-align: center;
}

.modern-flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ===== MODERN ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for better accessibility */
.btn:focus,
.header-nav-main nav > ul > li > a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}
