# Generated by Django 3.2.9 on 2022-10-20 12:25

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import sitetree.models
import tinymce.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('auth', '0012_alter_user_first_name_max_length'),
        ('flatpages', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ChurchFile',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('perspectief', 'Perspectief'), ('gemeentegids', 'Gemeentegids'), ('gezichtenboek', 'Gezichtenboek'), ('qrcollecte', 'QR-Code Collecte'), ('overig', 'Overig')], default='overig', max_length=13, verbose_name='Type bestand')),
                ('name', models.CharField(max_length=100, verbose_name='Naam bestand')),
                ('file', models.FileField(blank=True, help_text='De bijlage van dit bestand. Hoeft niet ingevuld te worden voor een QR-code', upload_to='%Y/%m/%d/', verbose_name='Bestand')),
                ('url', models.CharField(blank=True, help_text='De URL waarnaar dit bestand naar toe moet linken. Voor een QR-code is dit de link naar de betaling. De QR-code wordt dan automatisch gegenereerd.', max_length=255, null=True, verbose_name='Link URL')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='Datum upload')),
                ('notify_members', models.BooleanField(default=False, verbose_name='Gemeenteleden e-mailen (werkt nog niet)')),
            ],
            options={
                'verbose_name': 'Bestand',
                'verbose_name_plural': 'Bestanden',
                'get_latest_by': 'date',
            },
        ),
        migrations.CreateModel(
            name='Event',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(default=None, verbose_name='Datum')),
                ('start_time', models.TimeField(blank=True, null=True, verbose_name='Start tijd')),
                ('end_time', models.TimeField(blank=True, null=True, verbose_name='Eind tijd')),
                ('room', models.CharField(choices=[('GZ', 'Grote Zaal'), ('KERK', 'Kerkzaal'), ('ZA12', 'Zaal 1 & 2 (beneden)'), ('ZA1', 'Zaal 1 (beneden)'), ('ZA2', 'Zaal 2 (beneden)'), ('ZA3', 'Zaal 3 (linkerzaal boven)'), ('ZA4', 'Zaal 4 (rechterzaal boven)'), ('ALL', 'Alle zalen'), ('CON', 'Consistorie'), ('JEU', 'Jeugdhonk'), ('CRE', 'Creche'), ('UNS', 'Onbenoemde locatie'), ('NVT', 'Niet van toepassing')], default='GZ', max_length=7, verbose_name='Zaal')),
                ('title', models.CharField(max_length=100, verbose_name='Titel')),
                ('description', models.CharField(blank=True, max_length=100, null=True, verbose_name='Opmerkingen')),
                ('remark', models.TextField(blank=True, max_length=100, null=True, verbose_name='Opmerkingen koster (worden niet getoond op website)')),
                ('public', models.BooleanField(default=True, verbose_name='Publiek (toon op website)')),
            ],
            options={
                'verbose_name': 'Agenda/Zaalbeheer',
                'verbose_name_plural': 'Agenda/Zaalbeheer',
                'ordering': ['date', 'room'],
            },
        ),
        migrations.CreateModel(
            name='LiveService',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('live', models.BooleanField(default=False)),
                ('last_check', models.DateTimeField(default=datetime.datetime.now)),
            ],
        ),
        migrations.CreateModel(
            name='Minister',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(choices=[('MR', 'Dhr.'), ('DR', 'Dr.'), ('DRS', 'Drs.'), ('PROF_DR', 'Prof. dr.'), ('PROF', 'Prof.'), ('DS', 'Ds.'), ('KA', 'Kandidaat'), ('ST', 'Student'), ('BR', 'Br.')], default='DS', max_length=7)),
                ('initials', models.CharField(default=None, max_length=15, null=True, verbose_name='Initialen')),
                ('insertion', models.CharField(blank=True, default=None, max_length=15, null=True, verbose_name='Tussenvoegsel')),
                ('surname', models.CharField(default=None, max_length=200, null=True, verbose_name='Achternaam')),
                ('location', models.CharField(blank=True, default=None, help_text='Uit welke plaats de voorganger komt', max_length=200, null=True, verbose_name='Plaats')),
                ('favorite', models.BooleanField(default=False, help_text='Wanneer een voorganger favoriet is, verschijnt hij bovenaan in de lijst', max_length=5, verbose_name='Favoriet')),
            ],
            options={
                'verbose_name': 'Voorganger',
                'verbose_name_plural': 'Voorgangers',
                'ordering': ['-favorite', 'surname'],
            },
        ),
        migrations.CreateModel(
            name='MyTree',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(blank=True, help_text='Site tree title for presentational purposes.', max_length=100, verbose_name='Title')),
                ('alias', models.CharField(db_index=True, help_text='Short name to address site tree from templates.<br /><b>Note:</b> change with care.', max_length=80, unique=True, verbose_name='Alias')),
            ],
            options={
                'verbose_name': 'Site Tree',
                'verbose_name_plural': 'Site Trees',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Post',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='Titel')),
                ('text', tinymce.models.HTMLField(verbose_name='Bericht')),
                ('status', models.CharField(choices=[('concept', 'Concept'), ('published', 'Gepubliceerd'), ('private', 'Prive'), ('expired', 'Verlopen')], default='published', max_length=15)),
                ('created_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Datum aangemaakt')),
                ('last_modified', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Datum laatste bewerking')),
                ('published_date', models.DateTimeField(blank=True, null=True, verbose_name='Datum gepubliceerd')),
                ('author', models.ForeignKey(blank=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Auteur')),
                ('last_modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='entry_modifiers', to=settings.AUTH_USER_MODEL, verbose_name='Laatste bewerking')),
            ],
            options={
                'verbose_name': 'Nieuwsbericht',
                'verbose_name_plural': 'Nieuwsberichten',
            },
        ),
        migrations.CreateModel(
            name='MyFlatPage',
            fields=[
                ('flatpage_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='flatpages.flatpage')),
                ('menu', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='cgkobl.mytree', to_field='alias')),
            ],
            options={
                'verbose_name': 'Pagina',
                'verbose_name_plural': "Pagina's",
            },
            bases=('flatpages.flatpage',),
        ),
        migrations.CreateModel(
            name='ChurchService',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('1', '1'), ('2', '2')], default='2', max_length=1, verbose_name='Aantal diensten')),
                ('date', models.DateField(default=None, verbose_name='Datum dienst(en)')),
                ('time1', models.TimeField(choices=[(datetime.time(9, 30), '09:30'), (datetime.time(10, 0), '10:00'), (datetime.time(10, 30), '10:30'), (datetime.time(11, 0), '11:00'), (datetime.time(11, 15), '11:15'), (datetime.time(14, 30), '14:30'), (datetime.time(15, 0), '15:00'), (datetime.time(15, 30), '15:30'), (datetime.time(16, 0), '16:00'), (datetime.time(16, 30), '16:30'), (datetime.time(17, 0), '17:00'), (datetime.time(17, 30), '17:30'), (datetime.time(18, 0), '18:00'), (datetime.time(18, 30), '18:30')], default=datetime.time(9, 30), verbose_name='Tijdstip eerste dienst')),
                ('time2', models.TimeField(blank=True, choices=[(datetime.time(9, 30), '09:30'), (datetime.time(10, 0), '10:00'), (datetime.time(10, 30), '10:30'), (datetime.time(11, 0), '11:00'), (datetime.time(11, 15), '11:15'), (datetime.time(14, 30), '14:30'), (datetime.time(15, 0), '15:00'), (datetime.time(15, 30), '15:30'), (datetime.time(16, 0), '16:00'), (datetime.time(16, 30), '16:30'), (datetime.time(17, 0), '17:00'), (datetime.time(17, 30), '17:30'), (datetime.time(18, 0), '18:00'), (datetime.time(18, 30), '18:30')], default=datetime.time(17, 0), null=True, verbose_name='Tijdstip tweede dienst')),
                ('title', models.CharField(default='Kerkdienst', max_length=100, verbose_name='Titel')),
                ('description', models.CharField(blank=True, max_length=100, null=True, verbose_name='Opmerkingen')),
                ('announcements', models.FileField(blank=True, upload_to='%Y/%m/%d/', verbose_name='Mededelingen')),
                ('youtube_id_1', models.CharField(blank=True, default=None, max_length=50, null=True, verbose_name='YouTube ID voor eerste dienst')),
                ('youtube_id_2', models.CharField(blank=True, default=None, max_length=50, null=True, verbose_name='YouTube ID voor tweede dienst')),
                ('liturgy', tinymce.models.HTMLField(blank=True, null=True, verbose_name='Liturgie')),
                ('liturgy_mail', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, to='django_mailbox.message', verbose_name='Liturgie binnengekomen via e-mail')),
                ('minister1', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='minister1', to='cgkobl.minister', verbose_name='Voorganger eerste dienst')),
                ('minister2', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='minister2', to='cgkobl.minister', verbose_name='Voorganger tweede dienst')),
            ],
            options={
                'verbose_name': 'Kerkdienst',
                'verbose_name_plural': 'Kerkdiensten',
                'ordering': ['date'],
            },
        ),
        migrations.CreateModel(
            name='MyTreeItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Site tree item title. Can contain template variables E.g.: {{ mytitle }}.', max_length=100, verbose_name='Title')),
                ('hint', models.CharField(blank=True, default='', help_text='Some additional information about this item that is used as a hint.', max_length=200, verbose_name='Hint')),
                ('url', models.CharField(db_index=True, help_text='Exact URL or URL pattern (see "Additional settings") for this item.', max_length=200, verbose_name='URL')),
                ('urlaspattern', models.BooleanField(db_index=True, default=False, help_text='Whether the given URL should be treated as a pattern.<br /><b>Note:</b> Refer to Django "URL dispatcher" documentation (e.g. "Naming URL patterns" part).', verbose_name='URL as Pattern')),
                ('hidden', models.BooleanField(db_index=True, default=False, help_text='Whether to show this item in navigation.', verbose_name='Hidden')),
                ('alias', sitetree.models.CharFieldNullable(blank=True, db_index=True, help_text='Short name to address site tree item from a template.<br /><b>Reserved aliases:</b> "trunk", "this-children", "this-siblings", "this-ancestor-children", "this-parent-siblings".', max_length=80, null=True, verbose_name='Alias')),
                ('description', models.TextField(blank=True, default='', help_text='Additional comments on this item.', verbose_name='Description')),
                ('inmenu', models.BooleanField(db_index=True, default=True, help_text='Whether to show this item in a menu.', verbose_name='Show in menu')),
                ('inbreadcrumbs', models.BooleanField(db_index=True, default=True, help_text='Whether to show this item in a breadcrumb path.', verbose_name='Show in breadcrumb path')),
                ('insitetree', models.BooleanField(db_index=True, default=True, help_text='Whether to show this item in a site tree.', verbose_name='Show in site tree')),
                ('access_loggedin', models.BooleanField(db_index=True, default=False, help_text='Check it to grant access to this item to authenticated users only.', verbose_name='Logged in only')),
                ('access_guest', models.BooleanField(db_index=True, default=False, help_text='Check it to grant access to this item to guests only.', verbose_name='Guests only')),
                ('access_restricted', models.BooleanField(db_index=True, default=False, help_text='Check it to restrict user access to this item, using Django permissions system.', verbose_name='Restrict access to permissions')),
                ('access_perm_type', models.IntegerField(choices=[(1, 'Any'), (2, 'All')], default=1, help_text='<b>Any</b> &mdash; user should have any of chosen permissions. <b>All</b> &mdash; user should have all chosen permissions.', verbose_name='Permissions interpretation')),
                ('sort_order', models.IntegerField(db_index=True, default=0, help_text='Item position among other site tree items under the same parent.', verbose_name='Sort order')),
                ('mobile_only', models.BooleanField(default=False, help_text='Whether to show this item only on mobile', verbose_name='Show only on mobile')),
                ('access_permissions', models.ManyToManyField(blank=True, to='auth.Permission', verbose_name='Permissions granting access')),
                ('parent', models.ForeignKey(blank=True, help_text='Parent site tree item.', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='mytreeitem_parent', to='cgkobl.mytreeitem', verbose_name='Parent')),
                ('tree', models.ForeignKey(help_text='Site tree this item belongs to.', on_delete=django.db.models.deletion.CASCADE, related_name='mytreeitem_tree', to='cgkobl.mytree', verbose_name='Site Tree')),
            ],
            options={
                'verbose_name': 'Site Tree Item',
                'verbose_name_plural': 'Site Tree Items',
                'abstract': False,
                'unique_together': {('tree', 'alias')},
            },
        ),
    ]
