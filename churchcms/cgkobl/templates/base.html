<!-- templates/base.html -->
<!DOCTYPE html>
<html>
<head>
    <!-- Basic -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="theme-color" content="#0073cc" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="google-site-verification" content="vV6pMRO38wE3bXFbpUQbU-Vl9rVPt5ZNvoTOps4T6tI" />
    {% load static %}
    {% load sitetree %}
    {% load compress %}
    <title>{% block html_title %}Home{% endblock %} - Christelijke Gereformeerde Gedachteniskerk - Oud-Beijerland</title>

    <meta name="keywords" content="kerk, oud-beijerland, kerk oud-beijerland, cgk, hoeksche waard, geda<PERSON><PERSON><PERSON>rk, kerk, gedachtenis kerk" />
    <meta name="description" content="<PERSON><PERSON><PERSON>e Gereformeerde Gedachteniskerk - Oud-Beijerland">

    <!-- Favicon -->
    <link rel="shortcut icon" href="{% static 'img/favicon.ico' %}" type="image/x-icon" />
    <link rel="apple-touch-icon" href="{% static 'img/app-icon.png' %}" />

    <!-- Mobile Metas -->
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1.0, shrink-to-fit=no">

    <!-- Web Fonts  -->
    <link href="https://fonts.googleapis.com/css?family=Playfair+Display:400,400i,700%7CSintony:400,700" rel="stylesheet" type="text/css">

    <!-- Vendor CSS -->
    {% compress css %}
    <link rel="stylesheet" href="{% static 'vendor/bootstrap/css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'vendor/fontawesome-free/css/all.min.css' %}">
    <link rel="stylesheet" href="{% static 'vendor/animate/animate.compat.css' %}">
    <link rel="stylesheet" href="{% static 'vendor/owl.carousel/assets/owl.carousel.min.css' %}">
    <link rel="stylesheet" href="{% static 'vendor/owl.carousel/assets/owl.theme.default.min.css' %}">
    <link rel="stylesheet" href="{% static 'vendor/simple-line-icons/css/simple-line-icons.min.css' %}">
    <link rel="stylesheet" href="{% static 'vendor/magnific-popup/magnific-popup.min.css' %}">

    <!-- Theme CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <link rel="stylesheet" href="{% static 'css/theme-elements.css' %}">
    <link rel="stylesheet" href="{% static 'css/theme-blog.css' %}">

    <!-- Demo CSS -->
    <link rel="stylesheet" href="{% static 'css/church.css' %}">

    <!-- Skin CSS -->
    <link rel="stylesheet" href="{% static 'css/skin-church.css' %}">

    <!-- Theme Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/custom.css' %}">
    {% endcompress %}

    <!-- Head Libs -->
    {% compress js preload %}
    <script src="{% static 'vendor/modernizr/modernizr.min.js' %}"></script>
    {% endcompress %}

    {% block header_static %}
    {% endblock %}

</head>
<body>

<div class="body">
    <header id="header" class="header-transparent header-effect-shrink" data-plugin-options="{'stickyEnabled': true, 'stickyEffect': 'shrink', 'stickyEnableOnBoxed': true, 'stickyEnableOnMobile': true, 'stickyChangeLogo': true, 'stickyStartAt': 30, 'stickyHeaderContainerHeight': 70}">
        <div class="header-body border-top-0 header-body-bottom-border">
            <div class="header-container container">
                {% if request.headers.host == "127.0.0.1:8000" %}
                <div class="header-row development-header">
                    <p><b>Please note!</b> You are currently in the development environment.</p>
                </div>
                {% endif %}
                <div class="header-row">
                    <div class="header-column">
                        <div class="header-row">
                            <div class="header-logo header-logo-sticky-change">
                                <a href="/">
                                    <svg class="header-logo-non-sticky align-content-center" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="320" height="48" viewBox="0 0 3323 500">
                                        <defs>
                                            <clipPath id="clip-Artboard_3_Path">
                                                <rect width="3323" height="500"/>
                                            </clipPath>
                                        </defs>
                                        <g id="Artboard_3_Path" data-name="Artboard 3 Path" clip-path="url(#clip-Artboard_3_Path)">
                                            <g id="Group_1" data-name="Group 1" transform="translate(0.93 70.731)">
                                                <path id="Path_1" data-name="Path 1" d="M227.152,265.255H164.035V191.972H128.541v82.269h26.508V376.5h-71V274.242h26.217V191.972H75.059v73.283H11.367v8.986H75.059V376.5H11.367V160.731c.284-6.314.721-12.4,1.223-18.359H75.059v31.336h35.21V133.385H84.053V-30.921a144.753,144.753,0,0,1,21.228-16.407,104.007,104.007,0,0,1,11.4-6.263c1.078-.51,1.944-.881,2.593-1.151.634.269,1.507.641,2.578,1.151,6.175,2.92,18.781,9.788,33.2,23.245V133.385H128.541v40.323h35.494V142.372h61.857c.524,6.037.976,12.169,1.267,18.556Zm0,111.246H164.035V274.242h63.117ZM69.655-15.846c1.806-2.126,3.612-4.144,5.4-6.088v155.32H13.486C21.4,58.376,46.046,12.082,69.655-15.846m94.38-5.447c25.037,27.2,52.543,74.463,61.027,154.679H164.035ZM120.705-63.8l-1.442-.5-1.456.5c-1.078.124-107.1,37.606-115.426,224.328V385.51H236.146V160.731C227.815-26.194,121.812-63.677,120.705-63.8" transform="translate(10.948 29.559)" fill="#2971b9"/>
                                                <path id="Path_2" data-name="Path 2" d="M128.31-60.721l1.612-4.722Zm-1.258,1.909a114.261,114.261,0,0,0-12.682,6.956c-10.6,6.666-24.925,17.666-39.594,35C45.461,17.809,14.661,77.8,9.981,179.421V419.259H249.841l.014-239.619C241.563,1.507,153.773-48.866,132.8-58.813c-1.195-.58-2.163-.976-2.877-1.279-.714.3-1.682.7-2.87,1.279M0,429.269V179.188C9.246-28.366,127.094-70.038,128.31-70.18l1.6-.551,1.619.551c1.223.141,119.064,41.813,128.31,249.6V429.269Z" transform="translate(0 0)" fill="#2971b9"/>
                                            </g>
                                            <path id="Path_20" data-name="Path 20" d="M193.95-111.15v-12.6H111v42.3h70.35V-27.6c-19.05,12-40.05,18.15-63.9,18.15-58.95,0-86.1-49.8-86.1-103.05v-2.1c0-49.65,29.25-100.8,84.3-100.8,20.7,0,36.9,4.2,52.65,13.65l-8.7,15.6c-12.45-7.65-24.15-12.15-43.05-12.15-42.75,0-66.3,42.9-66.3,83.7v2.1c0,43.35,21,85.95,66.75,85.95,14.55,0,27.9-2.4,39-7.5l6.45-3V-64.5h-12.6v19.35c-7.8,3.45-18.45,6-32.85,6-39.9,0-54.15-39.9-54.15-73.35v-2.1c0-31.8,17.4-71.1,53.7-71.1,21.45,0,31.8,6.6,47.4,17.7l20.85-37.65C165.15-220.35,144.45-228,115.65-228c-61.8,0-96.9,55.95-96.9,113.4v2.1c0,63,34.5,115.65,98.7,115.65,37.2,0,62.1-13.8,76.5-24v-73.2H123.6v-17.1Zm96.6,68.85v-84.15h76.8v17.1h-57.9v12.6h70.5v-42.3h-102V-29.7H372.9v17.1H259.05V-212.1H370.8V-195H277.95v38.85h12.6V-182.4H383.4v-42.3H246.45V0H385.5V-42.3ZM508.8-224.7H438v12.6h70.8c60.6,0,87.9,42.75,87.9,99.6,0,60.6-35.25,99.9-90,99.9H450.6V-182.4h58.2c35.85,0,56.4,24.75,56.4,69.9,0,42.15-22.2,70.2-56.4,70.2H482.1V-165.45H469.5V-29.7h39.3c41.25,0,69-33.3,69-82.8,0-51.75-25.8-82.5-68.85-82.5H438V0h68.7c61.5,0,102.6-44.85,102.6-112.5C609.3-184.65,569.25-224.7,508.8-224.7Zm248.85,0h-44.1L663-85.65H775.8L735.6-196.2l-29.4,80.85h13.35L735.6-159.3l22.2,61.05H681L722.4-212.1h26.4L821.4-12.6H802.2L781.95-68.55H656.7L631.8,0h45.9l14.1-38.85H678.45l-9.6,26.25H649.8l15.75-43.35H773.1L793.35,0H839.4Zm254.7,166.95C997.2-47.7,981-39.15,961.5-39.15c-33.6,0-50.1-26.4-54.3-55.2H894.45c4.8,35.85,26.1,67.8,67.05,67.8,17.85,0,33-6,45.9-13.35l8.4,16.05a111.838,111.838,0,0,1-55.5,14.4c-57.45,0-85.8-48.3-85.8-100.95v-.9H906v-3.3c0-35.1,18.9-71.1,52.5-71.1,21.3,0,31.65,6.6,47.25,17.7l21-37.65C1007.1-220.35,986.25-228,957.6-228c-52.2,0-84.45,40.35-93.3,87h12.9c8.25-40.05,35.55-74.4,80.4-74.4,20.55,0,36.9,4.2,52.5,13.65l-8.7,15.6c-12.3-7.65-24-12.15-42.9-12.15-38.85,0-61.8,36.75-64.65,74.4H862.2c-.15,3-.3,6.15-.3,9.3v4.2c0,62.55,36,113.55,98.4,113.55,29.55,0,52.5-9.3,72.15-22.35Zm188.1-166.95v85.5H1086.3v-72.9h18.9v55.8h12.6v-68.4h-44.1v98.1h139.35v-85.5h18.9V-12.6h-18.9v-96.9H1073.7V0h44.1V-79.8h-12.6v67.2h-18.9V-96.9h114.15V0h44.1V-224.7Zm175.5,0V-12.6h-18.9V-224.7H1285.8v42.3h39.75V-195H1298.4v-17.1h46.05V0h44.1V-212.1h45.9V-195h-27v12.6h39.6v-42.3ZM1532.4-42.3v-84.15h76.8v17.1h-57.9v12.6h70.5v-42.3h-102V-29.7h94.95v17.1H1500.9V-212.1h111.75V-195H1519.8v38.85h12.6V-182.4h92.85v-42.3H1488.3V0h139.05V-42.3Zm282.45-35.4-86.55-147h-14.7L1827.45-31.35V-212.1h18.9V-12.6H1817.4L1692.3-224.7h-12.45V0h44.1V-109.8l-12.6-21.45V-12.6h-18.9V-199.65L1810.2,0h48.75V-224.7h-44.1ZM1966.8-182.4v-42.3h-44.1v12.6h31.5V-195h-31.5V0h44.1V-165.3h-12.6V-12.6h-18.9V-182.4Zm143.1,36.15-4.2,11.85c36,14.85,52.05,37.35,52.05,71.25,0,40.2-34.2,53.7-69.3,53.7-22.2,0-41.4-4.35-62.55-14.4l6.45-17.1c20.55,9.6,39.45,14.4,56.1,14.4,43.95,0,50.4-22.95,50.4-36.6,0-35.7-23.85-46.8-44.7-58.05-16.35-8.85-30.9-16.35-30.9-39.45,0-15.9,11.55-25.05,33.9-25.05,17.55,0,33.75,5.1,52.05,14.55l15-40.8c-18.9-9.6-32.25-16.05-67.2-16.05-41.55,0-77.85,22.8-77.85,67.5,0,34.8,17.7,59.55,49.65,73.95l4.35-11.85c-23.25-10.65-41.4-29.4-41.4-62.1,0-36.45,29.4-54.9,65.25-54.9,27.6,0,38.1,3.75,51.45,9.75l-6.3,17.25c-16.05-6.75-30.3-9.9-45-9.9-33.3,0-46.5,18.3-46.5,37.65,0,29.4,18.45,40.35,37.2,50.4,19.05,10.35,38.4,18.15,38.4,47.1,0,16.8-13.5,24-37.8,24-20.4,0-42.45-8.4-63-19.2l-15.3,40.65c22.5,12.15,46.65,20.85,78.3,20.85,47.55,0,81.9-22.5,81.9-66.3C2170.35-104.55,2149.05-130.35,2109.9-146.25Z" transform="translate(308 399)" fill="#fff"/>
                                            <path id="Path_21" data-name="Path 21" d="M109.65-117l92.7-107.7H145.8L61.5-126.45V-12.6H42.6V-212.1H61.5v58.8L74.1-168v-56.7H30V0H74.1V-121.8l77.4-90.3h23.4L93-117,182.85-12.6h-24L93-89.7v19.5L153.15,0H210.3ZM291.6-42.3v-84.15h76.8v17.1H310.5v12.6H381v-42.3H279V-29.7h94.95v17.1H260.1V-212.1H371.85V-195H279v38.85h12.6V-182.4h92.85v-42.3H247.5V0H386.55V-42.3Zm225-182.4H439.05V0h44.1V-101.25L557.4,0h53.7L529.35-110.25c22.95-3.3,36.3-19.35,36.3-43.2,0-25.2-18.3-41.55-46.8-41.55h-48.3v56.1h12.6v-43.5h35.7c20.85,0,34.2,10.05,34.2,28.95,0,16.8-7.95,31.65-35.7,31.65h-12.3l81,109.2h-22.2L483.6-121.8H470.55V-12.6h-18.9V-212.1H516.6c41.1,0,67.95,22.5,67.95,58.65,0,22.5-8.7,40.05-27.6,51.15l7.35,10.05c21-11.85,32.85-33.9,32.85-61.2C597.15-193.35,568.35-224.7,516.6-224.7ZM729.3-117,822-224.7H765.45l-84.3,98.25V-12.6h-18.9V-212.1h18.9v58.8l12.6-14.7v-56.7h-44.1V0h44.1V-121.8l77.4-90.3h23.4L712.65-117,802.5-12.6h-24L712.65-89.7v19.5L772.8,0h57.15Z" transform="translate(2497 399)" fill="#2971b9"/>
                                        </g>
                                    </svg>
                                    <svg class="header-logo-sticky align-content-center" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="319" height="48" viewBox="0 0 3323 500">
                                        <defs>
                                            <clipPath id="clip-Artboard_3-GRAY_2">
                                                <rect width="3323" height="500"/>
                                            </clipPath>
                                        </defs>
                                        <g id="Artboard_3-GRAY_2" data-name="Artboard – 3-GRAY – 2" clip-path="url(#clip-Artboard_3-GRAY_2)">
                                            <g id="Group_1" data-name="Group 1" transform="translate(0.93 70.731)">
                                                <path id="Path_1" data-name="Path 1" d="M227.152,265.255H164.035V191.972H128.541v82.269h26.508V376.5h-71V274.242h26.217V191.972H75.059v73.283H11.367v8.986H75.059V376.5H11.367V160.731c.284-6.314.721-12.4,1.223-18.359H75.059v31.336h35.21V133.385H84.053V-30.921a144.753,144.753,0,0,1,21.228-16.407,104.007,104.007,0,0,1,11.4-6.263c1.078-.51,1.944-.881,2.593-1.151.634.269,1.507.641,2.578,1.151,6.175,2.92,18.781,9.788,33.2,23.245V133.385H128.541v40.323h35.494V142.372h61.857c.524,6.037.976,12.169,1.267,18.556Zm0,111.246H164.035V274.242h63.117ZM69.655-15.846c1.806-2.126,3.612-4.144,5.4-6.088v155.32H13.486C21.4,58.376,46.046,12.082,69.655-15.846m94.38-5.447c25.037,27.2,52.543,74.463,61.027,154.679H164.035ZM120.705-63.8l-1.442-.5-1.456.5c-1.078.124-107.1,37.606-115.426,224.328V385.51H236.146V160.731C227.815-26.194,121.812-63.677,120.705-63.8" transform="translate(10.948 29.559)" fill="#2971b9"/>
                                                <path id="Path_2" data-name="Path 2" d="M128.31-60.721l1.612-4.722Zm-1.258,1.909a114.261,114.261,0,0,0-12.682,6.956c-10.6,6.666-24.925,17.666-39.594,35C45.461,17.809,14.661,77.8,9.981,179.421V419.259H249.841l.014-239.619C241.563,1.507,153.773-48.866,132.8-58.813c-1.195-.58-2.163-.976-2.877-1.279-.714.3-1.682.7-2.87,1.279M0,429.269V179.188C9.246-28.366,127.094-70.038,128.31-70.18l1.6-.551,1.619.551c1.223.141,119.064,41.813,128.31,249.6V429.269Z" transform="translate(0 0)" fill="#2971b9"/>
                                            </g>
                                            <path id="Path_20" data-name="Path 20" d="M193.95-111.15v-12.6H111v42.3h70.35V-27.6c-19.05,12-40.05,18.15-63.9,18.15-58.95,0-86.1-49.8-86.1-103.05v-2.1c0-49.65,29.25-100.8,84.3-100.8,20.7,0,36.9,4.2,52.65,13.65l-8.7,15.6c-12.45-7.65-24.15-12.15-43.05-12.15-42.75,0-66.3,42.9-66.3,83.7v2.1c0,43.35,21,85.95,66.75,85.95,14.55,0,27.9-2.4,39-7.5l6.45-3V-64.5h-12.6v19.35c-7.8,3.45-18.45,6-32.85,6-39.9,0-54.15-39.9-54.15-73.35v-2.1c0-31.8,17.4-71.1,53.7-71.1,21.45,0,31.8,6.6,47.4,17.7l20.85-37.65C165.15-220.35,144.45-228,115.65-228c-61.8,0-96.9,55.95-96.9,113.4v2.1c0,63,34.5,115.65,98.7,115.65,37.2,0,62.1-13.8,76.5-24v-73.2H123.6v-17.1Zm96.6,68.85v-84.15h76.8v17.1h-57.9v12.6h70.5v-42.3h-102V-29.7H372.9v17.1H259.05V-212.1H370.8V-195H277.95v38.85h12.6V-182.4H383.4v-42.3H246.45V0H385.5V-42.3ZM508.8-224.7H438v12.6h70.8c60.6,0,87.9,42.75,87.9,99.6,0,60.6-35.25,99.9-90,99.9H450.6V-182.4h58.2c35.85,0,56.4,24.75,56.4,69.9,0,42.15-22.2,70.2-56.4,70.2H482.1V-165.45H469.5V-29.7h39.3c41.25,0,69-33.3,69-82.8,0-51.75-25.8-82.5-68.85-82.5H438V0h68.7c61.5,0,102.6-44.85,102.6-112.5C609.3-184.65,569.25-224.7,508.8-224.7Zm248.85,0h-44.1L663-85.65H775.8L735.6-196.2l-29.4,80.85h13.35L735.6-159.3l22.2,61.05H681L722.4-212.1h26.4L821.4-12.6H802.2L781.95-68.55H656.7L631.8,0h45.9l14.1-38.85H678.45l-9.6,26.25H649.8l15.75-43.35H773.1L793.35,0H839.4Zm254.7,166.95C997.2-47.7,981-39.15,961.5-39.15c-33.6,0-50.1-26.4-54.3-55.2H894.45c4.8,35.85,26.1,67.8,67.05,67.8,17.85,0,33-6,45.9-13.35l8.4,16.05a111.838,111.838,0,0,1-55.5,14.4c-57.45,0-85.8-48.3-85.8-100.95v-.9H906v-3.3c0-35.1,18.9-71.1,52.5-71.1,21.3,0,31.65,6.6,47.25,17.7l21-37.65C1007.1-220.35,986.25-228,957.6-228c-52.2,0-84.45,40.35-93.3,87h12.9c8.25-40.05,35.55-74.4,80.4-74.4,20.55,0,36.9,4.2,52.5,13.65l-8.7,15.6c-12.3-7.65-24-12.15-42.9-12.15-38.85,0-61.8,36.75-64.65,74.4H862.2c-.15,3-.3,6.15-.3,9.3v4.2c0,62.55,36,113.55,98.4,113.55,29.55,0,52.5-9.3,72.15-22.35Zm188.1-166.95v85.5H1086.3v-72.9h18.9v55.8h12.6v-68.4h-44.1v98.1h139.35v-85.5h18.9V-12.6h-18.9v-96.9H1073.7V0h44.1V-79.8h-12.6v67.2h-18.9V-96.9h114.15V0h44.1V-224.7Zm175.5,0V-12.6h-18.9V-224.7H1285.8v42.3h39.75V-195H1298.4v-17.1h46.05V0h44.1V-212.1h45.9V-195h-27v12.6h39.6v-42.3ZM1532.4-42.3v-84.15h76.8v17.1h-57.9v12.6h70.5v-42.3h-102V-29.7h94.95v17.1H1500.9V-212.1h111.75V-195H1519.8v38.85h12.6V-182.4h92.85v-42.3H1488.3V0h139.05V-42.3Zm282.45-35.4-86.55-147h-14.7L1827.45-31.35V-212.1h18.9V-12.6H1817.4L1692.3-224.7h-12.45V0h44.1V-109.8l-12.6-21.45V-12.6h-18.9V-199.65L1810.2,0h48.75V-224.7h-44.1ZM1966.8-182.4v-42.3h-44.1v12.6h31.5V-195h-31.5V0h44.1V-165.3h-12.6V-12.6h-18.9V-182.4Zm143.1,36.15-4.2,11.85c36,14.85,52.05,37.35,52.05,71.25,0,40.2-34.2,53.7-69.3,53.7-22.2,0-41.4-4.35-62.55-14.4l6.45-17.1c20.55,9.6,39.45,14.4,56.1,14.4,43.95,0,50.4-22.95,50.4-36.6,0-35.7-23.85-46.8-44.7-58.05-16.35-8.85-30.9-16.35-30.9-39.45,0-15.9,11.55-25.05,33.9-25.05,17.55,0,33.75,5.1,52.05,14.55l15-40.8c-18.9-9.6-32.25-16.05-67.2-16.05-41.55,0-77.85,22.8-77.85,67.5,0,34.8,17.7,59.55,49.65,73.95l4.35-11.85c-23.25-10.65-41.4-29.4-41.4-62.1,0-36.45,29.4-54.9,65.25-54.9,27.6,0,38.1,3.75,51.45,9.75l-6.3,17.25c-16.05-6.75-30.3-9.9-45-9.9-33.3,0-46.5,18.3-46.5,37.65,0,29.4,18.45,40.35,37.2,50.4,19.05,10.35,38.4,18.15,38.4,47.1,0,16.8-13.5,24-37.8,24-20.4,0-42.45-8.4-63-19.2l-15.3,40.65c22.5,12.15,46.65,20.85,78.3,20.85,47.55,0,81.9-22.5,81.9-66.3C2170.35-104.55,2149.05-130.35,2109.9-146.25Z" transform="translate(308 399)" fill="#707070"/>
                                            <path id="Path_21" data-name="Path 21" d="M109.65-117l92.7-107.7H145.8L61.5-126.45V-12.6H42.6V-212.1H61.5v58.8L74.1-168v-56.7H30V0H74.1V-121.8l77.4-90.3h23.4L93-117,182.85-12.6h-24L93-89.7v19.5L153.15,0H210.3ZM291.6-42.3v-84.15h76.8v17.1H310.5v12.6H381v-42.3H279V-29.7h94.95v17.1H260.1V-212.1H371.85V-195H279v38.85h12.6V-182.4h92.85v-42.3H247.5V0H386.55V-42.3Zm225-182.4H439.05V0h44.1V-101.25L557.4,0h53.7L529.35-110.25c22.95-3.3,36.3-19.35,36.3-43.2,0-25.2-18.3-41.55-46.8-41.55h-48.3v56.1h12.6v-43.5h35.7c20.85,0,34.2,10.05,34.2,28.95,0,16.8-7.95,31.65-35.7,31.65h-12.3l81,109.2h-22.2L483.6-121.8H470.55V-12.6h-18.9V-212.1H516.6c41.1,0,67.95,22.5,67.95,58.65,0,22.5-8.7,40.05-27.6,51.15l7.35,10.05c21-11.85,32.85-33.9,32.85-61.2C597.15-193.35,568.35-224.7,516.6-224.7ZM729.3-117,822-224.7H765.45l-84.3,98.25V-12.6h-18.9V-212.1h18.9v58.8l12.6-14.7v-56.7h-44.1V0h44.1V-121.8l77.4-90.3h23.4L712.65-117,802.5-12.6h-24L712.65-89.7v19.5L772.8,0h57.15Z" transform="translate(2497 399)" fill="#2971b9"/>
                                        </g>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="header-column justify-content-end">
                        <div class="header-row">
                            <div class="header-nav header-nav-links header-nav-light-text order-2 order-lg-1">
                                <div class="header-nav-main header-nav-main-square header-nav-main-dropdown-no-borders header-nav-main-effect-2 header-nav-main-sub-effect-1">
                                    {% block mainmenu %}
                                        {% sitetree_menu from "header" include "trunk,topmenu" template "menu/header.html" %}
                                    {% endblock %}
                                </div>
                                <button class="btn header-btn-collapse-nav" data-bs-toggle="collapse" data-bs-target=".header-nav-main nav">
                                    <i class="fas fa-bars"></i>
                                </button>
                            </div>
                            <!-- Login button for laptop/desktop -->
                            <div class="header-nav-features header-nav-features-no-border header-nav-features-lg-show-border order-1 order-lg-2 d-none d-lg-none d-xl-block">
                                <div class="header-nav-feature header-nav-features-user d-inline-flex mx-2 pr-2 signin" id="headerAccount">
                                    {% if request.user.is_authenticated %}
                                        <a href="{% url 'user-home' %}" class="btn btn-xl btn-rounded btn-primary text-1 ml-3 font-weight-bold text-uppercase">
                                            <i class="far fa-user"></i> Mijn Gedachteniskerk
                                        </a>
                                    {% else %}
                                        <a href="{% url 'magicauth-login' %}" class="btn btn-xl btn-rounded btn-primary text-1 ml-3 font-weight-bold text-uppercase">
                                            <i class="far fa-user"></i> Inloggen
                                        </a>
                                    {% endif %}
                                </div>
                            </div>
                            <!-- Login button for tablets -->
                            <div class="header-nav-features header-nav-features-no-border header-nav-features-lg-show-border order-1 order-lg-2 d-none d-sm-block d-md-block d-lg-block d-xl-none">
                                <div class="header-nav-feature header-nav-features-user d-inline-flex mx-2 pr-2 signin" id="headerAccount">
                                    {% if request.user.is_authenticated %}
                                        <a href="{% url 'user-home' %}" class="btn btn-xl btn-rounded btn-primary text-1 ml-3 font-weight-bold text-uppercase">
                                            <i class="far fa-user"></i>
                                        </a>
                                    {% else %}
                                        <a href="{% url 'magicauth-login' %}" class="btn btn-xl btn-rounded btn-primary text-1 ml-3 font-weight-bold text-uppercase">
                                            <i class="far fa-user"></i>
                                        </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </header>
{% block main %}
{% endblock %}
{% block footer %}
    <footer id="footer">
        <div class="container">
            <div class="row py-5">
                <div class="col-md-4 d-flex justify-content-center justify-content-md-start mb-4 mb-lg-0">
                    <a href="index.html" class="logo pr-0 pr-lg-3 pl-0 pl-md-0">
                        <svg class="footer-logo" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="397" height="90" viewBox="0 0 3493 792">
                            <defs>
                                <clipPath id="clip-Artboard_4_Path_only">
                                    <rect width="3493" height="792"/>
                                </clipPath>
                            </defs>
                            <g id="Artboard_4_Path_only" data-name="Artboard – 4 Path only" clip-path="url(#clip-Artboard_4_Path_only)">
                                <g id="Group_1" data-name="Group 1" transform="translate(30 70.474)">
                                    <path id="Path_1" data-name="Path 1" d="M348.1,442.586H251.02V329.871H196.427V456.408H237.2V613.692H128V456.408h40.324V329.871H114.167V442.586H16.2v13.822h97.964V613.692H16.2V281.818c.437-9.711,1.109-19.075,1.882-28.238h96.082v48.2h54.157v-62.02H128V-12.958a222.642,222.642,0,0,1,32.651-25.236,159.972,159.972,0,0,1,17.53-9.633c1.658-.784,2.991-1.355,3.988-1.77.974.414,2.319.986,3.965,1.77,9.5,4.492,28.887,15.054,51.065,35.753V239.759H196.427v62.02H251.02v-48.2h95.141c.806,9.286,1.5,18.717,1.949,28.54Zm0,171.106H251.02V456.408H348.1ZM105.855,10.228c2.778-3.271,5.556-6.373,8.311-9.364V239.759h-94.7c12.164-115.37,50.08-186.575,86.393-229.531M251.02,1.85c38.509,41.836,80.815,114.53,93.864,237.909H251.02ZM184.374-63.53l-2.218-.773-2.24.773C178.259-63.34,15.183-5.689,2.381,281.5V627.548H361.932V281.818C349.118-5.689,186.077-63.34,184.374-63.53" transform="translate(24.285 65.569)" fill="#2971b9"/>
                                    <path id="Path_2" data-name="Path 2" d="M203.309-54.87l2.554-7.482Zm-1.994,3.024a181.048,181.048,0,0,0-20.095,11.022c-16.79,10.563-39.495,27.991-62.737,55.456C72.034,69.561,23.231,164.613,15.816,325.638V705.665H395.876l.022-379.68C382.76,43.732,243.655-36.086,210.422-51.846c-1.893-.918-3.428-1.546-4.559-2.027-1.131.47-2.666,1.109-4.548,2.027M0,721.526V325.269C14.651-3.6,201.383-69.633,203.309-69.857l2.543-.874,2.565.874c1.938.224,188.658,66.254,203.309,395.5V721.526Z" transform="translate(0 0)" fill="#2971b9"/>
                                </g>
                                <path id="Path_23" data-name="Path 23" d="M193.95-111.15v-12.6H111v42.3h70.35V-27.6c-19.05,12-40.05,18.15-63.9,18.15-58.95,0-86.1-49.8-86.1-103.05v-2.1c0-49.65,29.25-100.8,84.3-100.8,20.7,0,36.9,4.2,52.65,13.65l-8.7,15.6c-12.45-7.65-24.15-12.15-43.05-12.15-42.75,0-66.3,42.9-66.3,83.7v2.1c0,43.35,21,85.95,66.75,85.95,14.55,0,27.9-2.4,39-7.5l6.45-3V-64.5h-12.6v19.35c-7.8,3.45-18.45,6-32.85,6-39.9,0-54.15-39.9-54.15-73.35v-2.1c0-31.8,17.4-71.1,53.7-71.1,21.45,0,31.8,6.6,47.4,17.7l20.85-37.65C165.15-220.35,144.45-228,115.65-228c-61.8,0-96.9,55.95-96.9,113.4v2.1c0,63,34.5,115.65,98.7,115.65,37.2,0,62.1-13.8,76.5-24v-73.2H123.6v-17.1Zm96.6,68.85v-84.15h76.8v17.1h-57.9v12.6h70.5v-42.3h-102V-29.7H372.9v17.1H259.05V-212.1H370.8V-195H277.95v38.85h12.6V-182.4H383.4v-42.3H246.45V0H385.5V-42.3ZM508.8-224.7H438v12.6h70.8c60.6,0,87.9,42.75,87.9,99.6,0,60.6-35.25,99.9-90,99.9H450.6V-182.4h58.2c35.85,0,56.4,24.75,56.4,69.9,0,42.15-22.2,70.2-56.4,70.2H482.1V-165.45H469.5V-29.7h39.3c41.25,0,69-33.3,69-82.8,0-51.75-25.8-82.5-68.85-82.5H438V0h68.7c61.5,0,102.6-44.85,102.6-112.5C609.3-184.65,569.25-224.7,508.8-224.7Zm248.85,0h-44.1L663-85.65H775.8L735.6-196.2l-29.4,80.85h13.35L735.6-159.3l22.2,61.05H681L722.4-212.1h26.4L821.4-12.6H802.2L781.95-68.55H656.7L631.8,0h45.9l14.1-38.85H678.45l-9.6,26.25H649.8l15.75-43.35H773.1L793.35,0H839.4Zm254.7,166.95C997.2-47.7,981-39.15,961.5-39.15c-33.6,0-50.1-26.4-54.3-55.2H894.45c4.8,35.85,26.1,67.8,67.05,67.8,17.85,0,33-6,45.9-13.35l8.4,16.05a111.838,111.838,0,0,1-55.5,14.4c-57.45,0-85.8-48.3-85.8-100.95v-.9H906v-3.3c0-35.1,18.9-71.1,52.5-71.1,21.3,0,31.65,6.6,47.25,17.7l21-37.65C1007.1-220.35,986.25-228,957.6-228c-52.2,0-84.45,40.35-93.3,87h12.9c8.25-40.05,35.55-74.4,80.4-74.4,20.55,0,36.9,4.2,52.5,13.65l-8.7,15.6c-12.3-7.65-24-12.15-42.9-12.15-38.85,0-61.8,36.75-64.65,74.4H862.2c-.15,3-.3,6.15-.3,9.3v4.2c0,62.55,36,113.55,98.4,113.55,29.55,0,52.5-9.3,72.15-22.35Zm188.1-166.95v85.5H1086.3v-72.9h18.9v55.8h12.6v-68.4h-44.1v98.1h139.35v-85.5h18.9V-12.6h-18.9v-96.9H1073.7V0h44.1V-79.8h-12.6v67.2h-18.9V-96.9h114.15V0h44.1V-224.7Zm175.5,0V-12.6h-18.9V-224.7H1285.8v42.3h39.75V-195H1298.4v-17.1h46.05V0h44.1V-212.1h45.9V-195h-27v12.6h39.6v-42.3ZM1532.4-42.3v-84.15h76.8v17.1h-57.9v12.6h70.5v-42.3h-102V-29.7h94.95v17.1H1500.9V-212.1h111.75V-195H1519.8v38.85h12.6V-182.4h92.85v-42.3H1488.3V0h139.05V-42.3Zm282.45-35.4-86.55-147h-14.7L1827.45-31.35V-212.1h18.9V-12.6H1817.4L1692.3-224.7h-12.45V0h44.1V-109.8l-12.6-21.45V-12.6h-18.9V-199.65L1810.2,0h48.75V-224.7h-44.1ZM1966.8-182.4v-42.3h-44.1v12.6h31.5V-195h-31.5V0h44.1V-165.3h-12.6V-12.6h-18.9V-182.4Zm143.1,36.15-4.2,11.85c36,14.85,52.05,37.35,52.05,71.25,0,40.2-34.2,53.7-69.3,53.7-22.2,0-41.4-4.35-62.55-14.4l6.45-17.1c20.55,9.6,39.45,14.4,56.1,14.4,43.95,0,50.4-22.95,50.4-36.6,0-35.7-23.85-46.8-44.7-58.05-16.35-8.85-30.9-16.35-30.9-39.45,0-15.9,11.55-25.05,33.9-25.05,17.55,0,33.75,5.1,52.05,14.55l15-40.8c-18.9-9.6-32.25-16.05-67.2-16.05-41.55,0-77.85,22.8-77.85,67.5,0,34.8,17.7,59.55,49.65,73.95l4.35-11.85c-23.25-10.65-41.4-29.4-41.4-62.1,0-36.45,29.4-54.9,65.25-54.9,27.6,0,38.1,3.75,51.45,9.75l-6.3,17.25c-16.05-6.75-30.3-9.9-45-9.9-33.3,0-46.5,18.3-46.5,37.65,0,29.4,18.45,40.35,37.2,50.4,19.05,10.35,38.4,18.15,38.4,47.1,0,16.8-13.5,24-37.8,24-20.4,0-42.45-8.4-63-19.2l-15.3,40.65c22.5,12.15,46.65,20.85,78.3,20.85,47.55,0,81.9-22.5,81.9-66.3C2170.35-104.55,2149.05-130.35,2109.9-146.25Z" transform="translate(473 789)" fill="#fff"/>
                                <path id="Path_22" data-name="Path 22" d="M56.4-19.25c-5.05,3.35-10.45,6.2-16.95,6.2-11.2,0-16.7-8.8-18.1-18.4H17.1c1.6,11.95,8.7,22.6,22.35,22.6a30.558,30.558,0,0,0,15.3-4.45l2.8,5.35a37.279,37.279,0,0,1-18.5,4.8c-19.15,0-28.6-16.1-28.6-33.65v-.3h10.5v-1.1c0-11.7,6.3-23.7,17.5-23.7,7.1,0,10.55,2.2,15.75,5.9l7-12.55C54.65-73.45,47.7-76,38.15-76,20.75-76,10-62.55,7.05-47h4.3C14.1-60.35,23.2-71.8,38.15-71.8c6.85,0,12.3,1.4,17.5,4.55l-2.9,5.2a24.572,24.572,0,0,0-14.3-4.05c-12.95,0-20.6,12.25-21.55,24.8H6.35c-.05,1-.1,2.05-.1,3.1v1.4c0,20.85,12,37.85,32.8,37.85A42.017,42.017,0,0,0,63.1-6.4ZM119.1-74.9v28.5H81.05V-70.7h6.3v18.6h4.2V-74.9H76.85v32.7H123.3V-70.7h6.3V-4.2h-6.3V-36.5H76.85V0h14.7V-26.6h-4.2V-4.2h-6.3V-32.3H119.1V0h14.7V-74.9Zm60.55,0H153.8V0h14.7V-33.75L193.25,0h17.9L183.9-36.75c7.65-1.1,12.1-6.45,12.1-14.4,0-8.4-6.1-13.85-15.6-13.85H164.3v18.7h4.2V-60.8h11.9c6.95,0,11.4,3.35,11.4,9.65,0,5.6-2.65,10.55-11.9,10.55h-4.1l27,36.4h-7.4L168.65-40.6H164.3V-4.2H158V-70.7h21.65c13.7,0,22.65,7.5,22.65,19.55,0,7.5-2.9,13.35-9.2,17.05l2.45,3.35c7-3.95,10.95-11.3,10.95-20.4C206.5-64.45,196.9-74.9,179.65-74.9Zm60.3,14.1V-74.9h-14.7v4.2h10.5V-65h-10.5V0h14.7V-55.1h-4.2V-4.2h-6.3V-60.8Zm47.7,12.05-1.4,3.95c12,4.95,17.35,12.45,17.35,23.75,0,5.65-2.05,10.15-6.05,13.2-4.05,3.05-9.95,4.7-17.05,4.7a46.26,46.26,0,0,1-20.85-4.8l2.15-5.7c6.85,3.2,13.15,4.8,18.7,4.8,14.65,0,16.8-7.65,16.8-12.2,0-11.9-7.95-15.6-14.9-19.35-5.45-2.95-10.3-5.45-10.3-13.15,0-5.3,3.85-8.35,11.3-8.35,5.85,0,11.25,1.7,17.35,4.85l5-13.6C299.45-73.85,295-76,283.35-76c-13.85,0-25.95,7.6-25.95,22.5,0,11.6,5.9,19.85,16.55,24.65l1.45-3.95c-7.75-3.55-13.8-9.8-13.8-20.7,0-12.15,9.8-18.3,21.75-18.3,9.2,0,12.7,1.25,17.15,3.25l-2.1,5.75a37.749,37.749,0,0,0-15-3.3c-11.1,0-15.5,6.1-15.5,12.55,0,9.8,6.15,13.45,12.4,16.8,6.35,3.45,12.8,6.05,12.8,15.7,0,5.6-4.5,8-12.6,8-6.8,0-14.15-2.8-21-6.4L254.4-5.9c7.5,4.05,15.55,6.95,26.1,6.95,15.85,0,27.3-7.5,27.3-22.1C307.8-34.85,300.7-43.45,287.65-48.75Zm60.2-26.15V-4.2h-6.3V-74.9H317.8v14.1h13.25V-65H322v-5.7h15.35V0h14.7V-70.7h15.3V-65h-9v4.2h13.2V-74.9ZM400-14.1V-42.15h25.6v5.7H406.3v4.2h23.5v-14.1h-34V-9.9h31.65v5.7H389.5V-70.7h37.25V-65H395.8v12.95H400V-60.8h30.95V-74.9H385.3V0h46.35V-14.1Zm53.35,0V-70.7h6.3v50.9h4.2V-74.9h-14.7v65h37.2v5.7h-37.2V0h41.4V-14.1Zm66.9-46.7V-74.9h-14.7v4.2h10.5V-65h-10.5V0h14.7V-55.1h-4.2V-4.2h-6.3V-60.8Zm28.55-9.9h29.3v-4.2H544.6v14.1h29.3v37.15c0,6.25-1.5,11.45-4.35,15.05s-7,5.45-12.35,5.45c-6.8,0-11.95-1.35-16.55-4.45l2.85-5.25c4.4,2.65,8.6,4,12.4,4,5.3,0,11.7-2.55,11.7-14.7V-55.1h-4.2v31.55c0,7.25-2.3,10.5-7.5,10.5-4.8,0-9.9-2.85-13.9-5.9l-6.75,12.5c5.25,4.55,11.7,7.5,21.95,7.5,13.9,0,20.9-10.65,20.9-24.7V-65H548.8ZM624.65-39l30.9-35.9H636.7L608.6-42.15V-4.2h-6.3V-70.7h6.3v19.6l4.2-4.9V-74.9H598.1V0h14.7V-40.6l25.8-30.1h7.8L619.1-39,649.05-4.2h-8L619.1-29.9v6.5L639.15,0H658.2ZM685.3-14.1V-42.15h25.6v5.7H691.6v4.2h23.5v-14.1h-34V-9.9h31.65v5.7H674.8V-70.7h37.25V-65H681.1v12.95h4.2V-60.8h30.95V-74.9H670.6V0h46.35V-14.1ZM829-37.05v-4.2H801.35v14.1H824.8V-9.2a38.936,38.936,0,0,1-21.3,6.05c-19.65,0-28.7-16.6-28.7-34.35v-.7c0-16.55,9.75-33.6,28.1-33.6a32.253,32.253,0,0,1,17.55,4.55l-2.9,5.2A24.851,24.851,0,0,0,803.2-66.1c-14.25,0-22.1,14.3-22.1,27.9v.7c0,14.45,7,28.65,22.25,28.65a31.255,31.255,0,0,0,13-2.5l2.15-1V-21.5h-4.2v6.45a26.88,26.88,0,0,1-10.95,2c-13.3,0-18.05-13.3-18.05-24.45v-.7c0-10.6,5.8-23.7,17.9-23.7,7.15,0,10.6,2.2,15.8,5.9l6.95-12.55C819.4-73.45,812.5-76,802.9-76c-20.6,0-32.3,18.65-32.3,37.8v.7c0,21,11.5,38.55,32.9,38.55a42.917,42.917,0,0,0,25.5-8v-24.4H805.55v-5.7ZM861.2-14.1V-42.15h25.6v5.7H867.5v4.2H891v-14.1H857V-9.9h31.65v5.7H850.7V-70.7h37.25V-65H857v12.95h4.2V-60.8h30.95V-74.9H846.5V0h46.35V-14.1Zm75-60.8H910.35V0h14.7V-33.75L949.8,0h17.9L940.45-36.75c7.65-1.1,12.1-6.45,12.1-14.4,0-8.4-6.1-13.85-15.6-13.85h-16.1v18.7h4.2V-60.8h11.9c6.95,0,11.4,3.35,11.4,9.65,0,5.6-2.65,10.55-11.9,10.55h-4.1l27,36.4h-7.4L925.2-40.6h-4.35V-4.2h-6.3V-70.7H936.2c13.7,0,22.65,7.5,22.65,19.55,0,7.5-2.9,13.35-9.2,17.05l2.45,3.35c7-3.95,10.95-11.3,10.95-20.4C963.05-64.45,953.45-74.9,936.2-74.9Zm59.05,60.8V-42.15h25.6v5.7h-19.3v4.2h23.5v-14.1h-34V-9.9h31.65v5.7H984.75V-70.7H1022V-65H991.05v12.95h4.2V-60.8h30.95V-74.9H980.55V0h46.35V-14.1ZM1054.9-65v12.95h4.2V-60.8h28.85V-74.9H1044.4v32.75h32.9v5.7h-32.9V0h14.7V-26.6h-4.2V-4.2h-6.3V-32.25h32.9v-14.1h-32.9V-70.7h35.15V-65Zm85.6-9.95v4.35c13.95,4.15,20.3,18.7,20.3,33.1,0,17.1-8.9,34.35-28.7,34.35s-28.7-17.25-28.7-34.35c0-16.45,8.25-33,26.6-34.25v10a13.829,13.829,0,0,1,2.1-.15c13.3,0,18.2,13.9,18.2,24.4s-4.85,24.45-18.2,24.45S1113.9-27,1113.9-37.5c0-7.85,2.75-17.7,9.8-22.1v-4.75c-9.55,4.3-14,15.9-14,26.85,0,13.8,7,28.65,22.4,28.65s22.4-14.85,22.4-28.65c0-13.1-6.4-27.2-20.3-28.5v-9.9c-.7-.05-1.4-.1-2.1-.1-21,0-32.9,17.5-32.9,38.5s11.9,38.55,32.9,38.55S1165-16.5,1165-37.5C1165-55.5,1156.25-70.9,1140.5-74.95Zm66.6.05h-25.85V0h14.7V-33.75L1220.7,0h17.9l-27.25-36.75c7.65-1.1,12.1-6.45,12.1-14.4,0-8.4-6.1-13.85-15.6-13.85h-16.1v18.7h4.2V-60.8h11.9c6.95,0,11.4,3.35,11.4,9.65,0,5.6-2.65,10.55-11.9,10.55h-4.1l27,36.4h-7.4L1196.1-40.6h-4.35V-4.2h-6.3V-70.7h21.65c13.7,0,22.65,7.5,22.65,19.55,0,7.5-2.9,13.35-9.2,17.05l2.45,3.35c7-3.95,10.95-11.3,10.95-20.4C1233.95-64.45,1224.35-74.9,1207.1-74.9Zm102.75,0-27.7,48.5-20.2-35.15V-4.2h-6.3V-70.7h8.25l18.3,32.05,2.45-4.2-18.3-32.05h-14.9V0h14.7V-45.8l16,27.85L1312.3-70.7h8.25V-4.2h-6.3V-61.55l-25.1,43.65H1294l16.05-27.9V0h14.7V-74.9Zm49.6,60.8V-42.15h25.6v5.7h-19.3v4.2h23.5v-14.1h-34V-9.9h31.65v5.7h-37.95V-70.7h37.25V-65h-30.95v12.95h4.2V-60.8h30.95V-74.9h-45.65V0h46.35V-14.1Zm63.85,0V-42.15h25.6v5.7h-19.3v4.2h23.5v-14.1h-34V-9.9h31.65v5.7H1412.8V-70.7h37.25V-65H1419.1v12.95h4.2V-60.8h30.95V-74.9H1408.6V0h46.35V-14.1Zm75-60.8h-25.85V0h14.7V-33.75L1511.9,0h17.9l-27.25-36.75c7.65-1.1,12.1-6.45,12.1-14.4,0-8.4-6.1-13.85-15.6-13.85h-16.1v18.7h4.2V-60.8h11.9c6.95,0,11.4,3.35,11.4,9.65,0,5.6-2.65,10.55-11.9,10.55h-4.1l27,36.4h-7.4L1487.3-40.6h-4.35V-4.2h-6.3V-70.7h21.65c13.7,0,22.65,7.5,22.65,19.55,0,7.5-2.9,13.35-9.2,17.05l2.45,3.35c7-3.95,10.95-11.3,10.95-20.4C1525.15-64.45,1515.55-74.9,1498.3-74.9Zm67.95,0h-23.6v4.2h23.6c8.85,0,16.2,2.85,21.2,8.3,5.3,5.75,8.1,14.35,8.1,24.9,0,20.2-11.75,33.3-30,33.3h-18.7V-60.8h19.4c11.95,0,18.8,8.25,18.8,23.3,0,14.05-7.4,23.4-18.8,23.4h-8.9V-55.15h-4.2V-9.9h13.1c13.75,0,23-11.1,23-27.6,0-17.25-8.6-27.5-22.95-27.5h-23.65V0h22.9c20.5,0,34.2-14.95,34.2-37.5C1599.75-61.55,1586.4-74.9,1566.25-74.9Zm64.45,60.8V-42.15h25.6v5.7H1637v4.2h23.5v-14.1h-34V-9.9h31.65v5.7H1620.2V-70.7h37.25V-65H1626.5v12.95h4.2V-60.8h30.95V-74.9H1616V0h46.35V-14.1Z" transform="translate(487 496)" fill="#fff"/>
                                <path id="Path_24" data-name="Path 24" d="M109.65-117l92.7-107.7H145.8L61.5-126.45V-12.6H42.6V-212.1H61.5v58.8L74.1-168v-56.7H30V0H74.1V-121.8l77.4-90.3h23.4L93-117,182.85-12.6h-24L93-89.7v19.5L153.15,0H210.3ZM291.6-42.3v-84.15h76.8v17.1H310.5v12.6H381v-42.3H279V-29.7h94.95v17.1H260.1V-212.1H371.85V-195H279v38.85h12.6V-182.4h92.85v-42.3H247.5V0H386.55V-42.3Zm225-182.4H439.05V0h44.1V-101.25L557.4,0h53.7L529.35-110.25c22.95-3.3,36.3-19.35,36.3-43.2,0-25.2-18.3-41.55-46.8-41.55h-48.3v56.1h12.6v-43.5h35.7c20.85,0,34.2,10.05,34.2,28.95,0,16.8-7.95,31.65-35.7,31.65h-12.3l81,109.2h-22.2L483.6-121.8H470.55V-12.6h-18.9V-212.1H516.6c41.1,0,67.95,22.5,67.95,58.65,0,22.5-8.7,40.05-27.6,51.15l7.35,10.05c21-11.85,32.85-33.9,32.85-61.2C597.15-193.35,568.35-224.7,516.6-224.7ZM729.3-117,822-224.7H765.45l-84.3,98.25V-12.6h-18.9V-212.1h18.9v58.8l12.6-14.7v-56.7h-44.1V0h44.1V-121.8l77.4-90.3h23.4L712.65-117,802.5-12.6h-24L712.65-89.7v19.5L772.8,0h57.15Z" transform="translate(2662 789)" fill="#2971b9"/>
                            </g>
                        </svg>

                    </a>
                </div>
                <div class="col-md-8 d-flex justify-content-center justify-content-md-end mb-4 mb-lg-0">
                    <div class="row">
                        <div class="col-md-12 mb-3 mb-md-0">
                            <div class="ml-3 text-end">
                                <h5 class="mb-0 text-color-light">Christelijke Gereformeerde<br>Gedachteniskerk Oud-Beijerland</h5>
                                <p class="text-4 mb-0"><i class="fa fa-phone text-color-primary top-1 p-relative"></i> <span class="pl-1">(0186) 618 155</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer-copyright footer-copyright-style-2">
            <div class="container py-2">
                <div class="row py-4">
                    <div class="col-md-4 d-flex align-items-center justify-content-center justify-content-md-start mb-2 mb-lg-0">
                        <p>© Copyright {% now 'Y' %}. Alle rechten voorbehouden.</p>
                    </div>
                    <div class="col-md-8 d-flex align-items-center justify-content-center justify-content-md-end mb-4 mb-lg-0">
                        <p><i class="far fa-envelope text-color-primary top-1 p-relative"></i> <a href="mailto:<EMAIL>" class="opacity-7 pl-1"><EMAIL></a></p>
                        <ul class="footer-social-icons social-icons social-icons-clean social-icons-icon-light">
                            <li class="social-icons-instagram"><a href="https://www.instagram.com/jpt.cgk.obl/" target="_blank" title="Instagram JPT"> <i class="fab fa-instagram"></i></a></li>
                            <li class="social-icons-youtube"><a href="https://www.youtube.com/channel/UCKOTp3k7pReNx-CKZ2jLRcA" target="_blank" title="YouTube CGK Oud-Beijerland"><i class="fab fa-youtube"></i></a></li>
                            <li><a href="https://kerkdienstgemist.nl/stations/117-Chr-Ger-Gedachteniskerk---Oud-Beijerland" target="_blank" title="Kerkdienst gemist"><img width="25" height="25" src="{% static 'img/kerkdienst-gemist.png' %}"></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    </div>

    <!-- Vendor -->
    {% compress js %}
    <script src="{% static 'vendor/jquery/jquery.js' %}"></script>
    <script src="{% static 'vendor/jquery.appear/jquery.appear.min.js' %}"></script>
    <script src="{% static 'vendor/jquery.cookie/jquery.cookie.min.js' %}"></script>
    <script src="{% static 'vendor/popper/umd/popper.min.js' %}"></script>
    <script src="{% static 'vendor/bootstrap/js/bootstrap.min.js' %}"></script>
    <script src="{% static 'vendor/jquery.validation/jquery.validate.min.js' %}"></script>
    <script src="{% static 'vendor/jquery.easy-pie-chart/jquery.easypiechart.min.js' %}"></script>
    <script src="{% static 'vendor/jquery.gmap/jquery.gmap.min.js' %}"></script>
    <script src="{% static 'vendor/jquery.lazyload/jquery.lazyload.min.js' %}"></script>
    <script src="{% static 'vendor/isotope/jquery.isotope.min.js' %}"></script>
    <script src="{% static 'vendor/owl.carousel/owl.carousel.min.js' %}"></script>
    <script src="{% static 'vendor/magnific-popup/jquery.magnific-popup.min.js' %}"></script>
    <script src="{% static 'vendor/vide/jquery.vide.min.js' %}"></script>
    <script src="{% static 'vendor/vivus/vivus.min.js' %}"></script>
    <script src="{% static 'vendor/jquery.countdown/jquery.countdown.min.js' %}"></script>

    <!-- Theme Base, Components and Settings -->
    <script src="{% static 'js/theme.js' %}"></script>
    <script src="{% static 'js/theme.init.js' %}"></script>

    <!-- Current Page Vendor and Views -->
    <script src="{% static 'vendor/rs-plugin/js/jquery.themepunch.tools.min.js' %}"></script>
    <script src="{% static 'vendor/rs-plugin/js/jquery.themepunch.revolution.min.js' %}"></script>

    <!-- Demo -->
    <script src="{% static 'js/demos/demo-church.js' %}"></script>

    <!-- Theme Custom -->
    <script src="{% static 'js/custom.js' %}"></script>

    <!-- Theme Initialization Files -->
    <script src="{% static 'js/theme.init.js' %}"></script>
    <script>
        jQuery(document).ready(function(){
            jQuery.ajax({
                url:'https://dailyverses.net/get/verse?language=hsv&isdirect=1&url=' + window.location.hostname,
                dataType: 'JSONP',
                success:function(json){
                    jQuery(".dailyverse-verse").prepend(jQuery(json.html).get(0));
                    jQuery(".dailyverse-book").prepend(jQuery(json.html).get(1));
                },
                failure:function (json){
                    console.log("Error")
                }
            });
        });
    </script>
    {% endcompress %}
    </body>
    </html>
{% endblock %}
