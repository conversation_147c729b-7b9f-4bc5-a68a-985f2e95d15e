[Unit]
Description=gunicorn daemon
Requires=gunicorn.socket
After=network.target

[Service]
User=cgk
Group=www-data
WorkingDirectory=/home/<USER>/church-cms/churchcms
ExecStart=/home/<USER>/church-cms/venv/bin/gunicorn \
          --access-logfile - \
          --workers 3 \
          --bind unix:/run/gunicorn.sock \
          churchcms.wsgi:application
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
~