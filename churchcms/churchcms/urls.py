"""churchcms URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/1.11/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  url(r'^$', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  url(r'^$', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.conf.urls import url, include
    2. Add a URL to urlpatterns:  url(r'^blog/', include('blog.urls'))
"""
from django.conf.urls.static import static
from django.contrib import admin
from django.contrib.auth.decorators import login_required
from django.urls import path, include, reverse_lazy, re_path, register_converter
from cgkobl import views
from django.views.static import serve
from flatblocks.views import edit
from magicauth.settings import VALIDATE_TOKEN_URL

from magicauth import views as magicauth_views
from magicauth.urls import urlpatterns as magicauth_urls

from cgkobl.admin import AdminLoginView
from cgkobl.models import ChurchServiceFeed

from cgkobl.views import CustomValidateTokenView

from cgkobl.views import OtpLoginView, MagicLinkLoginView

from cgkobl.converters import DateConverter
from songs.views import get_collections, get_songs, edit_songs, migrate_songs, delete_songs, get_formulieren, \
    migrate_weerklank_titles

register_converter(DateConverter, 'date')

urlpatterns = []

# if settings.DEBUG:
#     urlpatterns += [
#         re_path(r'^media/posts/(?P<path>.*)$', serve, {
#             'document_root': settings.MEDIA_ROOT,
#         }),
#     ]
# else:
#     urlpatterns += [url(r'^media/posts/(?P<file>.*)$', views.serve_posts_file, name='serve_posts_file')]

urlpatterns += [
    path('', views.index, name='index'),
    path('kerkdienst/<date:service_date>/', views.church_service_detail, name='church-service-detail'),
    path('nieuws/<int:post_id>/', views.post_detail, name='post-detail'),
    path('agenda/', views.event_overview, name='event-overview'),
    path('agenda/<int:year>/<int:month>/', views.event_overview_month, name='event-overview-month'),
    path('agenda/<int:event_id>/', views.event_detail, name='event-detail'),
    path('mijn-gedachteniskerk/', views.user_home, name='user-home'),
    path('mijn-gedachteniskerk/profiel-wijzigen/', views.user_edit_profile, name='user-edit-profile'),
    path('mijn-gedachteniskerk/agenda/', views.user_event_manager, name='user-event-manager'),
    path('mijn-gedachteniskerk/agenda/events.json', views.user_events, name='user-events'),
    path('mijn-gedachteniskerk/beamer/', views.user_beamer, name='user-beamer'),
    path('mijn-gedachteniskerk/beamer/weerklank/', views.fetch_from_weerklank, name='beamer-weerklank'),
    path('mijn-gedachteniskerk/vvb/', views.user_set_vvb, name='user-set-vvb'),
    path('mijn-gedachteniskerk/vvb/overview', views.user_overview_vvb, name='user-overview-vvb'),
    path('mijn-gedachteniskerk/<str:post_type>/', views.user_files, name='user-files'),
    path('account/eerste-keer/', views.landing_page, name='landing-page'),
    re_path(r'^admin/login/?$', AdminLoginView.as_view(), name='admin-login'),
    path('admin/', admin.site.urls, name='admin'),
    path('login-otp/', OtpLoginView.as_view(), name='login-otp'),
    path('login/', MagicLinkLoginView.as_view(), name='login'),
    path("webauthn/", include("django_otp_webauthn.urls", namespace="otp_webauthn")),
    path("webauthn/revoke/<str:id>/", views.revoke_passkey, name="revoke-passkey"),
    path("webauthn/delete/<str:id>/", views.delete_passkey, name="delete-passkey"),
    path("webauthn/activate/<str:id>/", views.activate_passkey, name="activate-passkey"),
    path('account/', include('django.contrib.auth.urls')),
    path('register/', views.register, name='register'),
    path('contact/', views.contact, name='contact'),
    path('contact/bedankt/', views.contact_thanks, name='contact_thanks'),
    path('kerkdiensten/komende-kerkdiensten/', views.upcoming_services, name='upcoming_services'),
    path('kerkdiensten/archief/', views.archive_services, name='archive_services'),
    path('kerkdiensten/calendar.ics', ChurchServiceFeed(), name='services_calendar'),
    path('api/mededelingen/', views.get_announcement_slides, name='get_announcement_slides'),
    path('api/mededelingen/<int:slide_id>/', views.get_announcement_slide, name='get_announcement_slide'),
    path('api/songs/collections/', get_collections, name='get_collections'),
    path('api/songs/search/', get_songs, name='get_songs'),
    path('api/songs/edit/', edit_songs, name='edit_songs'),
    path('api/songs/migrate/', migrate_songs, name='migrate_songs'),
    path('api/songs/migrate/weerklank/', migrate_weerklank_titles, name='migrate_weerklank_titles'),
    path('api/songs/delete/', delete_songs, name='migrate_songs'),
    path('api/formulier/', get_formulieren, name='get_formulieren'),
    path('tinymce/', include('tinymce.urls')),
    re_path(r'^flatblocks/(?P<pk>\d+)/edit/$', login_required(edit), name='flatblocks-edit'),
    re_path(r'^media/posts/(?P<file>.*)$', views.serve_posts_file, name='serve_posts_file'),
    re_path(r'^media/postimg/(?P<file>.*)$', views.serve_posts_gallery_file, name='serve_posts_gallery_file'),
    re_path(r'^media/(?P<file>.*)$', views.serve_protected_document, name='serve_protected_document'),
    path('', include('qr_code.urls', namespace='qr_code')),
    path(
        VALIDATE_TOKEN_URL,
        CustomValidateTokenView.as_view(),
        name="magicauth-validate-token",
    ),
]

urlpatterns.extend(magicauth_urls)
urlpatterns += path('', include('django.contrib.flatpages.urls'), name="flatpage"),
