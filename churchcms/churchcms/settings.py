"""
Django settings for churchcms project.

Generated by 'django-admin startproject' using Django 1.11.5.

For more information on this file, see
https://docs.djangoproject.com/en/1.11/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/1.11/ref/settings/
"""

import os
from datetime import date
from importlib.resources import _

import django
from django.templatetags.static import static
from django.urls import reverse_lazy, reverse
from django.utils.encoding import smart_str


# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/1.11/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'uq8#9xm$wbcbq*cg4ee=k954l^r_9uunw98dhl7a5^+1+fm^25'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True
COMPRESS_ENABLED = True
COMPRESS_OFFLINE = False
COMPRESS_STORAGE = 'compressor.storage.GzipCompressorFileStorage'

SITE_ID = 1

ALLOWED_HOSTS = ['localhost', '0.0.0.0', '127.0.0.1', '**************', "joosts-macbook-pro.local", 'great-rabbits-divide-109-200-196-6.loca.lt']

ADMINS = [('Joost', '<EMAIL>')]
STAFF = ['<EMAIL>', '<EMAIL>']

# Application definition

INSTALLED_APPS = [
    'cgkobl',
    "corsheaders",
    'songs',
    "sslserver",
    "unfold",
    "unfold.contrib.forms",  # optional, if special form elements are needed
    "unfold.contrib.filters",  # optional, if special form elements are needed
    "django_otp_webauthn",
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.sites',
    'django.contrib.flatpages',
    'tinymce',
    'users',
    'ordered_model',
    'sitetree',
    'inbox',
    'huey.contrib.djhuey',
    'qr_code',
    'flatblocks',
    'magicauth',
    "compressor",
    "dynamic_breadcrumbs",
    "colorfield",
    "djversion",
    'django_recaptcha'
]

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'django.contrib.flatpages.middleware.FlatpageFallbackMiddleware',
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.contrib.sites.middleware.CurrentSiteMiddleware"
]

ROOT_URLCONF = 'churchcms.urls'
DATA_UPLOAD_MAX_MEMORY_SIZE = 10621440

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'cgkobl/templates/')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                "django.template.context_processors.request",
                "dynamic_breadcrumbs.context_processors.breadcrumbs",
                "djversion.context_processors.version",
            ],
            'debug': True,
        },
    },
]

DEFAULT_AUTO_FIELD = 'django.db.models.AutoField'

WSGI_APPLICATION = 'churchcms.wsgi.application'

LOGIN_REDIRECT_URL = 'user-home'
LOGIN_URL = '/login/'
LOGOUT_REDIRECT_URL = 'index'
MAGICAUTH_FROM_EMAIL = 'CGK Oud-Beijerland <<EMAIL>>'
MAGICAUTH_EMAIL_FIELD = 'email'
MAGICAUTH_WAIT_URL = 'login/code/<str:key>/'
MAGICAUTH_LOGGED_IN_REDIRECT_URL_NAME = 'user-home'
MAGICAUTH_EMAIL_HTML_TEMPLATE = 'email/login-validation.html'
MAGICAUTH_EMAIL_TEXT_TEMPLATE = 'email/login-validation.txt'
MAGICAUTH_EMAIL_SENT_URL = 'login-sent'
MAGICAUTH_EMAIL_SENT_VIEW_TEMPLATE = 'registration/login-email-sent.html'
MAGICAUTH_LOGIN_VIEW_TEMPLATE = 'registration/login.html'
MAGICAUTH_WAIT_VIEW_TEMPLATE = 'registration/validate.html'
MAGICAUTH_WAIT_SECONDS = 1
MAGICAUTH_EMAIL_SUBJECT = "Link om in te loggen"
EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"
EMAIL_HOST = 'email-smtp.eu-central-1.amazonaws.com'
EMAIL_PORT = 587
EMAIL_HOST_USER = 'AKIATQDWCT5NTJHRJSDY'
EMAIL_HOST_PASSWORD = 'BEpF/Wm4EuO8+m/2y7hl/jH+Yx5eoNceSO1VTJdHZJbH'
EMAIL_USE_TLS = True
EMAIL_FILE_PATH = os.path.join(BASE_DIR, "sent_emails")
SERVER_EMAIL = '<EMAIL>'
AUTH_USER_MODEL = 'users.User'
SITETREE_MODEL_TREE = 'cgkobl.MyTree'
SITETREE_MODEL_TREE_ITEM = 'cgkobl.MyTreeItem'
SITETREE_CLS = 'cgkobl.views.MySiteTree'
handler404 = 'cgkobl.views.handler404'

YOUTUBE_OAUTH_FILE = "/Users/<USER>/IdeaProjects/church-cms/oauth/client_secret_1030260490826-23msdp4350fb4b8akl0nh8ph0k4p38g3.apps.googleusercontent.com.json"
YOUTUBE_OAUTH_TOKEN = "/Users/<USER>/IdeaProjects/church-cms/oauth/token.json"

# Database
# https://docs.djangoproject.com/en/1.11/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': os.path.join(BASE_DIR, 'db.sqlite3'),
    }
}

HUEY = {
    'huey_class': 'huey.SqliteHuey',  # Huey implementation to use.
    'name': DATABASES['default']['NAME'],  # Use db name for huey.
    'results': True,  # Store return values of tasks.
    'store_none': False,  # If a task returns None, do not save to results.
    'immediate': False,  # If DEBUG=True, run synchronously.
    'utc': True,  # Use UTC for all times internally.
    'consumer': {
        'workers': 1,
        'worker_type': 'thread',
        'initial_delay': 0.1,  # Smallest polling interval, same as -d.
        'backoff': 1.15,  # Exponential backoff using this rate, -b.
        'max_delay': 10.0,  # Max possible polling interval, -m.
        'scheduler_interval': 1,  # Check schedule every second, -s.
        'periodic': True,  # Enable crontab feature.
        'check_worker_health': True,  # Enable worker health checks.
        'health_check_interval': 1,  # Check worker health every second.
    },
}

# Password validation
# https://docs.djangoproject.com/en/1.11/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/1.11/topics/i18n/

LANGUAGE_CODE = 'nl'

TIME_ZONE = 'Europe/Amsterdam'

USE_I18N = True

USE_L10N = False

DATE_INPUT_FORMATS = ['%Y-%m-%d', '%d-%m-%Y']
TIME_INPUT_FORMATS = ['%H:%M:%S']

USE_TZ = True

LOCALE_PATHS = [
    os.path.join(BASE_DIR, 'locale'),
]


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/1.11/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, "static")

STATICFILES_FINDERS = (
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
    'compressor.finders.CompressorFinder',
)

MEDIA_ROOT = os.path.join(BASE_DIR, 'media').replace('\\', '/')
MEDIA_URL = '/media/'

# STATICFILES_DIRS = [
#     os.path.join(BASE_DIR, "static"),
# ]

CKEDITOR_UPLOAD_PATH = "uploads/"

TINYMCE_DEFAULT_CONFIG = {
    'height': 360,
    'width': 1120,
    'content_css': "/static/css/theme-elements.css,/static/css/church.css",
    'content_style': '@import url("https://fonts.googleapis.com/css?family=Playfair+Display:400,400i,700%7CSintony:400,700");',
    'cleanup_on_startup': True,
    'custom_undo_redo_levels': 20,
    'selector': 'textarea',
    'theme': 'modern',
    'plugins': '''
            textcolor save link image media preview codesample contextmenu
            table code lists fullscreen  insertdatetime  nonbreaking
            contextmenu directionality searchreplace wordcount visualblocks
            visualchars code fullscreen autolink lists  charmap print  hr
            anchor pagebreak
            ''',
    'toolbar1': '''
            fullscreen preview bold italic underline | fontselect,
            fontsizeselect  | forecolor backcolor | alignleft alignright |
            aligncenter alignjustify | indent outdent | bullist numlist table |
            | link image media | codesample |
            ''',
    'toolbar2': '''
            visualblocks visualchars |
            charmap hr pagebreak nonbreaking anchor |  code |
            ''',
    'contextmenu': 'formats | link image',
    'menubar': True,
    'statusbar': True,
    'formats': {
        'h1': { 'block': 'h1', 'classes': 'heading' },
        'p': { 'block': 'p'},
        'p2': { 'block': 'p', 'classes': 'text-2' },
    },
    'style_formats':[
        { 'title': "Koppen", 'items': [
            { 'title': 'Kop 1', 'block': 'h1' },
            { 'color': 'Heading 2', 'title': 'Kop 2', 'format': 'h2' },
            { 'color': 'Heading 3', 'title': 'Kop 3', 'format': 'h3' },
            { 'color': 'Heading 4', 'title': 'Kop 4', 'format': 'h4' },
            { 'color': 'Heading 5', 'title': 'Kop 5', 'format': 'h5' },
            { 'color': 'Heading 6', 'title': 'Kop 6', 'format': 'h6' }
        ]},
        { 'color': 'Inline', 'title': "Inlijn", 'items': [
            { 'color': 'Bold',  'title': 'Vet', 'format': 'bold' },
            { 'color': 'Italic',  'title': 'Schuin', 'format': 'italic' },
            { 'color': 'Underline',  'title': 'Onderlijnt', 'format': 'underline' },
            { 'color': 'Strikethrough',  'title': 'Doostreept', 'format': 'strikethrough' },
            { 'color': 'Superscript',  'title': 'Superscript', 'format': 'superscript' },
            { 'color': 'Subscript',  'title': 'Subscript', 'format': 'subscript' },
            { 'color': 'Code',  'title': 'Code', 'format': 'code' }
        ]},
        { 'color': 'Blocks', 'title': 'Blokken', 'items': [
            { 'title': 'Paragraaf',  'block': 'p', 'attributes': { 'class': '' } },
            { 'title': 'Paragraaf 2',  'block': 'p', 'attributes': {'class': 'text-2'} },
            { 'color': 'Blockquote',  'title': 'Blockquote', 'format': 'blockquote' },
            { 'color': 'Div',  'title': 'Div', 'format': 'div' },
            { 'color': 'Pre',  'title': 'Pre', 'format': 'pre' }
        ]},
        { 'color': 'Align', 'title': 'Uitlijnen', 'items': [
            { 'color': 'Left', 'title': 'Links',  'format': 'alignleft' },
            { 'color': 'Center',  'title': 'Midden', 'format': 'aligncenter' },
            { 'color': 'Right',  'title': 'Rechts', 'format': 'alignright' },
            { 'color': 'Justify',  'title': 'Uitlijnen', 'format': 'alignjustify' }
        ]}
    ]
    # 'formats': [
    #     {''title'': 'Paragaaf 2', 'inline': 'p', 'classes': 'text-2'}
    # ],
}

UNFOLD = {
    "SITE_TITLE": "Gedachteniskerk",
    "SITE_HEADER": "Gedachteniskerk",
    "ENVIRONMENT": "cgkobl.admin.environment_callback",
    "SITE_ICON": {
        "light": lambda request: static("images/logo-icon.png"),  # light mode
        "dark": lambda request: static("images/logo-icon.png"),  # dark mode
    },
    "SITE_FAVICONS": [
        {
            "rel": "icon",
            "sizes": "32x32",
            "type": "image/svg+xml",
            "href": lambda request: static("img/favicon.ico"),
        },
    ],
    "SHOW_HISTORY": True, # show/hide "History" button, default: True
    "LOGIN": {
        "image": lambda request: static("img/kerk.jpg"),
        "redirect_after": lambda request: reverse_lazy("admin:index"),
    },
    "COLORS": {
        "primary": {
            "50": "250 245 255",
            "100": "243 232 255",
            "200": "233 213 255",
            "300": "216 180 254",
            "400": "192 132 252",
            "500": "168 85 247",
            "600": "0 115 204",
            "700": "126 34 206",
            "800": "107 33 168",
            "900": "88 28 135",
            "950": "59 7 100",
        },
    },
    "SIDEBAR": {
        "show_search": False,  # Search in applications and models names
        "show_all_applications": False,  # Dropdown with all applications and models
        "navigation": [
            {
                "title": _("Navigatie"),
                "separator": False,  # Top border
                "collapsible": False,  # Collapsible group of links
                "items": [
                    {
                        "title": _("Dashboard"),
                        "icon": "dashboard",  # Supported icon set: https://fonts.google.com/icons
                        "link": reverse_lazy("admin:index"),
                        "permission": lambda request: request.user.is_superuser,
                    },
                ],
            },
            {
                "title": _("Bestanden"),
                "icon": "file",
                "collapsible": True,
                "items": [
                    {
                        "title": _("Allen"),
                        "icon": "draft",
                        "link": reverse_lazy("admin:cgkobl_churchfile_changelist"),
                    },
                    {
                        "title": _("Perspectief"),
                        "icon": "book",
                        "link": reverse_lazy("admin:cgkobl_churchfileperspectief_changelist"),
                    },
                ],
            },
            {
                "title": _("Gebruikers"),
                "icon": "people",
                "badge": "cgkobl.admin.badge_callback",
                "link": reverse_lazy("admin:users_user_changelist"),
                "collapsible": True,
                "items": [
                    {
                        "title": _("Gebruikers"),
                        "icon": "person",
                        "link": reverse_lazy("admin:users_user_changelist"),
                    },
                    {
                        "title": _("Wachtend op goedkeuring"),
                        "icon": "person_check",
                        "link": reverse_lazy("admin:users_unverifieduser_changelist"),
                        "badge": "cgkobl.admin.badge_callback"
                    },
                    {
                        "title": _("Groepen"),
                        "icon": "group",
                        "link": reverse_lazy("admin:auth_group_changelist"),
                    },
                ],
            },
            {
                "title": _("Kerkdiensten"),
                "icon": "groups_2",
                "collapsible": True,
                "link": reverse_lazy("admin:cgkobl_churchservice_changelist"),
                "items": [
                    {
                        "title": _("Kerkdiensten"),
                        "icon": "groups_2",
                        "link": reverse_lazy("admin:cgkobl_churchservice_changelist"),
                    },
                    {
                        "title": _("Voorgangers"),
                        "icon": "man",
                        "link": reverse_lazy("admin:cgkobl_minister_changelist"),
                    },
                ]
            },
            {
                "title": _("Pagina's"),
                "icon": "page",
                "collapsible": True,
                "items": [
                    {
                        "title": _("Pagina's"),
                        "icon": "article",
                        "link": reverse_lazy("admin:cgkobl_myflatpage_changelist"),
                    },
                    {
                        "title": _("Blokken"),
                        "icon": "browse",
                        "link": reverse_lazy("admin:cgkobl_myflatblock_changelist"),
                    },
                    {
                        "title": _("Menu's"),
                        "icon": "menu",
                        "link": reverse_lazy("admin:cgkobl_mytree_changelist"),
                    },
                ],
            },
            {
                "title": _("Zaalbeheer"),
                "icon": "meeting_room",
                "collapsible": True,
                "items": [
                    {
                        "title": _("Activiteiten"),
                        "icon": "event",
                        "link": reverse_lazy("admin:cgkobl_event_changelist"),
                    },
                ],
            }
        ],
    },
}

django.utils.encoding.smart_text = smart_str

RECAPTCHA_PUBLIC_KEY = '6LcAYSEpAAAAACLzHQwAAltPL6qJWyNa120siro8'
RECAPTCHA_PRIVATE_KEY = '6LcAYSEpAAAAAGTfwljeq1etEWT_vj2iVB3l7rEb'

OTP_WEBAUTHN_RP_NAME = "Gedachteniskerk"
# This is necessary to bind the Passkey to a specific domain. This should be the domain of your website.
OTP_WEBAUTHN_RP_ID = "localhost"
# This is used to check the origin of the request and is used for security. It is similar to Django's CSRF_TRUSTED_ORIGINS setting.
# The origins must always be a subdomain of the RP ID or the RP ID itself.
OTP_WEBAUTHN_ALLOWED_ORIGINS = ["https://localhost:8000", "https://cgk-obl.nl", "https://gedachteniskerk.nl"]
AUTHENTICATION_BACKENDS = ["django_otp_webauthn.backends.WebAuthnBackend"]

BASIC_AUTH_USER = "beamer:1c2g3k"

CORS_ALLOW_ALL_ORIGINS = True

CORS_ALLOW_METHODS = (
    "DELETE",
    "GET",
    "OPTIONS",
    "PATCH",
    "POST",
    "PUT",
)

CSRF_TRUSTED_ORIGINS = ['https://cgk-obl.nl','http://localhost:8000']

DJVERSION_VERSION = "4.4.0"
DJVERSION_UPDATED = date.fromisoformat('2025-05-06')
