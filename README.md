# church-cms

Church CMS is designed to support church website administrators to do their managing tasks. 

## CMS features ##

- Add church services with minister management
- Add files on types
- Add news posts
- Registration of VVB
- User management
- Magic login link and OTP support

## Features ##

- Django 4.2.7
- SQLite database
- Gunicorn
- Nginx webserver


## Setup production ##

Install `python3`, `pip3`

Check-out the project on the server

Create a virtualenv inside

Install the dependencies with `pip install -r requirements.txt`

Install Nginx on the server. Create a new available site with the configuration as in `churchcms/nginx/nginx.conf`

Make sure to add the defined `server_name` to the `ALLOWED_HOSTS` inside the `settings.py` of Django

Now copy the gunicorn.socket and gunicorn.service towards `/etc/systemd/system/`.

Run both via `sudo systemctl start gunicorn.<socket/service>`

Enable both `sudo systemctl enable gunicorn.<socket/service>`

## Install ##

Do the initial installation

`python manage.py migrate`

Generate static files

`python manage.py collectstatic`

Create a superuser

`python manage.py createsuperuser`

Make sure this user is also defined in the `settings.py` `ADMINS` field

Import sitetrees to generate menu's

`python manage.py sitetree_resync_apps`

Import the default pages and siteblocks

`python manage.py pages`

## Cron jobs

### Mailboxes

To keep data up to date we have cron jobs running. One is
for the mail <NAME_EMAIL>, to get
mededelingen and liturgie inside ChurchServices.

Crontab is set as follows

`*/15 * * * * /home/<USER>/church-cms/venv/bin/python /home/<USER>/church-cms/churchcms/manage.py getmail`

### Live streams

To check on live streams we run a periodic check via Huey. This is run inside a screen on the server.

Open up a screen

`screen -S huey`

Inside this screen make sure to be inside the correct virtualenv.

`source venv/bin/activate`

Then run the following `manage.py` command

`python manage.py run_huey`

This will check every 10 minutes for a new live stream and generated thumbnails and match it to the right `ChurchService`