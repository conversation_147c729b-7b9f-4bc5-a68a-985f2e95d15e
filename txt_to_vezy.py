import json
import os
import random
import string

from PIL.ImImagePlugin import number


def run():
    f = open("/Users/<USER>/Documents/test/1773.txt", "r", encoding='iso-8859-1')
    number = None
    songs = []
    song_lyrics = """"""
    for x in f:
        # start of new Psalm
        if x.startswith("*"):
            if number is not None:
                song = {
                    "objectID": id,
                    "title": f"Psalm {number}",
                    "collection": "Psalmen OB",
                    "abbreviation": "Ps",
                    "liturgyVerses": "",
                    "fontSize": "4.0",
                    "number": str(number),
                    "lyrics": song_lyrics.lstrip("\n"),
                    "lyricstranslate": "",
                    "creator": "joost",
                    "created_at": "2024-10-28 00:33:09",
                    "updated_at": "2024-10-28 00:33:09"
                }
                songs.append(song)
                # reset lyrics
                song_lyrics = """"""
            number = x.lstrip("*").strip("\n")
            id = ''.join(random.choice(string.ascii_uppercase + string.ascii_lowercase + string.digits) for _ in range(21))
        elif number is not None:
            if x.strip(" ").rstrip("\n").isnumeric():
                song_lyrics += f"""\nVers {x.strip(" ")}"""
            elif x.lower().__contains__("voorzang"):
                song_lyrics += f"""Vers 0\n"""
            elif not x.startswith("#") and x != "---\n":
                song_lyrics += f"""{x}"""

    encoder = json.JSONEncoder()
    json_output = encoder.encode(songs)
    print(json_output)

if __name__ == '__main__':
    run()
