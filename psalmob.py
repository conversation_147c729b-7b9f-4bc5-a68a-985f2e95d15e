import json
import random
import re
import string
from json import encoder
from time import sleep

from bs4 import BeautifulSoup
from requests import Session


def fetch_song_from_bijbelbox(session, number):
    song = session.get(
        f"https://www.liturgie.nu/psalmen/{number}", headers={"User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:131.0) Gecko/20100101 Firefox/131.0"})
    soup = BeautifulSoup(song.text, "html.parser")
    paragraph = soup.find_all("div", {"class": "psalm_tekst"})[0]
    output = paragraph.contents

    lyrics = """"""
    for row in output:
        vers_nummer = row.find_all("a", {"class": "versnummer"})[0].text
        vers_tekst = row.find_all("p", {"class": "verstekst"})[0]
        if row == ' ':
            continue
        p = re.compile(r'<.*?>')
        row_text = str(vers_tekst).replace("<em>", '[em]').replace("</em>", '[/em]').replace("<br/>", "\n")
        verse_rows = p.sub('', str(row_text))
        soup_text = BeautifulSoup(verse_rows, "html.parser")
        lyrics += f"""Vers {vers_nummer}\n{soup_text.text}\n\n"""

    return lyrics.strip("\n")


if __name__ == '__main__':
    i = 10
    songs = []
    session = Session()
    while i < 151:
        print(f"Fetching Psalm OB {i}")
        lyrics = fetch_song_from_bijbelbox(session, i)
        id = x = ''.join(random.choice(string.ascii_uppercase + string.ascii_lowercase + string.digits) for _ in range(21))
        song = {
            "objectID": id,
            "title": f"Psalm {i} OB",
            "collection": "Psalmen OB",
            "abbreviation": "Ps",
            "liturgyVerses": "",
            "fontSize": "4.0",
            "number": str(i),
            "lyrics": lyrics,
            "lyricstranslate": "",
            "creator": "joost",
            "created_at": "2024-10-28 11:33:09",
            "updated_at": "2024-10-28 11:33:09"
        }
        songs.append(song)
        i = i + 1
        sleep(1)
    encoder = json.JSONEncoder()
    json_output = encoder.encode(songs)
    print(json_output)
