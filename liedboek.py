import json
import os
import random
import string

from PIL.ImImagePlugin import number


def run():
    f = open("/Users/<USER>/Documents/test/liedboek.txt", "r", encoding='iso-8859-1')
    number = None
    songs = []
    song_lyrics = """"""
    for x in f:
        # start of new song
        if x.startswith("GEZANG"):
            if number is not None:
                song = {
                    "objectID": id,
                    "title": f"Liedboek {number}",
                    "collection": "Liedboek voor de Kerken",
                    "abbreviation": "LvK",
                    "liturgyVerses": "",
                    "fontSize": "4.0",
                    "number": str(number),
                    "lyrics": song_lyrics.lstrip("\n").rstrip("\n"),
                    "lyricstranslate": "",
                    "creator": "joost",
                    "created_at": "2024-10-28 00:33:09",
                    "updated_at": "2024-10-28 00:33:09"
                }
                songs.append(song)
                # reset lyrics
                song_lyrics = """"""
            number = x.split(":")[0].lstrip("GEZANG ")
            id = ''.join(random.choice(string.ascii_uppercase + string.ascii_lowercase + string.digits) for _ in range(21))
        elif x.rstrip("\n").isnumeric():
            verse = x.rstrip("\n")
            song_lyrics += f"""\nVers {verse}\n"""
        else:
            song_lyrics += x.lstrip()

    encoder = json.JSONEncoder()
    json_output = encoder.encode(songs)
    print(json_output)

if __name__ == '__main__':
    run()
