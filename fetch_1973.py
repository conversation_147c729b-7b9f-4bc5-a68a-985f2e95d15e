import json
import random
import string
from json import encoder
from time import sleep

from bs4 import BeautifulSoup
from requests import Session


def fetch_song_from_bijbelbox(session, number):
    song = session.get(
        f"https://www.bijbelbox.nl/psalmen1973/psalm-{number}")
    soup = BeautifulSoup(song.text, "html.parser")
    paragraph = soup.find(id='result')
    output = paragraph.contents[:-1]

    lyrics = """"""
    for row in output:
        row_text = row.text.strip("\r\n")
        if row_text.__contains__("online voor gebruik op iphone"):
            break
        if row_text.strip(".").isnumeric():
            lyrics += f"""\nVers {row_text.strip('.')}\n"""
        elif row_text != '' and not row_text.__contains__("Bijbelbox"):
            lyrics += f"""{row_text}\n"""

    return lyrics.strip("\n")


if __name__ == '__main__':
    i = 10
    songs = []
    session = Session()
    while i < 151:
        print(f"Fetching Psalm NB {i}")
        lyrics = fetch_song_from_bijbelbox(session, i)
        id = x = ''.join(random.choice(string.ascii_uppercase + string.ascii_lowercase + string.digits) for _ in range(21))
        song = {
            "objectID": id,
            "title": f"Psalm {i} NB",
            "collection": "Psalmen NB",
            "abbreviation": "Ps",
            "liturgyVerses": "",
            "fontSize": "4.0",
            "number": str(i),
            "lyrics": lyrics,
            "lyricstranslate": "",
            "creator": "joost",
            "created_at": "2024-10-28 11:33:09",
            "updated_at": "2024-10-28 11:33:09"
        }
        songs.append(song)
        i = i + 1
        sleep(1)
    encoder = json.JSONEncoder()
    json_output = encoder.encode(songs)
    print(json_output)
